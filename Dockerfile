# Use Node.js LTS version
FROM node:22-alpine

# Install bash and other required dependencies
RUN apk add --no-cache bash python3 make g++

# Set working directory
WORKDIR /app
COPY package*.json ./

# Install dependencies with legacy peer deps to handle React dependencies
RUN npm ci --legacy-peer-deps

# Copy the rest of the application code
COPY . .

# Set environment variables
ENV NODE_ENV=development
ENV GENERATE_SOURCEMAP=false
ENV PORT=3000

# Expose port 3000 for React development server
EXPOSE 3000

# # Start the React development server
# CMD ["npm", "start"]
# Start with umask
CMD ["bash", "-c", "umask ${UMASK:-0002} && npm start"]