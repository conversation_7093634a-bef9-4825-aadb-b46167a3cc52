const webpack = require('webpack');

module.exports = {
  devServer: (devServerConfig, { env, paths, proxy, allowedHost }) => {
    // Override the deprecated options
    delete devServerConfig.onAfterSetupMiddleware;
    delete devServerConfig.onBeforeSetupMiddleware;
    
    // Use the new setupMiddlewares option
    devServerConfig.setupMiddlewares = (middlewares, devServer) => {
      return middlewares;
    };
    
    // Use default WebSocket settings for HMR to avoid connection errors
    // when the development server runs on a different host or port.
    // Removing the explicit configuration lets Webpack Dev Server
    // automatically determine the correct WebSocket URL.
    
    // Ensure proper host configuration
    devServerConfig.host = 'localhost';
    devServerConfig.allowedHosts = 'all';
    
    return devServerConfig;
  },
  webpack: {
    configure: (webpackConfig) => {
      // Disable ESLint plugin completely
      webpackConfig.plugins = webpackConfig.plugins.filter(
        plugin => plugin.constructor.name !== 'ESLintWebpackPlugin'
      );
      
      // Add fallbacks for Node.js modules
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        "http": require.resolve("stream-http"),
        "https": require.resolve("https-browserify"),
        "util": require.resolve("util/"),
        "zlib": require.resolve("browserify-zlib"),
        "stream": require.resolve("stream-browserify"),
        "crypto": require.resolve("crypto-browserify"),
        "assert": require.resolve("assert/"),
        "url": require.resolve("url/"),
        "buffer": require.resolve("buffer/"),
        "process": require.resolve("process/browser"),
        "process/browser": require.resolve("process/browser"),
        "process/browser.js": require.resolve("process/browser"),
      };

      // Configure module resolution
      webpackConfig.resolve.extensionAlias = {
        ".js": [".js", ".ts", ".tsx"],
        ".mjs": [".mts", ".mjs"],
      };

      // Ensure proper module resolution for ES modules
      webpackConfig.resolve.modules = [
        'node_modules',
        ...(webpackConfig.resolve.modules || [])
      ];

      // Add plugins to provide global variables
      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer'],
        }),
        new webpack.DefinePlugin({
          'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
          'process.browser': true,
          global: 'globalThis',
        }),
      ];

      // Handle ES module compatibility
      webpackConfig.module.rules.push({
        test: /\.m?js$/,
        resolve: {
          fullySpecified: false,
        },
      });

      return webpackConfig;
    },
  },
};
