# Iona AI Capabilities

## <PERSON><PERSON>
<PERSON> is the conversational assistant for the Mission X platform. She helps users manage missions, wallet credits, and their profile through a conversational interface. The assistant combines rule‑based intent handling with AI responses and integrates with platform services to fetch real data.

## Current Feature Set
- **Multilingual Chat Interface** – Supports English, Malay, and Chinese through the existing `i18n` framework.
- **Navigation Commands** – Quick access to pages like Explore, Wallet, Missions, Profile, and Settings using natural language or bang commands (e.g. `!missions`).
- **Balance Lookup** – Retrieves the user’s credit balance via `walletAPI.getBalance`.
- **Mission Suggestions** – Calls `missionApi.getMissions` to surface recommended missions.
- **Top Up Flow** – Fetches credit packages from `walletAPI.getCreditPackages` and guides the user through selecting an amount.
- **Availability Check** – Uses `availabilityAPI.getUserAvailability` to report upcoming schedule slots.
- **Order Notifications** – Listens for immediate and pre‑booked order alerts, greets the user with the requester’s name, and guides them to respond via My Clients.
- **AI Responses** – Falls back to `ai.processMessage` for general questions and contextual replies.

## Usage Examples
| User Message | Assistant Response |
|--------------|-------------------|
| "What’s my balance?" | "Your current balance is 120 credits." |
| "Find missions for me" | "Here are some missions you might like: Logo Design, Coaching Session." |
| "Top up" | "How many credits would you like to top up?" (followed by package suggestions) |
| "I’m free tomorrow?" | "You are available on 2024-05-12 from 09:00 to 12:00." |

## Service Endpoints
| Feature | Service & Endpoint |
|--------|--------------------|
| Balance Lookup | `walletService.js` → `walletAPI.getBalance()` (`GET /credits/balance`)
| Mission Suggestions | `missionApi.js` → `missionApi.getMissions()` (`GET /missions`)
| Credit Packages / Top Up | `walletService.js` → `walletAPI.getCreditPackages()` (`GET /credits/{channel}/packages`)
| Availability Check | `availabilityService.js` → `availabilityAPI.getUserAvailability()` (`GET /user/availability`)

## Integration Points
- **Wallet** – Balance queries, credit package suggestions, and navigation actions for topping up.
- **Missions** – Lists recommended missions and provides navigation to the Missions page.
- **Profile & Scheduling** – Reads availability data to answer scheduling questions.
- **Global AI Context** – User, profile, wallet, services, and availability are synchronised with `AIContext` to give the assistant rich context for replies.

## Future Enhancements
- Advanced natural language understanding powered by dedicated NLP services.
- Persistent conversation history across sessions.
- Deeper mission and talent recommendations based on user behaviour.
- Automated task creation and scheduling based on conversational cues.
- Voice interaction and accessibility improvements.

## Change Log

- **2025-08-06** – Added support for reacting to immediate and pre‑booked order notifications, including customer name extraction, incoming message indicators, and automated guidance to My Clients.

