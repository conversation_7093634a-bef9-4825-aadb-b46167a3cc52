{"common": {"profile": "Profile", "edit": "Edit Profile", "save": "Save Changes", "cancel": "Cancel", "uploadPhoto": "Upload Photo", "removePhoto": "Remove Photo", "changePhoto": "Change Photo"}, "view": {"title": "Profile", "personalInfo": "Personal Information", "bio": "Bio", "skills": "Skills", "games": "Games", "achievements": "Achievements", "reviews": "Reviews", "services": "Services", "stats": {"title": "Stats", "missions": "Missions", "completed": "Completed", "rating": "Rating", "joined": "Joined"}, "contact": "Contact", "report": "Report User", "share": "Share Profile", "loadDemoData": "Load Demo Data", "noBiography": "No biography available yet.", "verified": "Verified", "errorLoadingProfile": "Could not load profile data.", "quickSections": "Quick Sections", "bookmarkedMissions": "Bookmarked Missions", "likedPosts": "Liked Posts", "noBookmarkedMissions": "No Bookmarked Missions", "noLikedPosts": "No Liked Posts", "viewAll": "View All", "quick": {"myClients": "My Clients", "noMissions": "No Missions", "missionCount": "{{count}} Mission", "missionCount_plural": "{{count}} Missions", "myOrders": "My Orders", "pending": "{{count}} Pending", "myMissions": "My Missions", "activeCount": "{{count}} Active", "disputes": " My Disputes", "noneActive": "None Active", "activeLabel": "Active", "totalLabel": "Total", "myReviews": "My Reviews", "reviewsDesc": "View & Filter", "myGifts": "My Gifts", "giftsDesc": "View your gifts", "inviteFriends": "My Points & Referral", "referrals": "{{count}} Referrals", "voiceNote": "My Voice Note", "playing": "Playing...", "clickToPlay": "Click to play", "notRecorded": "Not recorded yet", "setAvailability": "My Availability", "manageSchedule": "Manage schedule"}, "profileDetails": {"title": "Profile Details", "experience": "Experience", "level": "Level", "xpToLevel": "{{xp}} XP to Level {{level}}", "birthday": "Birthday", "gender": "Gender", "height": "Height", "weight": "Weight", "race": "Race", "languages": "Languages", "personalities": "Personalities", "constellation": "Constellation", "totalFollowers": "Total Followers"}, "inviteCard": {"description": "Share your referral code and earn 500 credits for each friend who joins!", "yourCode": "Your Referral Code", "copy": "Copy Referral Code", "rewardPerReferral": "Reward per referral", "whenFriendsSignUp": "When friends sign up", "credits": "Credits"}}, "edit": {"title": "Edit Profile", "sections": {"personal": "Personal Information", "bio": "Bio & Description", "skills": "Skills & Expertise", "games": "Games & Platforms", "services": "Services & Pricing", "social": "Social Media", "basicInfo": "Basic Info", "media": "Media", "preferences": "Preferences"}, "fields": {"name": "Full Name", "username": "Username", "email": "Email", "phone": "Phone Number", "country": "Country", "language": "Language", "timezone": "Timezone", "bio": "Bio", "shortBio": "Short Bio", "skills": "Skills", "addSkill": "<PERSON><PERSON>", "games": "Games", "addGame": "Add Game", "platforms": "Platforms", "addPlatform": "Add Platform", "services": "Services", "addService": "Add Service", "pricing": "Pricing", "availability": "Availability", "social": {"discord": "Discord", "twitter": "Twitter", "twitch": "Twitch", "youtube": "YouTube", "instagram": "Instagram"}}, "placeholders": {"name": "Enter your full name", "username": "Choose a username", "email": "Enter your email", "phone": "Enter your phone number", "bio": "Tell others about yourself...", "shortBio": "Brief introduction (150 characters max)", "addSkill": "Type to add a skill", "addGame": "Search for a game", "addPlatform": "Select platforms", "serviceName": "Service name", "serviceDescription": "Describe your service", "price": "Set your price"}, "success": "Profile updated successfully!", "error": "Failed to update profile"}, "setup": {"title": "Profile Setup", "subtitle": "Let's set up your profile", "steps": {"personal": "Personal Info", "bio": "Bio", "personality": "Personality", "services": "Services", "complete": "Complete"}, "personalInfo": {"title": "Personal Information", "subtitle": "Tell us about yourself", "fields": {"name": "Full Name", "username": "Username", "country": "Country", "language": "Language"}}, "bio": {"title": "Your Bio", "subtitle": "Tell others about yourself", "fields": {"bio": "Bio", "shortBio": "Short Bio"}}, "personality": {"title": "Your Personality", "subtitle": "Select traits that describe you", "traits": {"friendly": "Friendly", "competitive": "Competitive", "strategic": "Strategic", "teamPlayer": "Team Player", "creative": "Creative", "patient": "Patient", "leader": "Leader", "adaptable": "Adaptable", "communicative": "Communicative", "analytical": "Analytical"}}, "services": {"title": "Your Services", "subtitle": "What services do you offer?", "fields": {"serviceName": "Service Name", "description": "Description", "price": "Price", "addService": "Add Service"}}, "complete": {"title": "Setup Complete!", "subtitle": "Your profile is ready to go", "message": "You've successfully set up your profile. You can now start exploring missions and connecting with other gamers.", "viewProfile": "View Profile", "exploreMissions": "Explore Missions"}, "buttons": {"back": "Back", "next": "Next", "skip": "<PERSON><PERSON>", "finish": "Finish"}}, "modals": {"myClients": {"title": "My Clients", "description": "Manage your service orders and client interactions", "tabs": {"toAccept": "To Accept", "accepted": "Accepted", "inProgress": "In Progress", "rejected": "Rejected", "cancelled": "Cancelled", "completed": "Completed", "expired": "Expired"}, "loading": "Loading orders...", "empty": "No {{status}} orders", "emptyDesc": "You don't have any {{status}} orders at the moment.", "acceptOffer": "Accept Offer", "rejectOffer": "Reject", "accepting": "Accepting...", "rejecting": "Rejecting...", "viewAcceptedOrders": "View Accepted Orders", "continue": "Continue", "dateTime": "Date & Time", "creditsToReceive": "Credits to Receive", "earnings": "Earnings:", "whatNext": "What happens next?", "steps": {"notified": "Client has been notified of your acceptance", "creditsHeld": "Credits are held securely until completion", "startService": "You can now start the service"}, "notSet": "Not set", "unknownService": "Unknown Service", "orderAccepted": "Order Accepted! 🎉", "credits": "Credits", "acceptOrderFailed": "Failed to accept order. Please try again.", "rejectOrderFailed": "Failed to reject order. Please try again.", "startOrderSuccess": "Order started successfully.", "startOrderFailed": "Failed to start order. Please try again.", "congratulations": "Congratulations! 🎉", "acceptedOrderSubtitle": "You've successfully accepted an order!", "acceptedSuccess": "Accepted!", "acceptedAt": "Accepted:", "completedAt": "Completed:", "startedAt": "Started:", "endedAt": "Ended:", "starting": "Starting...", "startNow": "I am Online Now, Let's Start", "review": "Review", "dispute": "Dispute", "orderType": "Order Type", "qty": "Qty", "orderNumber": "Order #{{id}}", "status": {"pending": "Pending/To Accept", "accepted": "Accepted", "in_progress": "In Progress", "rejected": "Rejected", "cancelled": "Cancelled", "completed": "Completed", "expired": "Expired"}}, "myOrders": {"title": "My Orders", "description": "View and manage your service orders", "tabs": {"toaccept": "To Accept", "accepted": "Accepted", "rejected": "Rejected", "inprogress": "In Progress", "completed": "Completed", "expired": "Expired", "cancelled": "Cancelled"}, "loading": "Loading orders...", "empty": "No {{status}} orders", "emptyDesc": "You don't have any {{status}} orders at the moment.", "notSet": "Not set", "errorLoading": "Failed to load orders", "tryAgainLater": "Please try again later.", "badgeAccepted": "✓ Accepted", "serviceFallback": "Service", "orderInfo": "Order #{{id}} • {{customer}}", "status": {"pending": "Pending", "accepted": "Accepted", "rejected": "Rejected", "in_progress": "In Progress", "completed": "Completed", "expired": "Expired", "cancelled": "Cancelled"}, "dateTime": "Date & Time", "credits": "Credits", "orderType": "Order Type", "types": {"orders": "Immediate Order", "scheduled-orders": "Scheduled Order"}, "sessionTime": "Session Time", "idLabel": "ID", "remarks": "Remarks:", "actions": {"review": "Review", "cancel": "Cancel Order", "cancelling": "Cancelling...", "complete": "End Order, Wrap Up", "completing": "Wrapping Up...", "dispute": "Dispute"}, "toast": {"completeTitle": "Order Completed", "completeDescription": "Order #{{id}} marked as completed", "completeError": "Failed to mark order as completed", "cancelTitle": "Order Cancelled", "cancelDescription": "Order #{{id}} cancelled successfully", "cancelError": "Failed to cancel order"}, "confirmCancel": "Are you sure you want to cancel this order?", "enterCancelReason": "Please enter your cancellation reason:"}, "myReviews": {"title": "My Reviews", "description": "View and manage reviews you've written for others", "loading": "Loading your reviews...", "empty": "No reviews yet", "emptyDesc": "Complete orders and missions to start receiving reviews!", "retry": "Retry"}, "availability": {"title": "Availability", "available": "Available", "notAvailable": "Not Available", "noData": "No availability data found.", "remarks": "Remarks"}, "disputes": {"title": "Dispute Management", "description": "View and manage your order disputes", "status": {"all": "All", "submitted": "Submitted", "in_review": "In Review", "resolved": "Resolved", "rejected": "Rejected"}, "loading": "Loading disputes...", "noDisputes": "No disputes found", "noDisputesDesc": "You haven't created any disputes yet.", "noDisputesStatus": "No {{status}} disputes", "noDisputesStatusDesc": "Try changing the filter to see other disputes.", "retry": "Retry", "details": "Dispute Details", "statusMessages": {"submitted": "Your dispute has been submitted and is awaiting review.", "in_review": "Your dispute is currently under review.", "resolved": "Your dispute has been resolved."}}, "serviceSelection": {"steps": {"selectService": "Select Service", "schedule": "Schedule", "configure": "Configure", "confirm": "Confirm"}, "titles": {"step1": "Select Service Tier", "step2": "Choose Order Type", "step3": "Configure Your Order", "step4": "Confirm Your Order", "selectDateTime": "Select Date & Time", "walletBalance": "Wallet Balance", "totalCost": "Total Cost", "orderSummary": "Order Summary", "orderDetails": "Order Details"}, "buttons": {"select": "Select", "selected": "Selected", "chooseOrderNow": "Choose Order Now", "chooseScheduleLater": "<PERSON><PERSON> Schedule Later", "change": "Change", "pasteFromClipboard": "Paste from clipboard", "processingOrder": "Processing Order...", "processing": "Processing your order...", "validating": "Validating...", "placeOrder": "Place Order", "done": "Done", "dismiss": "<PERSON><PERSON><PERSON>", "messageTalent": "Message Talent", "back": "Back", "continue": "Continue"}, "messages": {"noTiers": "No Service Tiers Available", "noTiersDesc": "This talent hasn't set up any service tiers yet.", "selectTier": "Select a service tier", "selectTierDesc": "Please choose a service tier to continue with your order.", "tierSelected": "Service tier selected", "orderTypeSelected": "Order type selected", "ordersCount": "{{count}} person ordered this service", "ordersCount_plural": "{{count}} people ordered this service", "immediateService": "Immediate service", "scheduledService": "Scheduled service", "selectDateTime": "Select a date and time", "chooseWhen": "Please choose when you'd like your service to be scheduled.", "insufficientBalance": "Insufficient balance", "addCredits": "Please add more credits to your wallet to complete this order.", "insufficientBalanceNeed": "Insufficient balance. You need {{amount}} more credits.", "creditsAvailable": "{{balance}} credits available", "sufficientBalance": "Sufficient balance available", "remainingAfterPurchase": "Remaining after purchase:", "pullToRefresh": "Pull to refresh wallet", "pricePerUnit": "Price per unit:", "quantityLabel": "Quantity:", "totalAmount": "Total Amount:", "quantity": "Quantity", "orEnter": "Or enter:", "remarks": "Remarks (Optional)", "remarksPlaceholder": "Add any special instructions, requirements, or notes for the talent...", "remarksTip": "💡 Tip: Include specific details like game mode, rank requirements, or special requests to help the talent deliver exactly what you need.", "orderPlaced": "Order Placed Successfully!", "orderPlacedDesc": "Your order has been created and is waiting for the talent to respond.\nYou'll receive notifications about your order status.", "serviceLabel": "Service:", "orderType": "Order Type:", "immediate": "Immediate", "scheduled": "Scheduled", "scheduledFor": "Scheduled for:", "status": "Status:", "pendingResponse": "Pending Response", "nextSteps": "What happens next?", "nextStepReview": "The talent will review your order and respond within {{time}}", "nextStepNotifications": "You'll receive notifications about order status updates", "nextStepTrack": "Track your order progress in your dashboard"}, "errors": {"selectTier": "Please select a service tier", "chooseOrderType": "Please choose an order type", "selectSchedule": "Please select a date and time for scheduling", "futureDate": "Please select a future date and time", "quantityMin": "Quantity must be at least 1", "quantityMax": "Quantity cannot exceed 99", "remarksMax": "Remarks cannot exceed 500 characters", "unexpected": "An unexpected error occurred. Please try again.", "talentIdMissing": "Talent ID is missing. Please contact support.", "serviceStyleMissing": "Service style is missing. Please select a service tier.", "pricingOptionMissing": "Pricing option is missing or invalid. Please select a valid service tier.", "orderSubmission": "Order Submission Error", "walletError": "<PERSON><PERSON>", "error": "Error", "fixIssues": "Please fix the following issues:", "processing": "Please wait while we process your payment and create your order.", "checkingSelections": "Checking all your selections...", "validatingSelections": "Validating your selections...", "loading": "Loading...", "pleaseWait": "Please wait...", "orderErrorTitle": "Order Error"}}}, "availabilityPage": {"title": "Availability", "description": "Manage when you're available for bookings", "status": {"available": "Available", "unavailable": "Unavailable"}, "setOverride": "Set Override", "tabs": {"calendar": "Calendar View", "manage": "Manage Settings"}, "error": "Failed to load user data", "tryAgain": "Try Again", "goBack": "Go Back"}}