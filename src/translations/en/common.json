{"buttons": {"submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "continue": "Continue"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters", "passwordMatch": "Passwords do not match"}, "notifications": {"ariaLabel": "View 3 notifications"}, "loading": "Loading...", "error": {"general": "Something went wrong. Please try again.", "notFound": "The requested resource was not found.", "network": "Network error. Please check your connection."}, "empty": {"noResults": "No results found", "noData": "No data available"}, "time": {"justNow": "Just now", "minutesAgo": "{{count}} minute ago", "minutesAgo_plural": "{{count}} minutes ago", "hoursAgo": "{{count}} hour ago", "hoursAgo_plural": "{{count}} hours ago", "daysAgo": "{{count}} day ago", "daysAgo_plural": "{{count}} days ago", "today": "Today", "yesterday": "Yesterday"}, "currency": {"credits": "Credits"}, "navigation": {"home": "Home", "missions": "Missions", "talent": "Talent", "explore": "Explore", "chat": "Cha<PERSON>", "profile": "Profile", "settings": "Settings", "loginSignup": "Login/Signup", "becomeTalent": "Become a Talent", "topUp": "Top Up Credits"}, "wallet": {"ariaLabel": "View wallet with balance of {{balance}} credits", "creditsBalance": "Credits Balance"}, "filterModal": {"noOptions": "No options available.", "title": "Filter Talents", "subtitle": "Refine your search", "serviceCategory": "Service Category", "race": "Race", "selected": "Selected", "minimumRating": "Minimum Rating", "priceRange": "Price Range (Credits)", "currentRange": "Current Range", "range": "{{count}} {{unit}} range", "minimum": "Minimum", "maximum": "Maximum", "quickPresets": "Quick Presets", "budget": "Budget", "midRange": "Mid-Range", "premium": "Premium", "luxury": "Luxury", "resetFullRange": "Reset to Full Range", "experienceLevel": "Experience Level", "currentLevelRange": "Current Level Range", "levelsAvailable": "{{count}} levels available", "minimumLevel": "Minimum Level", "maximumLevel": "Maximum Level", "quickLevelPresets": "Quick Level Presets", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "expert": "Expert", "levelInfo": {"beginner": "Beginner (LV 1-25):", "beginnerDesc": "New players, learning phase", "intermediate": "Intermediate (LV 25-50):", "intermediateDesc": "Skilled players, good experience", "advanced": "Advanced (LV 50-75):", "advancedDesc": "Veteran players, high skill", "expert": "Expert (LV 75-99):", "expertDesc": "Elite players, master level"}, "serviceType": "Service Type", "serviceStyle": "Service Style", "gender": "Gender", "genderOptions": {"male": "Male", "female": "Female"}, "language": "Language", "failedToLoad": "Failed to load {{item}} options. Please try again.", "noOptionsAvailable": "No {{item}} options available. Please try again later.", "filtersApplied": "{{count}} filter applied", "filtersApplied_plural": "{{count}} filters applied", "resetAll": "Reset All", "applyFilters": "Apply Filters"}, "clear": "Clear"}