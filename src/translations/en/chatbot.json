{"greeting": {"initial": "Hi, I'm <PERSON>, your Mission X assistant. I can help you navigate the app. Type !help for a list of things I can do.", "welcome": "Hi! I'm <PERSON>. How can I help you today?"}, "tooltip": {"title": "<PERSON>, Your MissionX Assistant", "message": "Hi! I'm <PERSON>, your Mission X assistant. Ask me anything if you're confused!"}, "placeholder": "Ask Iona anything...", "ariaSend": "Send message", "toggle": {"label": "Toggle <PERSON>", "open": "Open Chatbot", "close": "Close Chatbot", "closeLabel": "Close Chatbot"}, "header": {"assistant": "Mission X Assistant", "status": "Online 24/7"}, "quickSuggestions": {"checkBalance": "Check my balance", "findMissions": "Find missions", "setAvailability": "Set availability", "showAnalytics": "Show analytics", "topUpCredits": "Top up credits"}, "commands": {"help": "!help", "balance": "!balance", "find": "!find", "post": "!post", "missions": "!missions", "settings": "Settings"}, "basic": {"greeting": "Hello there! How can I help you today?", "thanks": "You're welcome! Let me know if there's anything else.", "post": "Ready to share something? Use the Explore page to post images or videos.", "missions": "You can view all your active and completed missions on your Missions page.", "balance": "You can check your credit balance and transaction history on your Wallet page.", "findTalent": "Looking for talent? You can browse all creators and coaches on the Talent page via the Navigation Bar.", "talentChecklistIntro": "Here's your talent checklist:", "availability": "You can set your availability from your Profile page.", "service": "Add or edit services in the Services tab of your profile.", "help": "Here are some things you can ask me:\n- !balance to check your wallet balance\n- !find to look for talent\n- !post to post images or videos to the Explore page\n- !missions to view your missions\n- explore, clients, orders, or talent to navigate pages\n- Set Availability to manage your schedule\n- Add Services to update your offerings\n- My Clients or My Orders to manage your work\n- Create Mission to start a new mission\n- Settings to manage your account\n- Top Up to purchase credits.", "fallback1": "I'm not sure about that one. Maybe try asking about posting or finding missions?", "fallback2": "Hmm, that doesn't ring a bell. Need help with your balance or missions?", "fallback3": "I might have missed that. You can ask about exploring gigs or checking your wallet."}, "checklist": {"profilePicture": "Profile Picture", "coverMedia": "Cover Media (all)", "biography": "Biography", "approvedService": "Approved Service (at least one)"}, "talent": {"allDone": "You are officially a talent!"}, "navigation": {"explore": "Taking you to the Explore page where you can create posts and explore content!", "availability": "Opening your Availability settings...", "services": "Opening Services editor...", "missions": "Opening your Missions dashboard...", "clients": "Showing your Clients...", "orders": "Opening Orders page...", "settings": "Opening Settings...", "topup": "Opening Top Up...", "createMission": "Let's set up a new mission!", "talent": "Taking you to the Talent page..."}, "errors": {"processing": "I'm having trouble processing that request. Please try again."}, "typeLabels": {"data": "Data", "contextual": "Smart Insight", "help": "Help", "error": "Error", "greeting": "Greeting", "empty": "Suggestion", "default": "Assistant"}, "ai": {"balance": "Your current balance is {{amount}} credits.", "missions": {"found": "Here are some missions you might like: {{titles}}.", "none": "I couldn't find any recommended missions right now."}, "topup": {"askAmount": "How many credits would you like to top up?", "confirm": "Got it! You can top up {{amount}} credits from your wallet."}, "availability": {"slot": "You are available on {{date}} from {{start}} to {{end}}.", "none": "You have no availability on {{date}}."}}, "notifications": {"prebookedRequest": "Hi {{nickname}}, {{customerName}} has requested to book your service in advance. Please access My Clients via Profile to respond to this order, or type \"My Clients\" and I will take you to your clients hub.", "newOrder": "Hi {{nickname}}, {{customerName}} has placed an immediate order for your service. Please access My Clients via Profile to respond to this order, or type \"My Clients\" and I will take you to your clients hub."}}