import { useQuery, useQueryClient } from '@tanstack/react-query';
import { homepageApi } from '../../../services/homepageApi';
import { transformHomepageData } from '../../../utils/homepageDataTransformers';
import { homepageKeys } from '../../../queryKeys/homepageKeys';

export const useHomepageQuery = (options = {}) => {
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: homepageKeys.data(),
    queryFn: async () => {
      const response = await homepageApi.getAllHomepageData();
      return transformHomepageData(response);
    },
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });
};

export const prefetchHomepage = async (queryClient, options = {}) => {
  await queryClient.prefetchQuery({
    queryKey: homepageKeys.data(),
    queryFn: async () => {
      const response = await homepageApi.getAllHomepageData();
      return transformHomepageData(response);
    },
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });
};
