export const getSystemPrefersDark = () => typeof window !== 'undefined' && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
export const getStoredTheme = () => typeof window !== 'undefined' ? localStorage.getItem('theme') : null;
export const setStoredTheme = (theme) => typeof window !== 'undefined' && localStorage.setItem('theme', theme);
export const applyThemeClass = (theme) => {
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }
};

export const initializeDarkMode = () => {
  const stored = getStoredTheme();
  const theme = stored || (getSystemPrefersDark() ? 'dark' : 'light');
  applyThemeClass(theme);
};

import { useState, useEffect } from 'react';
export default function useDarkMode() {
  const [isDark, setIsDark] = useState(() => {
    const stored = getStoredTheme();
    if (stored) return stored === 'dark';
    return getSystemPrefersDark();
  });

  useEffect(() => {
    applyThemeClass(isDark ? 'dark' : 'light');
    setStoredTheme(isDark ? 'dark' : 'light');
  }, [isDark]);

  useEffect(() => {
    const stored = getStoredTheme();
    if (!stored) {
      const prefersDark = getSystemPrefersDark();
      setIsDark(prefersDark);
      applyThemeClass(prefersDark ? 'dark' : 'light');
    }
    if (typeof window !== 'undefined') {
      const mq = window.matchMedia('(prefers-color-scheme: dark)');
      const handler = (e) => {
        if (!getStoredTheme()) {
          setIsDark(e.matches);
          applyThemeClass(e.matches ? 'dark' : 'light');
        }
      };
      mq.addEventListener('change', handler);
      return () => mq.removeEventListener('change', handler);
    }
  }, []);

  const toggle = () => setIsDark((d) => !d);
  return [isDark, toggle];
}
