import { useInfiniteQuery } from '@tanstack/react-query';
import socialPostService from '../../services/socialPostService';
import { exploreKeys } from '../../queryKeys/exploreKeys';

export const useExploreFeed = (feedType) => {
  return useInfiniteQuery({
    queryKey: exploreKeys.feed(feedType, {}),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await socialPostService.getFeed(feedType, pageParam, 12);
      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch posts');
      }
      return {
        posts: response.posts,
        has_more: response.has_more,
        next_page: response.next_page,
        meta: {
          current_page: response.current_page,
          last_page: response.last_page,
          total: response.total
        }
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.has_more ? lastPage.next_page : undefined;
    },
    select: (data) => ({
      posts: data.pages.flatMap((page) => page.posts || []),
      hasMore: data.pages[data.pages.length - 1]?.has_more || false,
      meta: data.pages[data.pages.length - 1]?.meta
    }),
    staleTime: 1000 * 60 * 1, // 1 minute stale time for feed
    cacheTime: 1000 * 60 * 5, // 5 minutes cache time
    retry: 3,
    // Poll periodically to keep the feed fresh and refetch when the
    // browser window regains focus so users see new posts without needing
    // to manually refresh.
    refetchInterval: 30000,
    refetchOnWindowFocus: true,
  });
};