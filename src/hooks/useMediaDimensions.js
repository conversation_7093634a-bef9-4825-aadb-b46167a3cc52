import { useState, useEffect } from 'react';

/**
 * Hook to get media dimensions for images and videos.
 * Falls back to 1x1 when dimensions cannot be determined.
 * @param {Object} media - Media object with url and type fields
 * @returns {{width: number, height: number}}
 */
const useMediaDimensions = (media) => {
  const [dims, setDims] = useState(() => {
    if (media?.dimensions?.width && media?.dimensions?.height) {
      return {
        width: media.dimensions.width,
        height: media.dimensions.height,
      };
    }
    return { width: 1, height: 1 };
  });

  useEffect(() => {
    if (!media || !media.url) return;

    if (media.dimensions?.width && media.dimensions?.height) {
      setDims({ width: media.dimensions.width, height: media.dimensions.height });
      return;
    }

    if (typeof window !== 'undefined') {
      if (media.type && (media.type.startsWith('image') || media.type === 'image')) {
        const img = new window.Image();
        img.onload = () => {
          setDims({ width: img.naturalWidth, height: img.naturalHeight });
        };
        img.src = media.url;
      } else if (media.type && (media.type.startsWith('video') || media.type === 'video')) {
        const video = document.createElement('video');
        video.preload = 'metadata';
        video.onloadedmetadata = () => {
          setDims({ width: video.videoWidth, height: video.videoHeight });
        };
        video.src = media.url;
      }
    }
  }, [media]);

  return dims;
};

export default useMediaDimensions;
