import React from 'react';
import importWithRetry from './utils/importWithRetry';

// Lazy load page components and prefetch important bundles
export const LazyHome = React.lazy(() =>
  importWithRetry(() => import(/* webpackPrefetch: true */ './components/Home'))
);
// Allow manual preloading of the home page
LazyHome.preload = () => importWithRetry(() => import('./components/Home'));
export const LazyAllGames = React.lazy(() => importWithRetry(() => import('./pages/AllGames')));
export const LazyTalentProfile = React.lazy(() => importWithRetry(() => import('./components/TalentProfile')));
export const LazyOrderManagement = React.lazy(() => importWithRetry(() => import('./components/orders/OrderManagement')));
export const LazyOrderDetails = React.lazy(() => importWithRetry(() => import('./components/orders/OrderDetails')));
export const LazyProfile = React.lazy(() =>
  importWithRetry(() => import(/* webpackPrefetch: true */ './components/Profile'))
);
LazyProfile.preload = () => importWithRetry(() => import('./components/Profile'));
// Prefetch chat page bundle to reduce navigation latency
export const LazyChat = React.lazy(() =>
  importWithRetry(() => import(/* webpackPrefetch: true */ './pages/Chat'))
);
// Allow manual preloading
LazyChat.preload = () => importWithRetry(() => import('./pages/Chat'));
export const LazyProfileSetup = React.lazy(() => importWithRetry(() => import('./components/ProfileSetup')));
// Prefetch talent page bundle to reduce navigation latency
export const LazyTalent = React.lazy(() =>
  importWithRetry(() => import(/* webpackPrefetch: true */ './pages/TalentPage'))
);
// Allow manual preloading
LazyTalent.preload = () => importWithRetry(() => import('./pages/TalentPage'));
// Prefetch explore page bundle
export const LazyExplore = React.lazy(() =>
  importWithRetry(() => import(/* webpackPrefetch: true */ './pages/Explore'))
);
LazyExplore.preload = () => importWithRetry(() => import('./pages/Explore'));
export const LazyWallet = React.lazy(() => importWithRetry(() => import('./pages/Wallet')));
export const LazyPaymentReturn = React.lazy(() => importWithRetry(() => import('./pages/PaymentReturn')));
export const LazyPaymentReturnPage = React.lazy(() => importWithRetry(() => import('./pages/PaymentReturnPage')));
export const LazyBankAccountsPage = React.lazy(() => importWithRetry(() => import('./pages/BankAccountsPage')));
export const LazyMissionPage = React.lazy(() => importWithRetry(() => import('./pages/MissionPage')));
export const LazyMissionDetailPage = React.lazy(() => importWithRetry(() => import('./pages/MissionDetailPage')));
export const LazyMyMissionsPage = React.lazy(() => importWithRetry(() => import('./pages/MyMissionsPage')));
export const LazyMissionApplicantsPage = React.lazy(() => importWithRetry(() => import('./pages/MissionApplicantsPage')));
export const LazyMissionCreatePage = React.lazy(() => importWithRetry(() => import('./pages/MissionCreatePage')));
export const LazyMissionEditPage = React.lazy(() => importWithRetry(() => import('./pages/MissionEditPage')));
export const LazyMissionExecutionPage = React.lazy(() => importWithRetry(() => import('./pages/MissionExecutionPage')));

// Add missing components
export const LazyAvailabilityPage = React.lazy(() => importWithRetry(() => import('./pages/AvailabilityPage')));
export const LazyMissionDetailPageHost = React.lazy(() => importWithRetry(() => import('./pages/MissionDetailPageHost')));
