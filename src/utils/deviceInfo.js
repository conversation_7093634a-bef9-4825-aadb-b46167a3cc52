import { ClientJS } from 'clientjs';
import DeviceDetector from 'device-detector-js';

export async function getDeviceInfo() {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return {
      userAgent: '',
      appVersion: '1.0.0',
      hardwareConcurrency: '',
      deviceMemory: '',
      screenOrientation: '',
      pixelRatio: '',
      deviceType: 'unknown',
      browserName: 'unknown',
      osName: 'unknown'
    };
  }

  const userAgent = navigator.userAgent || '';
  const appVersion = '1.0.0';

  const hardwareConcurrency = navigator.hardwareConcurrency || '';
  const deviceMemory = navigator.deviceMemory || '';
  const screenOrientation =
    (window.screen && window.screen.orientation && window.screen.orientation.type) || '';
  const pixelRatio = window.devicePixelRatio || '';

  console.log('[DeviceInfo] hardwareConcurrency:', hardwareConcurrency);
  console.log('[DeviceInfo] deviceMemory:', deviceMemory);
  console.log('[DeviceInfo] screenOrientation:', screenOrientation);
  console.log('[DeviceInfo] pixelRatio:', pixelRatio);

  let deviceId = null;
  try {
    deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      const client = new ClientJS();
      const fingerprintSource = [
        client.getBrowser(),
        client.getBrowserVersion(),
        client.getOS(),
        client.getOSVersion(),
        client.getDevice(),
        client.getDeviceType(),
        client.getCPU(),
        client.getScreenPrint(),
        client.getPlugins(),
        client.getFonts(),
        client.getTimeZone(),
        client.getLanguage(),
        hardwareConcurrency,
        deviceMemory,
        screenOrientation,
        pixelRatio
      ].join('||');

      console.log('[DeviceInfo] ClientJS custom fingerprint source:', fingerprintSource);

      deviceId = client.getCustomFingerprint(fingerprintSource);
      if (deviceId) {
        console.log('[DeviceInfo] Generated deviceId via ClientJS custom method:', deviceId);
        localStorage.setItem('deviceId', deviceId);
      } else {
        console.warn('[DeviceInfo] ClientJS custom fingerprint did not return a deviceId');
      }
    } else if (process.env.NODE_ENV === 'development') {
      console.log('[DeviceInfo] Using stored deviceId:', deviceId);
    }
  } catch (err) {
    console.error('[DeviceInfo] Failed to generate deviceId with ClientJS:', err);
    deviceId = null;
  }

  const uaData = navigator.userAgentData || {};
  if (uaData && Object.keys(uaData).length) {
    console.log('[DeviceInfo] navigator.userAgentData available:', uaData);
  } else {
    console.log('[DeviceInfo] navigator.userAgentData not available, relying on userAgent');
  }

  const deviceDetector = new DeviceDetector();
  const parsed = deviceDetector.parse(userAgent);
  console.log('[DeviceInfo] device-detector-js parsed data:', parsed);

  const usedDeviceDetector = Boolean(
    (parsed.device && (parsed.device.model || parsed.device.type)) || parsed.os
  );
  if (usedDeviceDetector) {
    console.log('[DeviceInfo] Parsed device info via device-detector-js');
  } else {
    console.log('[DeviceInfo] device-detector-js returned no result, falling back to user agent');
  }

  let deviceModel = parsed.device?.model || uaData.model || '';
  let osVersion = parsed.os ? `${parsed.os.name} ${parsed.os.version}` : uaData.platformVersion || '';
  let deviceType = parsed.device?.type || (typeof uaData.mobile === 'boolean' ? (uaData.mobile ? 'mobile' : 'desktop') : '');
  let browserType = parsed.client?.name || (uaData.brands && uaData.brands[0]?.brand) || '';

  if (!browserType) {
    const browserMatch = userAgent.match(/(Firefox|Chrome|Safari|Edge|OPR|Opera)/i);
    if (browserMatch) {
      browserType = browserMatch[1].replace('OPR', 'Opera');
    }
  }

  if (!deviceModel) {
    const match = userAgent.match(/\(([^)]+)\)/);
    if (match && match[1]) {
      const parts = match[1].split(';').map(p => p.trim());
      deviceModel = parts[2] || '';
    }
  }

  if (!osVersion) {
    const osMatch = userAgent.match(/(Windows NT|Mac OS X|Android|CPU (?:iPhone )?OS|Linux)[\/_ ]([0-9._]+)/);
    if (osMatch) {
      osVersion = `${osMatch[1]} ${osMatch[2]}`;
    }
  }

  if (!deviceType) {
    deviceType = /Mobi|Android/i.test(userAgent) ? 'mobile' : 'desktop';
  }

  const info = {
    userAgent,
    appVersion,
    deviceId,
    deviceModel,
    osVersion,
    deviceType,
    browserType,
    hardwareConcurrency,
    deviceMemory,
    screenOrientation,
    pixelRatio,
  };

  console.log('[DeviceInfo] Final device info:', info);
  return info;
}
