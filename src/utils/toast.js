// src/utils/toast.js
let toastHandler = null;

export function setToastHandler(handler) {
  toastHandler = handler;
}

export function showErrorToast(message, duration = 4000) {
  const messageStr = typeof message === 'object' ? JSON.stringify(message) : String(message || '');
  if (toastHandler) toastHandler({ title: messageStr, type: 'error', duration });
}

export function showWarningToast(message, duration = 4000) {
  const messageStr = typeof message === 'object' ? JSON.stringify(message) : String(message || '');
  if (toastHandler) toastHandler({ title: messageStr, type: 'warning', duration });
}

export function showInfoToast(message, duration = 4000) {
  const messageStr = typeof message === 'object' ? JSON.stringify(message) : String(message || '');
  if (toastHandler) toastHandler({ title: messageStr, type: 'info', duration });
}

export function showSuccessToast(message, duration = 4000) {
  const messageStr = typeof message === 'object' ? JSON.stringify(message) : String(message || '');
  if (toast<PERSON>and<PERSON>) toastHandler({ title: messageStr, type: 'success', duration });
}