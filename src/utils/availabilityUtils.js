/**
 * Utility functions for handling user availability status
 */

// Availability status color constants
export const AVAILABILITY_COLORS = {
    RED: '#FF0000',      // User is offline
    YELLOW: '#FFFF00',   // User is busy/in game  
    GREEN: '#00FF00'     // User is online
};

/**
 * Maps availability status hex color to readable status information
 * @param {string} statusColor - Hex color code from API (e.g., "#FF0000")
 * @returns {Object} Status information with label, color, CSS classes, etc.
 */
export const getAvailabilityStatus = (statusColor) => {
    if (!statusColor) {
        return { 
            label: 'Offline', 
            color: 'red', 
            bgColor: 'bg-red-500', 
            textColor: 'text-red-300',
            glowColor: 'shadow-red-400/50',
            hexColor: AVAILABILITY_COLORS.RED
        };
    }
    
    const normalizedColor = statusColor.toUpperCase();
    
    switch (normalizedColor) {
        case AVAILABILITY_COLORS.GREEN:
            return { 
                label: 'Online', 
                color: 'green', 
                bgColor: 'bg-green-500', 
                textColor: 'text-green-300',
                glowColor: 'shadow-green-400/50',
                hexColor: AVAILABILITY_COLORS.GREEN,
                description: 'I am ready to chat'
            };
        case AVAILABILITY_COLORS.YELLOW:
            return { 
                label: 'Busy', 
                color: 'yellow', 
                bgColor: 'bg-yellow-500', 
                textColor: 'text-yellow-300',
                glowColor: 'shadow-yellow-400/50',
                hexColor: AVAILABILITY_COLORS.YELLOW,
                description: 'Currently in game or busy'
            };
        case AVAILABILITY_COLORS.RED:
        default:
            return { 
                label: 'Offline', 
                color: 'red', 
                bgColor: 'bg-red-500', 
                textColor: 'text-red-300',
                glowColor: 'shadow-red-400/50',
                hexColor: AVAILABILITY_COLORS.RED,
                description: 'Currently offline'
            };
    }
};

/**
 * Gets appropriate animation class based on availability status
 * @param {string} color - Color identifier ('green', 'yellow', 'red')
 * @returns {string} CSS animation class
 */
export const getAvailabilityAnimation = (color) => {
    switch (color) {
        case 'green':
            return 'animate-pulse';
        case 'yellow':
            return 'animate-bounce';
        case 'red':
        default:
            return '';
    }
};

/**
 * Checks if user is considered "online" (green status)
 * @param {string} statusColor - Hex color code from API
 * @returns {boolean} True if user is online
 */
export const isUserOnline = (statusColor) => {
    return statusColor?.toUpperCase() === AVAILABILITY_COLORS.GREEN;
};

/**
 * Checks if user is considered "busy" (yellow status)
 * @param {string} statusColor - Hex color code from API
 * @returns {boolean} True if user is busy
 */
export const isUserBusy = (statusColor) => {
    return statusColor?.toUpperCase() === AVAILABILITY_COLORS.YELLOW;
};

/**
 * Checks if user is considered "offline" (red status or no status)
 * @param {string} statusColor - Hex color code from API
 * @returns {boolean} True if user is offline
 */
export const isUserOffline = (statusColor) => {
    return !statusColor || statusColor?.toUpperCase() === AVAILABILITY_COLORS.RED;
};
