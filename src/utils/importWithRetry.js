export default function importWithRetry(dynamicImport, retries = 3, delay = 500) {
  return new Promise((resolve, reject) => {
    const attempt = n => {
      dynamicImport()
        .then(module => resolve(module))
        .catch(error => {
          const isChunkLoadError =
            error?.name === 'ChunkLoadError' ||
            /Loading chunk [\w-]+ failed/i.test(error?.message || '');
          if (!isChunkLoadError || n === 0) {
            reject(error);
            return;
          }
          setTimeout(() => attempt(n - 1), delay);
        });
    };
    attempt(retries);
  });
}
