import { getCdnUrl } from '../utils/cdnUtils';

/**
 * Talent Data Model
 *
 * This file defines the structure of talent data received from the API
 * and provides utility functions for working with talent data.
 */

/**
 * Get the primary service from a talent's services array
 * @param {Object} talent - The talent object
 * @returns {Object|null} The primary service or null if no services
 */
export const getPrimaryService = (talent) => {
  if (!talent) {
    return null;
  }

  // Handle different API response formats
  if (talent.services && talent.services.length > 0) {
    return talent.services[0];
  }

  // Handle the case where services are in a different format
  if (talent.service && talent.service.length > 0) {
    return talent.service[0];
  }

  return null;
};

/**
 * Get the lowest price from a talent's pricing options
 * @param {Object} talent - The talent object
 * @returns {number|null} The lowest price or null if no pricing options
 */
export const getLowestPrice = (talent) => {
  const primaryService = getPrimaryService(talent);

  if (!primaryService) {
    return null;
  }

  // Handle different API response formats
  if (primaryService.pricing_options && primaryService.pricing_options.length > 0) {
    return primaryService.pricing_options.reduce((lowest, option) => {
      const price = option.pricing_option?.credits || option.credits || 0;
      return price < lowest ? price : lowest;
    }, primaryService.pricing_options[0].pricing_option?.credits || primaryService.pricing_options[0].credits || 0);
  }

  // Handle the case where pricing is in a different format
  if (primaryService.price) {
    return primaryService.price;
  }

  // Handle the case where rates are available
  if (primaryService.rates && primaryService.rates.length > 0) {
    return primaryService.rates.reduce((lowest, rate) => {
      const price = rate.price || 0;
      return price < lowest ? price : lowest;
    }, primaryService.rates[0].price || 0);
  }

  return null;
};

/**
 * Get the service type name from a talent
 * @param {Object} talent - The talent object
 * @returns {string} The service type name or empty string if not available
 */
export const getServiceTypeName = (talent) => {
  const primaryService = getPrimaryService(talent);

  if (!primaryService) {
    return '';
  }

  // Handle different API response formats
  if (primaryService.service_type && primaryService.service_type.name) {
    return primaryService.service_type.name;
  }

  // Handle the case where service type is a string
  if (primaryService.service_type && typeof primaryService.service_type === 'string') {
    return primaryService.service_type;
  }

  // Handle the case where type is available
  if (primaryService.type) {
    return typeof primaryService.type === 'string' ? primaryService.type : (primaryService.type.name || '');
  }

  // Handle the case where name is available
  if (primaryService.name) {
    return primaryService.name;
  }

  return '';
};

/**
 * Get the service style name from a talent
 * @param {Object} talent - The talent object
 * @returns {string} The service style name or empty string if not available
 */
export const getServiceStyleName = (talent) => {
  const primaryService = getPrimaryService(talent);

  if (!primaryService) {
    return '';
  }

  // Handle different API response formats
  if (primaryService.service_style && primaryService.service_style.name) {
    return primaryService.service_style.name;
  }

  // Handle the case where service style is a string
  if (primaryService.service_style && typeof primaryService.service_style === 'string') {
    return primaryService.service_style;
  }

  // Handle the case where style is available
  if (primaryService.style) {
    return typeof primaryService.style === 'string' ? primaryService.style : (primaryService.style.name || '');
  }

  return '';
};

/**
 * Check if a talent is featured
 * @param {Object} talent - The talent object
 * @returns {boolean} True if the talent is featured
 */
export const isFeatured = (talent) => {
  if (!talent) {
    return false;
  }

  // Check if the talent itself is featured
  if (talent.is_featured !== undefined) {
    return !!talent.is_featured;
  }

  // Check if the primary service is featured
  const primaryService = getPrimaryService(talent);
  if (primaryService && primaryService.is_featured !== undefined) {
    return !!primaryService.is_featured;
  }

  // Check if the talent has a featured flag
  if (talent.featured !== undefined) {
    return !!talent.featured;
  }

  return false;
};

/**
 * Get the talent's level as a number
 * @param {Object} talent - The talent object
 * @returns {number} The talent's level or 0 if not available
 */
export const getTalentLevel = (talent) => {
  if (!talent) {
    return 0;
  }

  // Handle different API response formats
  if (talent.level && talent.level.level) {
    return talent.level.level;
  }

  // Handle the case where level is a number
  if (talent.level && typeof talent.level === 'number') {
    return talent.level;
  }

  // Handle the case where level_number is available
  if (talent.level_number) {
    return talent.level_number;
  }

  return 0;
};

/**
 * Get the talent's level name
 * @param {Object} talent - The talent object
 * @returns {string} The talent's level name or empty string if not available
 */
export const getTalentLevelName = (talent) => {
  if (!talent) {
    return '';
  }

  // Handle different API response formats
  if (talent.level && talent.level.name) {
    return talent.level.name;
  }

  // Handle the case where level_name is available
  if (talent.level_name) {
    return talent.level_name;
  }

  // If we have a level number but no name, generate a generic name
  const level = getTalentLevel(talent);
  if (level > 0) {
    if (level < 20) return 'Beginner';
    if (level < 40) return 'Intermediate';
    if (level < 60) return 'Advanced';
    if (level < 80) return 'Expert';
    return 'Master';
  }

  return '';
};

/**
 * Format a talent object for display
 * @param {Object} talent - The raw talent object from API
 * @returns {Object} Formatted talent object with derived properties
 */
export const formatTalentForDisplay = (talent) => {
  if (!talent) {
    return {
      id: 0,
      uid: '',
      displayName: '',
      profileImage: '',
      coverImage: '',
      bio: '',
      gender: '',
      constellation: '',
      dateOfBirth: null,
      voice_note: null,
      levelNumber: 0,
      levelName: '',
      experience: 0,
      services: [],
      gameProfiles: [],
      skills: [],
      mediaFiles: {
        video: [],
        thumbnail: [],
        photos: []
      },
      followers: 0,
      isFollowing: false,
      rating: 0,
      reviewCount: 0,
      reviews: [],
      languages: [],
      personalities: [],
      race: '',
      height: 0,
      weight: 0,
      age: 0,
      referralCode: '',
      allow3rdPartyAccess: false,
      isOnline: false,
      missionsCompleted: 0,
      posts: [],
      availabilityStatus: null
    };
  }

  const primaryService = getPrimaryService(talent);

  // Helper function to safely get string values
  const getStringValue = (value) => {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      return value.name || value.description || '';
    }
    return '';
  };

  // Helper function to safely get number values
  const getNumberValue = (value) => {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') return parseFloat(value) || 0;
    return 0;
  };

  // Helper function to safely format dates
  const formatDate = (dateString) => {
    if (!dateString) return null;
    try {
      return new Date(dateString);
    } catch (e) {
      return null;
    }
  };

  // Helper to calculate age from date string
  const calculateAge = (dateString) => {
    if (!dateString) return 0;
    const birthDate = new Date(dateString);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Helper function to safely format arrays
  const formatArray = (arr, formatter) => {
    if (!Array.isArray(arr)) return [];
    return arr.map(item => formatter(item)).filter(Boolean);
  };

  // Helper function to format service objects
  const formatService = (service) => {
    if (!service) return null;

    const serviceType = service.service_type || {};
    const serviceCategory = service.service_category || {};
    const serviceStyles = Array.isArray(service.service_styles)
      ? service.service_styles.filter((s) => s.is_active !== false)
      : [];
    const pricingOptions = Array.isArray(service.pricing_options)
      ? service.pricing_options
      : service.pricing_options
        ? [service.pricing_options]
        : [];

    const rates = [];

    if (serviceStyles.length > 0) {
      serviceStyles.forEach((style) => {
        if (pricingOptions.length > 0) {
          pricingOptions.forEach((option) => {
            rates.push({
              type: `${getStringValue(style.name)} - ${getStringValue(
                option.pricing_option_type?.name || option.type || 'Standard'
              )}`,
              price: getNumberValue(
                style.preset_price ||
                  style.price ||
                  style.recommended_price ||
                  option.credits ||
                  option.price ||
                  service.price
              ),
              unit: getStringValue(
                option.pricing_option_type?.unit || option.unit || 'session'
              ),
              description: getStringValue(
                option.pricing_option_type?.description ||
                  option.description ||
                  style.description
              ),
              pricing_option_type_id:
                option.pricing_option_type_id ||
                option.id ||
                service.pricing_option_type_id ||
                null
            });
          });
        } else {
          rates.push({
            type: getStringValue(style.name),
            price: getNumberValue(
              style.preset_price || style.price || style.recommended_price
            ),
            unit: getStringValue(
              pricingOptions[0]?.pricing_option_type?.unit ||
                pricingOptions[0]?.unit ||
                'session'
            ),
            description: getStringValue(style.description)
          });
        }
      });
    } else {
      pricingOptions.forEach((option) => {
        rates.push({
          type: getStringValue(
            option.pricing_option_type?.name || option.type || 'Standard'
          ),
          price: getNumberValue(
            option.credits || option.price || service.price
          ),
          unit: getStringValue(
            option.pricing_option_type?.unit || option.unit || 'session'
          ),
          description: getStringValue(
            option.pricing_option_type?.description || option.description
          ),
          pricing_option_type_id:
            option.pricing_option_type_id ||
            option.id ||
            service.pricing_option_type_id ||
            null
        });
      });
    }

    return {
      id: service.id || 0,
      name: getStringValue(serviceType.name || service.name),
      image: getCdnUrl(
        serviceType.icon_path ||
        service.image ||
        serviceCategory.icon_path ||
        ''
      ),
      ordersCount: getNumberValue(
        service.total_person_order ||
          service.orders_count ||
          service.order_count
      ),
      service_category_id:
        service.service_category_id || serviceCategory.id || null,
      service_type_id: service.service_type_id || serviceType.id || null,
      service_category: {
        id: serviceCategory.id || 0,
        name: getStringValue(serviceCategory.name),
        slug: getStringValue(serviceCategory.slug),
        icon_path: serviceCategory.icon_path
          ? getCdnUrl(serviceCategory.icon_path)
          : '',
        description: getStringValue(serviceCategory.description)
      },
      service_type_title: getStringValue(service.service_type_title),
      service_type_description: getStringValue(
        service.service_type_description
      ),
      pricing_option_type_id: Array.isArray(service.pricing_options)
        ? service.pricing_options[0]?.id || service.pricing_options[0]?.pricing_option_type_id || null
        : service.pricing_options?.id || service.pricing_options?.pricing_option_type_id || null,
      pricing_options: pricingOptions,
      price: getNumberValue(service.price),
      rates,
      serviceType: getStringValue(serviceType.name),
      serviceTypeIcon: getCdnUrl(serviceType.icon_path || ''),
      styles: serviceStyles.map((style) => ({
        id: style.id || 0,
        name: getStringValue(style.name),
        price: getNumberValue(
          style.preset_price || style.price || style.recommended_price
        ),
        description: getStringValue(style.description)
      }))
    };
  };

  // Helper function to format game profile objects
  const formatGameProfile = (profile) => ({
    id: profile.id || 0,
    game: getStringValue(profile.game),
    username: getStringValue(profile.username),
    level: getNumberValue(profile.level),
    rank: getStringValue(profile.rank)
  });

  // Helper function to format skill objects
  const formatSkill = (skill) => ({
    id: skill.id || 0,
    name: getStringValue(skill.name),
    level: getNumberValue(skill.level),
    experience: getNumberValue(skill.experience)
  });

  // Helper function to format review objects
  const formatReview = (review) => ({
    id: review.id || 0,
    user: {
      id: review.reviewer?.id || review.user_id || 0,
      name: getStringValue(
        review.reviewer?.nickname ||
          review.reviewer?.name ||
          review.user_name ||
          'Anonymous'
      ),
      profile_image: getCdnUrl(
        review.reviewer?.profile_picture || review.user_profile_image || ''
      )
    },
    rating: getNumberValue(review.rating),
    comment: getStringValue(review.review_text || review.comment),
    created_at: formatDate(review.created_at)
  });

  // Process media files to include full CDN url and mime type
  const processMediaFiles = (mediaFiles = []) => {
    const mimeMap = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      webp: 'image/webp',
      gif: 'image/gif',
      mp4: 'video/mp4',
      mov: 'video/quicktime',
      avi: 'video/x-msvideo',
      webm: 'video/webm',
    };

    return mediaFiles.map((file) => {
      if (file.url && (file.url.startsWith('http://') || file.url.startsWith('https://'))) {
        return file;
      }

      let fileType = 'image/jpeg';
      if (file.mime_type) {
        fileType = file.mime_type;
      } else if (file.type && file.type.includes('/')) {
        fileType = file.type;
      } else if (file.type === 'video' || file.type === 'image') {
        const ext = (file.original || '').split('.').pop().toLowerCase();
        fileType = mimeMap[ext] || (file.type === 'video' ? 'video/mp4' : 'image/jpeg');
      } else if (file.extension) {
        fileType = mimeMap[file.extension.toLowerCase()] || 'image/jpeg';
      } else {
        const ext = (file.original || '').split('.').pop().toLowerCase();
        if (mimeMap[ext]) {
          fileType = mimeMap[ext];
        }
      }

      const filePath = file.optimized || file.original || file.path;

      return {
        ...file,
        type: fileType,
        url: getCdnUrl(filePath),
      };
    });
  };

  // Helper function to format media objects
  const formatMedia = (media) => {
    if (!media) return null;
    if (typeof media === 'string') {
      return {
        id: 0,
        url: getCdnUrl(media),
        title: '',
        description: '',
        video_id: 0,
      };
    }
    return {
      id: media.id || 0,
      url: getCdnUrl(media.path || media.url || ''),
      title: getStringValue(media.title),
      description: getStringValue(media.description),
      video_id: media.video_id || 0,
    };
  };

  // Helper function to format post objects from the backend structure
  const formatPost = (post) => {
    if (!post) return null;

    const mediaFiles = processMediaFiles(post.media_files || []);
    const firstMedia = mediaFiles.length > 0 ? mediaFiles[0] : null;

    return {
      id: post.id || 0,
      author: getStringValue(
        post.user?.nickname ||
          post.user?.name ||
          post.author
      ),
      author_image: getCdnUrl(
        post.user?.profile_picture || post.author_profile_image || ''
      ),
      created_at: formatDate(post.created_at),
      content: getStringValue(post.description || post.content || post.title),
      media_files: mediaFiles,
      image: firstMedia ? firstMedia.url : null,
      likes_count: getNumberValue(post.total_liked || post.likes || post.likes_count),
      comments_count: getNumberValue(post.total_comments || post.comments || post.comments_count)
    };
  };

  // Helper to format mission objects and extract image URLs
  const formatMission = (mission) => {
    if (!mission) return null;

    const firstImage = Array.isArray(mission.images) && mission.images.length > 0
      ? mission.images[0]
      : null;

    const formattedImages = Array.isArray(mission.images)
      ? mission.images.map((img) =>
          getCdnUrl(img.optimized_path || img.optimized || img.path || img.original_path || '')
        ).filter(Boolean)
      : [];

    const primaryImage = firstImage
      ? getCdnUrl(firstImage.optimized_path || firstImage.optimized || firstImage.path || firstImage.original_path || '')
      : mission.image
        ? getCdnUrl(mission.image)
        : formattedImages[0] || null;

    return { ...mission, images: formattedImages, image: primaryImage };
  };

  const formattedServices = formatArray(talent.services || [], formatService);

  // Extract availability data for calendar
  let availabilityData = [];
  if (Array.isArray(talent.user_availabilities) && talent.user_availabilities.length > 0) {
    availabilityData = talent.user_availabilities[0].availability_data || [];
  }

  return {
    // Basic Info
    id: talent.id || 0,
    uid: getStringValue(talent.uid),
    displayName: getStringValue(talent.nickname || talent.name || talent.display_name),
    profileImage: getCdnUrl(talent.profile_picture || talent.profile_image || talent.avatar || ''),
    coverImage: getCdnUrl(talent.profile_media?.photos?.[0]?.path || ''),
    bio: getStringValue(talent.biography || talent.bio),
    gender: getStringValue(talent.gender).toLowerCase(),
    constellation: getStringValue(talent.constellation),
    dateOfBirth: formatDate(talent.date_of_birth),
    age: calculateAge(talent.date_of_birth),
    voice_note: talent.voice_note || null,
    
    // Level and Experience
    levelNumber: getTalentLevel(talent),
    levelName: getTalentLevelName(talent),
    experience: getNumberValue(talent.experience),
    
    // Services
    services: formattedServices,

    // Pricing
    lowestPrice: talent.lowest_price !== undefined && talent.lowest_price !== null
      ? getNumberValue(talent.lowest_price)
      : getLowestPrice(talent),

    // Game Profiles
    gameProfiles: formatArray(talent.game_profiles || [], formatGameProfile),

    // Skills - ALIASED TO SERVICES for component compatibility
    skills: formattedServices,
    
    // Media Files
    mediaFiles: {
      video: talent.profile_media?.video
        ? Array.isArray(talent.profile_media.video)
          ? talent.profile_media.video.map(formatMedia).filter(Boolean)
          : [formatMedia(talent.profile_media.video)].filter(Boolean)
        : [],
      thumbnail: talent.profile_media?.thumbnail
        ? Array.isArray(talent.profile_media.thumbnail)
          ? talent.profile_media.thumbnail.map(formatMedia).filter(Boolean)
          : [formatMedia(talent.profile_media.thumbnail)].filter(Boolean)
        : [],
      photos: formatArray(talent.profile_media?.photos || [], formatMedia),
    },
    
    // Social and Stats
    followers: getNumberValue(
      talent.followers_count ?? talent.total_followers ?? talent.followers
    ),
    isFollowing: Boolean(
      talent.is_following ?? talent.is_follow
    ),
    averageRating: getNumberValue(talent.average_rating),
    rating: getNumberValue(talent.average_rating || talent.rating),
    reviewCount: getNumberValue(
      talent.reviews_count ?? (Array.isArray(talent.reviews) ? talent.reviews.length : 0)
    ),
    
    // Reviews
    reviews: formatArray(talent.reviews || [], formatReview),
    
    // Additional Info
    languages: formatArray(talent.languages || [], getStringValue),
    personalities: formatArray(talent.personalities || [], getStringValue),
    race: getStringValue(talent.race),
    height: getNumberValue(talent.height),
    weight: getNumberValue(talent.weight),
    referralCode: getStringValue(talent.referral_code),
    allow3rdPartyAccess: Boolean(talent.allow_3rd_party_access),
    isOnline: Boolean(talent.is_online),
    missionsCompleted: getNumberValue(
      talent.missions_completed ?? talent.total_missions_completed ?? 0
    ),
    posts: formatArray(talent.social_posts || talent.posts || [], formatPost),
    availabilityStatus: getStringValue(talent.availability_status),
    availabilityData,

    missions: formatArray(talent.missions || [], formatMission),
    hostedMissions: formatArray(talent.hosted_missions || [], formatMission),

    // User Availability
    user_availabilities: Array.isArray(talent.user_availabilities)
      ? talent.user_availabilities
      : []
  };
};

export default {
  getPrimaryService,
  getLowestPrice,
  getServiceTypeName,
  getServiceStyleName,
  isFeatured,
  getTalentLevel,
  getTalentLevelName,
  formatTalentForDisplay
};
