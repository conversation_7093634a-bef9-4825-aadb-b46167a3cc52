import React, { useState, useRef, useCallback } from "react";
import useTranslation from "../hooks/useTranslation";
import { motion, AnimatePresence } from "framer-motion";
import { getCdnUrl } from "../utils/cdnUtils";
import CreatePostModal from "../components/CreatePostModal";
import PostView from "../components/PostView";
import MainNavigation from "../components/navigation/MainNavigation";
import MobileNavigation from "../components/navigation/MobileNavigation";
import { SectionLoader, InlineLoader } from "../components/ui/LoadingIndicator";
import AutoPlayVideo from "../components/ui/AutoPlayVideo";
import useMediaDimensions from "../hooks/useMediaDimensions";
import { useExploreFeed } from "../hooks/explore/useExploreFeed";
import { useLikePost } from "../hooks/explore/usePostMutations";
import { useQueryClient } from '@tanstack/react-query';
import socialPostService from "../services/socialPostService";
import { Link } from "react-router-dom";
import ReactDOM from "react-dom";
import { useAuth } from "../contexts/AuthContext";
import { useToast } from '../components/common/ToastProvider';
import ReportPostModal from "../components/ReportPostModal";
import { FaFlag } from "react-icons/fa";
// Add CSS for masonry grid
const masonryStyles = `
  .masonry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    grid-auto-rows: 200px;
    grid-auto-flow: dense;
    gap: 1rem;
  }
  
  .masonry-item {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    background: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .masonry-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  .masonry-item img,
  .masonry-item video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
  
  @media (min-width: 640px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
  }
  
  @media (min-width: 768px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
  }
  
  @media (min-width: 1024px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
  }
  
  @media (min-width: 1280px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
  }
`;

// Utility to calculate age from date_of_birth
function getAge(dateString) {
  if (!dateString) return null;
  const today = new Date();
  const birthDate = new Date(dateString);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

// Custom hook to detect desktop
function useIsDesktop() {
  const [isDesktop, setIsDesktop] = useState(() => typeof window !== 'undefined' ? window.innerWidth >= 1024 : false);
  React.useEffect(() => {
    function handleResize() {
      setIsDesktop(window.innerWidth >= 1024);
    }
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return isDesktop;
}

const PostImageCarousel = ({ mediaFiles, postId }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isHovered, setIsHovered] = useState(false);

    const handlePrev = (e) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev === 0 ? mediaFiles.length - 1 : prev - 1));
    };

    const handleNext = (e) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev === mediaFiles.length - 1 ? 0 : prev + 1));
    };

    return (
        <div 
            className="relative w-full group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Main Image/Video Container */}
            <div className="relative w-full">
                {(mediaFiles[currentIndex].type.startsWith('image') || mediaFiles[currentIndex].type === 'image') ? (
                    <img
                        src={mediaFiles[currentIndex].url}
                        alt={`Post ${postId} - Image ${currentIndex + 1}`}
                        className="w-full h-auto object-contain rounded-t-2xl"
                        loading="lazy"
                    />
                ) : (
                    <AutoPlayVideo
                        className="w-full h-auto object-contain rounded-t-2xl"
                        poster={mediaFiles[currentIndex].thumbnail || ''}
                        src={mediaFiles[currentIndex].url}
                        type={mediaFiles[currentIndex].type}
                        preload="none"
                    />
                )}
            </div>

            {/* Navigation Arrows */}
            {mediaFiles.length > 1 && (
                <>
                    <motion.button
                        onClick={handlePrev}
                        className={`absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/50 text-white backdrop-blur-sm transition-all duration-200 ${
                            isHovered ? 'opacity-100' : 'opacity-0'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </motion.button>
                    <motion.button
                        onClick={handleNext}
                        className={`absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/50 text-white backdrop-blur-sm transition-all duration-200 ${
                            isHovered ? 'opacity-100' : 'opacity-0'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </motion.button>
                </>
            )}

            {/* Multiple Media Indicator */}
            {mediaFiles.length > 1 && (
                <motion.div
                    className="absolute top-3 right-3 bg-black/60 backdrop-blur-sm rounded-full p-2"
                    whileHover={{ scale: 1.1 }}
                >
                    <svg
                        className="w-4 h-4 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                        />
                    </svg>
                </motion.div>
            )}
        </div>
    );
}; 

// Grid item representing a single post in the masonry layout
const PostGridItem = React.forwardRef(({ post, onClick, onLike, onDelete, onReport, formatTimeAgo, currentUserId, isAuthenticated }, ref) => {
  const media = post.media_files?.[0];
  const dims = useMediaDimensions(media);
  const hasDims = dims.width > 1 && dims.height > 1;
  const aspectRatio = hasDims ? `${dims.width} / ${dims.height}` : '1 / 1';
  const gridRowSpan = hasDims ? `span ${Math.max(1, Math.ceil((dims.height / dims.width) * 2))}` : 'span 1';

  return (
    <motion.div
      ref={ref}
      key={post.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="group relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/20 dark:border-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.2] cursor-pointer"
      onClick={onClick}
      whileHover={{ y: -4 }}
      layout
      style={{ gridRow: gridRowSpan }}
    >
      {/* Only show report button if user is authenticated AND doesn't own the post */}
      {isAuthenticated && !(currentUserId && post.user?.id === currentUserId) && (
        <motion.button
          onClick={(e) => {
            e.stopPropagation();
            onReport(post.id);
          }}
          className="absolute top-2 right-2 p-1.5 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-full border border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 shadow-md z-10"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Report post"
        >
          <FaFlag className="w-4 h-4 text-indigo-500" />
        </motion.button>
      )}
      {/* Media Section */}
      <div className="relative w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
        {media ? (
          <div className="w-full h-full">
            {media.type && (media.type.startsWith('image') || media.type === 'image') ? (
              <img
                src={media.url}
                alt={`Post ${post.id}`}
                className="w-full h-full object-cover"
                loading="lazy"
                style={{ aspectRatio }}
              />
            ) : (
              <AutoPlayVideo
                className="w-full h-full object-cover"
                poster={media.thumbnail || ''}
                src={media.url}
                type={media.type}
                preload="none"
                style={{ aspectRatio }}
              />
            )}
            {post.media_files.length > 1 && (
              <motion.div
                className="absolute top-3 right-3 bg-black/60 dark:bg-white/20 backdrop-blur-sm rounded-full p-2"
                whileHover={{ scale: 1.1 }}
              >
                <svg
                  className="w-4 h-4 text-white dark:text-gray-900"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
              </motion.div>
            )}
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-800 dark:to-gray-900">
            <motion.svg
              className="w-16 h-16 text-indigo-300 dark:text-indigo-700"
              fill="currentColor"
              viewBox="0 0 20 20"
              whileHover={{ scale: 1.1 }}
            >
              <path
                fillRule="evenodd"
                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                clipRule="evenodd"
              />
            </motion.svg>
          </div>
        )}
      </div>

      {/* Enhanced Backgroundless Content Overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Subtle gradient vignette for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        {/* Profile Section - Top Left */}
        <div className="absolute top-3 left-3 pointer-events-auto">
          <ProfileHoverCard user={post.user}>
            <motion.div
              className="flex items-center space-x-2 group"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <Link
                to={post.user.role === 'talent' ? `/talents/${post.user.id}` : `/talents/${post.user.id}`}
                aria-label={`View ${post.user.name}'s profile`}
                className="relative w-10 h-10 rounded-full overflow-hidden shadow-lg ring-2 ring-white/80 dark:ring-gray-200/80 hover:ring-white dark:hover:ring-gray-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                tabIndex={0}
              >
                {/* Glass-morphism background for profile picture */}
                <div className="absolute inset-0 bg-white/20 dark:bg-gray-800/30 backdrop-blur-sm"></div>

                {post.user?.profile_picture ? (
                  <img
                    src={getCdnUrl(post.user.profile_picture)}
                    alt={post.user.username || post.user.name}
                    className="relative z-10 w-full h-full object-cover"
                  />
                ) : (
                  <div className="relative z-10 w-full h-full bg-gradient-to-br from-indigo-400 to-purple-500 flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {(post.user?.username || post.user?.name || '?').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}

                {/* Hover glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Link>

              {/* Username with glass-morphism background */}
              <Link
                to={post.user.role === 'talent' ? `/talents/${post.user.id}` : `/talents/${post.user.id}`}
                className="px-2 py-1 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md rounded-full shadow-lg border border-white/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                tabIndex={0}
              >
                <p className="text-xs font-semibold text-gray-800 dark:text-gray-200 truncate max-w-[80px] hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                  {post.user?.username || post.user?.name || 'Unknown User'}
                </p>
              </Link>
            </motion.div>
          </ProfileHoverCard>
        </div>

        {/* Time Badge - Top Right */}
        <div className="absolute mb-3 bottom-5 right-3 pointer-events-auto">
          <motion.div
            className="px-3 py-1.5 bg-black/60 dark:bg-gray-900/80 backdrop-blur-md rounded-full shadow-lg border border-white/20 dark:border-gray-600/30"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 400, damping: 25 }}
          >
            <p className="text-xs font-medium text-white dark:text-gray-200">
              {formatTimeAgo(post.created_at)}
            </p>
          </motion.div>
        </div>

        {/* Title and Description - Bottom Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 pointer-events-auto">
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, type: "spring", stiffness: 400, damping: 25 }}
          >
            {/* Title with enhanced glass-morphism */}
            <div className="inline-block">
              <h3 className="text-sm font-bold text-white dark:text-gray-100 px-3 py-2 bg-black/70 dark:bg-gray-900/80 backdrop-blur-md rounded-xl shadow-lg border border-white/20 dark:border-gray-600/30 hover:bg-black/80 dark:hover:bg-gray-800/90 transition-all duration-300 line-clamp-2 leading-relaxed">
                {post.title}
              </h3>
            </div>

            {/* Description with glass-morphism */}
            {post.description && (
              <div className="inline-block max-w-full">
                <p className="text-xs text-gray-100 dark:text-gray-200 px-3 py-2 bg-black/60 dark:bg-gray-900/70 backdrop-blur-md rounded-xl shadow-lg border border-white/15 dark:border-gray-600/25 line-clamp-2 leading-relaxed">
                  {post.description}
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Interaction Bar - Floating at Bottom */}
      <div className="absolute bottom-3 left-3 right-3 pointer-events-auto">
        <motion.div
          className="flex items-center justify-between p-3 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-xl border border-white/50 dark:border-gray-700/50"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 400, damping: 25 }}
        >
          <div className="flex items-center space-x-4">
            <motion.div
              className="flex items-center space-x-1.5 cursor-pointer group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={(e) => onLike(post.id, post.is_liked, e)}
            >
              <div className={`p-1.5 rounded-full transition-all duration-300 ${
                post.is_liked
                  ? 'bg-red-100 dark:bg-red-900/30'
                  : 'bg-gray-100 dark:bg-gray-800 group-hover:bg-red-50 dark:group-hover:bg-red-900/20'
              }`}>
                <svg
                  className={`w-4 h-4 transition-colors duration-300 ${
                    post.is_liked
                      ? 'text-red-500'
                      : 'text-gray-600 dark:text-gray-400 group-hover:text-red-500'
                  }`}
                  fill={post.is_liked ? 'currentColor' : 'none'}
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </div>
              <span className="text-xs font-semibold text-gray-700 dark:text-gray-300">
                {post.total_liked || 0}
              </span>
            </motion.div>

            <motion.div
              className="flex items-center space-x-1.5 group"
              whileHover={{ scale: 1.05 }}
            >
              <div className="p-1.5 rounded-full bg-gray-100 dark:bg-gray-800 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-all duration-300">
                <svg
                  className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-blue-500 transition-colors duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <span className="text-xs font-semibold text-gray-700 dark:text-gray-300">
                {post.total_comments || 0}
              </span>
            </motion.div>
          </div>

          <motion.button
            className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 text-white text-xs font-semibold rounded-xl shadow-lg hover:shadow-xl hover:from-indigo-600 hover:to-purple-700 dark:hover:from-indigo-700 dark:hover:to-purple-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View
          </motion.button>
        </motion.div>
      </div>

      {/* Delete Button - Automatically positioned in top-right (only for post owner) */}
      {currentUserId && post.user?.id === currentUserId && (
        <motion.button
          onClick={(e) => onDelete(post.id, e)}
          className="absolute top-2 right-2 p-1.5 bg-red-500/90 hover:bg-red-600 text-white rounded-full shadow-lg backdrop-blur-sm border border-white/20 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 z-10"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          title="Delete post"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4, type: "spring", stiffness: 400, damping: 25 }}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </motion.button>
      )}
    </motion.div>
  );
});

PostGridItem.displayName = 'PostGridItem';

const Explore = () => {
  const [activeTab, setActiveTab] = useState("for-you");
  const { t } = useTranslation(["explore", "common"]);
  const [isCreatePostModalOpen, setIsCreatePostModalOpen] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [isPostViewOpen, setIsPostViewOpen] = useState(false);
  const [deletingPostId, setDeletingPostId] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [postToDelete, setPostToDelete] = useState(null);
  const [reportingPostId, setReportingPostId] = useState(null);
  const [showReportModal, setShowReportModal] = useState(false);

  const { success: showSuccessToast, error: showErrorToast } = useToast();

  const { user } = useAuth();

  const handleReportPost = (postId) => {
    setReportingPostId(postId);
    setShowReportModal(true);
  };

  React.useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (params.get('create') === 'post') {
      setIsCreatePostModalOpen(true);
      params.delete('create');
      window.history.replaceState(
        {},
        '',
        `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`
      );
    }
  }, []);

  const feedType = activeTab === "for-you" ? "for_you" : "following";
  const { 
    data, 
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useExploreFeed(feedType);

  const posts = data?.posts || [];
  const hasMore = data?.hasMore || false;
  const initialLoading = isLoading && posts.length === 0;

  const { mutate: likePostMutation } = useLikePost(feedType);
  const queryClient = useQueryClient();

  const observer = useRef();
  const lastPostElementRef = useCallback(
    (node) => {
      if (isLoading || isFetchingNextPage) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage();
        }
      });
      if (node) observer.current.observe(node);
    },
    [isLoading, isFetchingNextPage, hasNextPage, fetchNextPage],
  );

  const handlePostClick = (postId) => {
    queryClient.prefetchQuery({
      queryKey: ['socialPost', postId],
      queryFn: () => socialPostService.getPostById(postId),
    });
    setSelectedPostId(postId);
    setIsPostViewOpen(true);
  };

  const handlePostCreated = (newPost) => {
    // Prepend the newly created post optimistically so it appears immediately
    // without waiting for the refetch to complete.
    queryClient.setQueryData(['explore', 'feed', feedType, {}], (oldData) => {
      if (!oldData) return oldData;
      return {
        ...oldData,
        posts: [newPost, ...(oldData.posts || [])],
      };
    });

    // Invalidate the feed query to ensure fresh data from the server
    queryClient.invalidateQueries({ queryKey: ['explore', 'feed', feedType] });

    showSuccessToast({ title: 'Post Created', description: 'Your post is live!' });
  };

  const handleLikePost = (postId, wasLiked, e) => {
    e.stopPropagation();
    likePostMutation({ postId, isLiked: wasLiked });
  };

  const handleDeletePost = (postId, e) => {
    e.stopPropagation();
    const post = posts.find(p => p.id === postId);
    setPostToDelete(post);
    setShowDeleteConfirm(true);
  };

  const confirmDeletePost = async () => {
    if (!postToDelete) return;

    setDeletingPostId(postToDelete.id);
    setShowDeleteConfirm(false);

    try {
      await socialPostService.deletePost(postToDelete.id);

      // Remove post from cache
      queryClient.setQueryData(['explore', 'feed', feedType], (oldData) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          posts: oldData.posts.filter(post => post.id !== postToDelete.id)
        };
      });

      showSuccessToast({ title: 'Post Deleted', description: 'Post deleted successfully' });
    } catch (error) {
      console.error('Error deleting post:', error);
      showErrorToast({
        title: 'Delete Failed',
        description: error.response?.data?.message || 'Failed to delete post. Please try again.'
      });
    } finally {
      setDeletingPostId(null);
      setPostToDelete(null);
    }
  };

  const cancelDeletePost = () => {
    setShowDeleteConfirm(false);
    setPostToDelete(null);
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInSeconds = Math.floor((now - postDate) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return postDate.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Shared Navigation */}
      <MainNavigation activeItem="/explore" />

      {/* Enhanced Tab Navigation with Modern 2025 Design */}
      <div className="relative bg-white/80 dark:bg-gray-950/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
        {/* Gradient Background Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-50/30 via-purple-50/20 to-blue-50/30 dark:from-indigo-950/20 dark:via-purple-950/10 dark:to-blue-950/20"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex space-x-2 relative">
            {/* For You Tab */}
            <motion.button
              className={`relative px-6 py-4 font-semibold text-sm rounded-t-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-950 ${
                activeTab === "for-you"
                  ? "text-white dark:text-white shadow-lg"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-800 bg-transparent hover:bg-transparent dark:hover:text-gray-200"
              }`}
              onClick={() => setActiveTab("for-you")}
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              {/* Active Tab Background */}
              {activeTab === "for-you" && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-indigo-500 via-purple-600 to-blue-600 dark:from-indigo-600 dark:via-purple-700 dark:to-blue-700 rounded-t-2xl shadow-xl"
                  layoutId="activeTab"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  style={{
                    boxShadow: '0 8px 32px rgba(99, 102, 241, 0.3), 0 2px 16px rgba(139, 92, 246, 0.2)'
                  }}
                />
              )}

              {/* Hover Background for Inactive Tab */}
              {activeTab !== "for-you" && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-gray-100/80 to-gray-200/80 dark:from-gray-800/80 dark:to-gray-700/80 rounded-t-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"
                  style={{
                    backdropFilter: 'blur(10px)'
                  }}
                />
              )}

              {/* Tab Content */}
              <span className="relative z-10 flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>{t("explore:tabs.forYou")}</span>
              </span>

              {/* Active Tab Glow Effect */}
              {activeTab === "for-you" && (
                <motion.div
                  className="absolute -inset-1 bg-gradient-to-br from-indigo-400/20 via-purple-400/20 to-blue-400/20 rounded-t-2xl blur-sm"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>

            {/* Following Tab */}
            <motion.button
              className={`relative px-6 py-4 font-semibold text-sm rounded-t-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-950 ${
                activeTab === "following"
                  ? "text-white dark:text-white shadow-lg"
                  : "text-gray-600 dark:text-gray-400 bg-transparent hover:bg-transparent hover:text-gray-800 dark:hover:text-gray-200"
              }`}
              onClick={() => setActiveTab("following")}
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              {/* Active Tab Background */}
              {activeTab === "following" && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-indigo-500 via-purple-600 to-blue-600 dark:from-indigo-600 dark:via-purple-700 dark:to-blue-700 rounded-t-2xl shadow-xl"
                  layoutId="activeTab"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  style={{
                    boxShadow: '0 8px 32px rgba(99, 102, 241, 0.3), 0 2px 16px rgba(139, 92, 246, 0.2)'
                  }}
                />
              )}

              {/* Hover Background for Inactive Tab */}
              {activeTab !== "following" && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-gray-100/80 to-gray-200/80 dark:from-gray-800/80 dark:to-gray-700/80 rounded-t-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"
                  style={{
                    backdropFilter: 'blur(10px)'
                  }}
                />
              )}

              {/* Tab Content */}
              <span className="relative z-10 flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>{t("explore:tabs.following")}</span>
              </span>

              {/* Active Tab Glow Effect */}
              {activeTab === "following" && (
                <motion.div
                  className="absolute -inset-1 bg-gradient-to-br from-indigo-400/20 via-purple-400/20 to-blue-400/20 rounded-t-2xl blur-sm"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>
          </div>
        </div>

        {/* Enhanced Bottom Border with Gradient */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-700 to-transparent"></div>
      </div>

      {/* Content Area */}
      <div className="w-full py-6 px-2 sm:px-4 lg:px-8 xl:px-12">
        {/* Error Message */}
        {isError && (
          <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-6 flex items-center justify-between">
            <span>{error?.message || 'An error occurred'}</span>
            <button
              onClick={() => refetch()}
              className="bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 text-red-800 dark:text-red-200 px-3 py-1 rounded-md text-sm transition-colors"
            >
              {t("explore:retry")}
            </button>
          </div>
        )}

        {/* Initial Loading State */}
        {initialLoading && (
          <SectionLoader
            type="wave"
            size="large"
            message={t("explore:loading")}
            color="indigo"
          />
        )}

        {/* Posts Grid */}
        {!initialLoading && posts.length > 0 && (
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 auto-rows-[200px]">
            <AnimatePresence>
              {posts.map((post, index) => (
                <PostGridItem
                  key={post.id}
                  ref={index === posts.length - 1 ? lastPostElementRef : null}
                  post={post}
                  onClick={() => handlePostClick(post.id)}
                  onLike={handleLikePost}
                  onDelete={handleDeletePost}
                  onReport={handleReportPost}
                  formatTimeAgo={formatTimeAgo}
                  currentUserId={user?.id}
                  isAuthenticated={!!user}
                />
              ))}
            </AnimatePresence>
          </div>
        )}

        {/* Loading More State */}
        {isFetchingNextPage && (
          <div className="flex justify-center mt-6">
            <InlineLoader size="medium" color="indigo" />
          </div>
        )}

        {/* Empty State */}
        {!initialLoading && !isLoading && posts.length === 0 && (
          <motion.div
            className="text-center py-16 text-gray-900 dark:text-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-gray-800 dark:to-gray-900 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg
                className="w-12 h-12 text-indigo-500 dark:text-indigo-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              No posts yet
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
              {activeTab === "for-you"
                ? "We don't have any recommended posts for you yet. Be the first to share your gaming moments!"
                : "People you follow haven't posted anything yet. Check out the For You tab or create your first post!"}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {activeTab === "following" && (
                <motion.button
                  onClick={() => setActiveTab("for-you")}
                  className="bg-white dark:bg-gray-900 border-2 border-indigo-600 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400 px-6 py-3 rounded-2xl font-semibold hover:bg-indigo-50 dark:hover:bg-gray-800 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Discover Posts
                </motion.button>
              )}
              <motion.button
                onClick={() => setIsCreatePostModalOpen(true)}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-800 dark:to-purple-800 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Create Your First Post
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* End of Results Message */}
        {!isLoading && !hasMore && posts.length > 0 && (
          <motion.div
            className="text-center py-8 text-gray-900 dark:text-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-green-500 dark:text-green-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300 font-medium">
              You've reached the end of the feed 🎉
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
              Check back later for more amazing content!
            </p>
          </motion.div>
        )}
      </div>

      {/* Fixed Create Post Button - Positioned above mobile navigation */}
      <motion.div
        className="fixed bottom-24 md:bottom-8 right-8 z-[70]"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: "spring", stiffness: 260, damping: 20, delay: 0.5 }}
      >
        <motion.button
          onClick={() => setIsCreatePostModalOpen(true)}
          className="relative bg-gradient-to-br from-indigo-500 to-indigo-600 dark:from-indigo-800 dark:to-indigo-900 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:shadow-indigo-500/20 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500/30 overflow-hidden group"
          whileHover={{
            scale: 1.05,
            rotate: 45,
            boxShadow: "0 20px 25px -5px rgba(99, 102, 241, 0.2)",
          }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Subtle gradient overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent dark:from-gray-700/20 dark:to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          />

          {/* Plus icon */}
          <motion.svg
            className="w-7 h-7 relative z-10 text-white dark:text-gray-100"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            whileHover={{ rotate: -45 }}
            transition={{ duration: 0.3 }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2.5"
              d="M12 4v16m8-8H4"
            />
          </motion.svg>

          {/* Subtle pulse effect */}
          <motion.div
            className="absolute inset-0 rounded-full bg-white/10 dark:bg-gray-700/20"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0, 0.3],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          {/* Tooltip */}
          <motion.div
            className="absolute right-full mr-4 px-3 py-1.5 bg-gray-900/90 dark:bg-gray-800/90 text-white dark:text-gray-100 text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none backdrop-blur-sm"
            initial={{ opacity: 0, x: 10 }}
            whileHover={{ opacity: 1, x: 0 }}
          >
            Create New Post
            <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 rotate-45 w-2 h-2 bg-gray-900/90"></div>
          </motion.div>
        </motion.button>
      </motion.div>

      {/* Create Post Modal */}
      <CreatePostModal
        isOpen={isCreatePostModalOpen}
        onClose={() => setIsCreatePostModalOpen(false)}
        onPostCreated={handlePostCreated}
      />

      {/* Post View Modal */}
      <PostView
        postId={selectedPostId}
        isOpen={isPostViewOpen}
        onClose={() => {
          setIsPostViewOpen(false);
          setSelectedPostId(null);
        }}
      />

      {/* Report Post Modal */}
      <ReportPostModal
        isOpen={showReportModal}
        onClose={() => {
          setShowReportModal(false);
          setReportingPostId(null);
        }}
        postId={reportingPostId}
      />

      {/* Delete Confirmation Dialog */}
      <AnimatePresence>
        {showDeleteConfirm && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[70] p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={cancelDeletePost}
          >
            <motion.div
              className="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full shadow-2xl border border-gray-200 dark:border-gray-700"
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-left text-gray-900 dark:text-white">Delete Post</h3>
                  <p className="text-sm text-left text-gray-600 dark:text-gray-300">This action cannot be undone</p>
                </div>
              </div>

              <p className="mt-7 mb-10 text-gray-700 dark:text-gray-300 mb-6">
                Are you sure you want to delete "{postToDelete?.title || 'this post'}"? This will permanently remove the post and all its comments.
              </p>

              <div className="flex space-x-3">
                <motion.button
                  onClick={cancelDeletePost}
                  className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  onClick={confirmDeletePost}
                  disabled={deletingPostId === postToDelete?.id}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-xl font-medium transition-colors flex items-center justify-center"
                  whileHover={{ scale: deletingPostId === postToDelete?.id ? 1 : 1.02 }}
                  whileTap={{ scale: deletingPostId === postToDelete?.id ? 1 : 0.98 }}
                >
                  {deletingPostId === postToDelete?.id ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    'Delete Post'
                  )}
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/explore" />
    </div>
  );
};

export default Explore;

function ProfileHoverCard({ user, children }) {
  const isDesktop = useIsDesktop();
  const [open, setOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
  const [cardDims, setCardDims] = useState({ width: 0, height: 0 });
  const triggerRef = React.useRef();
  const cardRef = React.useRef();
  let timeout;

  // Handlers for hover/focus
  const handleMouseEnter = () => {
    clearTimeout(timeout);
    setOpen(true);
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + rect.width / 2 + window.scrollX,
        width: rect.width
      });
    }
  };
  const handleMouseLeave = () => {
    timeout = setTimeout(() => setOpen(false), 120);
  };
  const handleFocus = () => {
    setOpen(true);
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + rect.width / 2 + window.scrollX,
        width: rect.width
      });
    }
  };
  const handleBlur = (e) => {
    // Only close if focus moves outside the card and trigger
    if (
      cardRef.current &&
      !cardRef.current.contains(e.relatedTarget) &&
      triggerRef.current &&
      !triggerRef.current.contains(e.relatedTarget)
    ) {
      setOpen(false);
    }
  };

  // Keyboard: close on Escape
  React.useEffect(() => {
    if (!open) return;
    function onKeyDown(e) {
      if (e.key === 'Escape') setOpen(false);
    }
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [open]);

  // Calculate age and profile details
  const age = user ? getAge(user.date_of_birth) : null;
  const level = user?.level?.name || user?.level?.level || null;
  const profileLink = user
    ? user.role === 'talent'
      ? `/talents/${user.id}`
      : `/talents/${user.id}`
    : '';

  // Responsive and overflow handling
  React.useEffect(() => {
    if (open && cardRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect();
      setCardDims({ width: cardRect.width, height: cardRect.height });
    }
  }, [open]);

  // Calculate left/top to keep card in viewport
  let left = position.left;
  let top = position.top + 8;
  const padding = 8;
  if (typeof window !== 'undefined') {
    if (cardDims.width && left - cardDims.width / 2 < padding) {
      left = cardDims.width / 2 + padding;
    }
    if (cardDims.width && left + cardDims.width / 2 > window.innerWidth - padding) {
      left = window.innerWidth - cardDims.width / 2 - padding;
    }
    if (cardDims.height && top + cardDims.height > window.innerHeight - padding) {
      top = window.innerHeight - cardDims.height - padding;
    }
  }

  // If there's no user data or not on desktop, simply render children
  if (!user || !isDesktop) {
    return <span ref={triggerRef}>{children}</span>;
  }

  return (
    <span
      className="relative inline-block"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={0}
      ref={triggerRef}
      aria-haspopup="dialog"
      aria-expanded={open}
      aria-label={`Show profile preview for ${user.name}`}
      role="button"
    >
      {children}
      {open && ReactDOM.createPortal(
        <AnimatePresence>
          <motion.div
            ref={cardRef}
            initial={{ opacity: 0, y: 10, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.98 }}
            transition={{ type: 'spring', stiffness: 300, damping: 24 }}
            className="z-[9999] fixed pointer-events-auto min-w-[220px] max-w-xs w-[90vw] rounded-2xl shadow-2xl border border-indigo-200 p-0 flex flex-col items-center bg-gradient-to-br from-white/80 via-indigo-50/80 to-blue-100/80 backdrop-blur-xl ring-1 ring-indigo-100/60"
            style={{
              top,
              left,
              transform: 'translateX(-50%)',
              boxShadow: '0 12px 40px 0 rgba(80,80,180,0.18), 0 2px 12px 0 rgba(80,80,180,0.10)'
            }}
            role="dialog"
            aria-modal="true"
            aria-label={`Profile preview for ${user.name}`}
            tabIndex={-1}
          >
            {/* Gradient accent bar */}
            <div className="absolute top-0 left-0 w-full h-2 rounded-t-2xl bg-gradient-to-r from-indigo-400 via-blue-400 to-violet-400" />
            {/* Arrow */}
            <div
              className="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-4 z-10"
              style={{ pointerEvents: 'none' }}
              aria-hidden="true"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" className="block">
                <polygon points="8,0 16,16 0,16" fill="#f8fafc" stroke="#a5b4fc" strokeWidth="1" />
              </svg>
            </div>
            <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-indigo-200 to-blue-200 flex items-center justify-center mb-2 mt-6 shadow-lg ring-4 ring-indigo-200/40 border-2 border-white overflow-hidden">
              {user.profile_picture ? (
                <img
                  src={getCdnUrl(user.profile_picture)}
                  alt={user.username || user.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-indigo-600 font-extrabold text-3xl">
                  {(user.username || user.name || "?").charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div className="text-center w-full px-2 pb-2">
              <div className="font-extrabold text-lg text-gray-900 truncate leading-tight mb-0.5">{user.name}</div>
              <div className="text-xs text-indigo-500 truncate mb-1 font-semibold">@{user.username || user.name}</div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mb-1">
                {age && <span className="font-medium">Age: {age}</span>}
                {level && <span className="inline-block px-2 py-0.5 bg-indigo-100 text-indigo-700 rounded-full ml-1 font-semibold shadow">{level}</span>}
              </div>
              {user.biography && (
                <div className="text-xs text-gray-600 mb-2 line-clamp-2 italic">{user.biography}</div>
              )}
              <Link
                to={profileLink}
                className="inline-block mt-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-xl font-bold text-sm shadow-lg hover:from-indigo-600 hover:to-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-400"
                aria-label={`View ${user.name}'s profile`}
                role="link"
              >
                View Profile
              </Link>
            </div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}
    </span>
  );
}
