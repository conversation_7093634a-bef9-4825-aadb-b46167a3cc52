import React, { useState, useEffect, useRef, useCallback } from 'react';
import TalentCard from '../components/TalentCard';
import FilterModal from '../components/FilterModal';
import { motion, AnimatePresence } from 'framer-motion';
import talentService from '../services/talentService';
import MainNavigation from '../components/navigation/MainNavigation';
import MobileNavigation from '../components/navigation/MobileNavigation';
import { SectionLoader, InlineLoader } from '../components/ui/LoadingIndicator';
import { useLocation } from 'react-router-dom';
import { referenceDataApi } from '../services/referenceDataApi';
import useTranslation from '../hooks/useTranslation';
// We'll use authentication in future implementations
// import { useAuth } from '../contexts/AuthContext';


const TalentPage = () => {
  // We'll use authentication status in future implementations
  // const { isAuthenticated } = useAuth();

  const location = useLocation();
  const { t } = useTranslation(['talent', 'common']);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [activeFilters, setActiveFilters] = useState(location.state?.initialFilters || {});
  const [searchTerm, setSearchTerm] = useState('');
  const [recentSearches, setRecentSearches] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [serviceTypeMap, setServiceTypeMap] = useState({});
  const [serviceCategories, setServiceCategories] = useState([]);
  const [serviceCategoryMap, setServiceCategoryMap] = useState({});

  const isSearchingActive = !!searchTerm && searchTerm.trim().length > 0;

  const perPage = 50; // Number of talents per page
  const [talents, setTalents] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const popularSearches = [
    'Valorant',
    'League of Legends',
    'John',
    'Fortnite',
  ];

  // Fetch talents with filters
  const fetchTalents = useCallback(async (page = 1, filters = activeFilters, append = false) => {
    try {
      setIsLoading(page === 1);
      setIsLoadingMore(page > 1);
      setIsError(false);
      setError(null);

      // Use filterTalents if there are active filters, otherwise use getTalents
      const response = Object.keys(filters).length > 0 
        ? await talentService.filterTalents(filters, page, perPage)
        : await talentService.getTalents(page, perPage);

      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch talents');
      }

      const newTalents = response.data || [];
      const meta = response.meta || {};

      if (append) {
        setTalents(prev => [...prev, ...newTalents]);
      } else {
        setTalents(newTalents);
      }

      setCurrentPage(page);
      setHasNextPage(meta.current_page < meta.last_page);
      
    } catch (err) {
      setIsError(true);
      setError(err.message || 'Failed to fetch talents');
      if (!append) {
        setTalents([]);
      }
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [activeFilters, perPage]);

  // Initial load and when filters change
  useEffect(() => {
    fetchTalents(1, activeFilters, false);
  }, [fetchTalents]);

  // Load more function
  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasNextPage) {
      fetchTalents(currentPage + 1, activeFilters, true);
    }
  }, [fetchTalents, currentPage, hasNextPage, isLoadingMore]);

  const filteredTalents = talents;
  const currentIsLoading = isLoading;
  const currentIsError = isError;
  const currentError = error;

  const observer = useRef();
  const lastTalentElementRef = useCallback(
    (node) => {
      if (currentIsLoading || isLoadingMore) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          loadMore();
        }
      });
      if (node) observer.current.observe(node);
    },
    [currentIsLoading, isLoadingMore, hasNextPage, loadMore]
  );

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    const savedSearches = localStorage.getItem('recentTalentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }
  }, []);

  // Handle applying filters
  const handleApplyFilters = (filters) => {
    setActiveFilters(filters);
  };

  // Handle search - improved implementation
  const handleSearch = (term) => {
    const trimmedTerm = term.trim();
    setSearchTerm(trimmedTerm);

    // Update activeFilters with search term
    const searchFilters = {
      ...activeFilters,
    };

    // Only add searchTerm if it's not empty
    if (trimmedTerm) {
      searchFilters.searchTerm = trimmedTerm;
    } else {
      // Remove searchTerm if it exists
      delete searchFilters.searchTerm;
    }

    setActiveFilters(searchFilters);

    // Add to recent searches if term is not empty
    if (trimmedTerm) {
      setRecentSearches((prev) => {
        const newSearches = [trimmedTerm, ...prev.filter((s) => s !== trimmedTerm)].slice(0, 5);
        localStorage.setItem('recentTalentSearches', JSON.stringify(newSearches));
        return newSearches;
      });
      
      // Close search modal after successful search
      setShowSearchModal(false);
    }
  };

  // Handle keyboard navigation in search modal
  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch(searchTerm);
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchTerm('');
    const newFilters = { ...activeFilters };
    delete newFilters.searchTerm;
    setActiveFilters(newFilters);
  };

  // Handle retry
  const handleRetry = () => {
    fetchTalents(1, activeFilters, false);
  };

  // Add this function near the other handlers
  const clearAllFilters = () => {
    setSearchTerm('');
    setActiveFilters({});
  };

  useEffect(() => {
    // Fetch service categories for mapping id to name
    referenceDataApi.getPlatforms().then((res) => {
      const categories = res.data || [];
      setServiceCategories(categories);
      const map = {};
      categories.forEach((cat) => {
        map[cat.id] = cat.name;
      });
      setServiceCategoryMap(map);
    });
  }, []);

  useEffect(() => {
    // Fetch all service types for mapping id to name
    referenceDataApi.getServiceTypes().then(res => {
      setServiceTypes(res.data || []);
      const map = {};
      (res.data || []).forEach(type => { map[type.id] = type.name; });
      setServiceTypeMap(map);
    });
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-950">
      {/* Shared Navigation */}
      <MainNavigation activeItem="/talent" />

      {/* Enhanced Glassmorphic Toolbar */}
      <div className="relative px-4 pt-4 pb-3 flex items-center gap-3 bg-white/60 dark:bg-gray-900/70 backdrop-blur-2xl rounded-3xl shadow-xl border-2 border-white/20 dark:border-indigo-900/40 mx-2 mt-4 mb-2"
        style={{ boxShadow: '0 8px 32px 0 rgba(24,25,38,0.18)' }}>

        <div className="relative z-10 px-5 py-2 flex items-center whitespace-nowrap">
          <h2 className="text-lg font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 dark:from-indigo-400 dark:via-purple-400 dark:to-blue-400 bg-clip-text text-transparent tracking-wide">
            {t('toolbar.recommended')}
          </h2>
        </div>

        <div className="ml-auto flex items-center gap-2 relative z-10">
          {/* Search Button */}
          <button
            onClick={() => setShowSearchModal(true)}
            className="px-3 md:px-5 py-2 text-sm font-bold rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-gray-800 dark:via-blue-900 dark:to-blue-700 text-white flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-105 active:scale-95 transition-all duration-300 border-2 border-white/20 min-w-[44px] min-h-[44px]"
            aria-label={t('aria.searchTalents')}
          >
            <svg className="w-4 h-4 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span className="hidden md:inline ml-1">{t('toolbar.search')}</span>
          </button>

          {/* Filter Button */}
          <button
            onClick={() => setShowFilterModal(true)}
            className="px-3 md:px-5 py-2 text-sm font-bold rounded-full bg-gradient-to-r from-gray-200 via-indigo-100 to-indigo-300 dark:from-gray-800 dark:via-indigo-900 dark:to-indigo-700 text-indigo-700 dark:text-white flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-105 active:scale-95 transition-all duration-300 border-2 border-white/20 min-w-[44px] min-h-[44px]"
            aria-label={t('aria.filterTalents')}
          >
             <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
            </svg>
             <span className="hidden md:inline ml-1">{t('toolbar.filter')}</span>
          </button>
        </div>
      </div>

      {/* Active Filters */}
      {Object.keys(activeFilters).length > 0 && (
        <div className="bg-white dark:bg-gray-900 px-4 py-3 border-t border-gray-100 dark:border-gray-800 animate-fadeIn">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200">{t('filters.active')}</h3>
            <button
              onClick={clearAllFilters}
              className="text-xs text-indigo-600 dark:text-indigo-400 bg-transparent hover:bg-transparent hover:text-indigo-800 dark:hover:text-indigo-300 font-medium flex items-center transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              {t('filters.clearAll')}
            </button>
          </div>

          <div className="flex flex-wrap gap-2">
            {/* Search Term Filter */}
            {activeFilters.searchTerm && (
              <div className="px-3 py-1.5 bg-blue-50 dark:bg-blue-900 rounded-full text-xs flex items-center text-blue-700 dark:text-blue-200 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span className="font-medium">{activeFilters.searchTerm}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, searchTerm: undefined};
                    handleApplyFilters(newFilters);
                    clearSearch();
                  }}
                  className="ml-2 p-0.5 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-500 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-700 hover:text-blue-700 dark:hover:text-blue-100 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Gender Filter */}
            {activeFilters.gender && (
              <div className="px-3 py-1.5 bg-purple-50 rounded-full text-xs flex items-center text-purple-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span className="font-medium">{activeFilters.gender}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, gender: ''};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-purple-100 text-purple-500 hover:bg-purple-200 hover:text-purple-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Experience Level Filter */}
            {activeFilters.experienceLevel && (
              activeFilters.experienceLevel[0] > 1 || activeFilters.experienceLevel[1] < 99
            ) && (
              <div className="px-3 py-1.5 bg-green-50 rounded-full text-xs flex items-center text-green-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <span className="font-medium">LV {activeFilters.experienceLevel[0]}-{activeFilters.experienceLevel[1]}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, experienceLevel: [1, 99]};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-green-100 text-green-500 hover:bg-green-200 hover:text-green-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Price Range Filter */}
            {activeFilters.priceRange && (
              activeFilters.priceRange[0] > 0 || activeFilters.priceRange[1] < 300
            ) && (
              <div className="px-3 py-1.5 bg-yellow-50 rounded-full text-xs flex items-center text-yellow-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-3 h-3 mr-1" />
                <span className="font-medium">{activeFilters.priceRange[0]}-{activeFilters.priceRange[1]} {t('common:currency.credits')}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, priceRange: [0, 300]};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-yellow-100 text-yellow-500 hover:bg-yellow-200 hover:text-yellow-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Race Filter */}
            {activeFilters.raceId && (
              <div className="px-3 py-1.5 bg-red-50 rounded-full text-xs flex items-center text-red-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span className="font-medium">{t('filters.race')}: {activeFilters.raceId}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, raceId: undefined};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-red-100 text-red-500 hover:bg-red-200 hover:text-red-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Languages Filter */}
            {activeFilters.languages && activeFilters.languages?.length > 0 &&
              activeFilters.languages.map(language => (
                <div key={language} className="px-3 py-1.5 bg-indigo-50 rounded-full text-xs flex items-center text-indigo-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                  </svg>
                  <span className="font-medium">{language}</span>
                  <button
                    onClick={() => {
                      const newLanguages = activeFilters.languages?.filter(lang => lang !== language) || [];
                      const newFilters = {...activeFilters, languages: newLanguages};
                      handleApplyFilters(newFilters);
                    }}
                    className="ml-2 p-0.5 rounded-full bg-indigo-100 text-indigo-500 hover:bg-indigo-200 hover:text-indigo-700 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ))}

            {/* Service Category Filter */}
            {activeFilters.serviceCategoryId && (
              <div className="px-3 py-1.5 bg-teal-50 rounded-full text-xs flex items-center text-teal-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg className="w-3 h-3 mr-1 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                <span className="font-medium">{serviceCategoryMap[activeFilters.serviceCategoryId] || activeFilters.serviceCategoryId}</span>
                <button
                  onClick={() => {
                    const newFilters = { ...activeFilters };
                    delete newFilters.serviceCategoryId;
                    delete newFilters.serviceTypeId;
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-teal-100 text-teal-500 hover:bg-teal-200 hover:text-teal-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Service Type Filter */}
            {activeFilters.serviceTypeId && (
              <div className="px-3 py-1.5 bg-indigo-50 rounded-full text-xs flex items-center text-indigo-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg className="w-3 h-3 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                </svg>
                <span className="font-medium">{serviceTypeMap[activeFilters.serviceTypeId] || activeFilters.serviceTypeId}</span>
                <button
                  onClick={() => {
                    const newFilters = { ...activeFilters };
                    delete newFilters.serviceTypeId;
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-indigo-100 text-indigo-500 hover:bg-indigo-200 hover:text-indigo-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Talent Grid */}
      <div className="flex-1 overflow-auto p-3 md:p-6 bg-gray-50 dark:bg-gray-950">
        {currentIsLoading ? (
          <div className="flex items-center justify-center h-full min-h-[300px] bg-white dark:bg-gray-900 rounded-xl shadow-sm">
            <SectionLoader
              type="morph"
              size="large"
              message={isSearchingActive ? t('loader.searching') : t('loader.loading')}
              color="indigo"
              className="text-gray-700 dark:text-indigo-200"
            />
          </div>
        ) : currentIsError ? (
          <div className="text-center p-8 bg-white dark:bg-gray-900 rounded-xl shadow-sm max-w-lg mx-auto">
            <div className="inline-flex p-4 bg-red-50 rounded-full mb-4 text-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              {isSearchingActive ? t('error.searching') : t('error.loading')}
            </h3>
            <p className="text-gray-600 dark:text-gray-300 max-w-md mx-auto mb-6">
              {currentError?.message || t('error.failed')}
            </p>
            <button
              onClick={handleRetry}
              className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-500 dark:from-indigo-800 dark:to-blue-800 text-white rounded-lg hover:from-indigo-700 hover:to-blue-600 dark:hover:from-indigo-900 dark:hover:to-blue-900 transition-all shadow-md hover:shadow-lg transform hover:scale-105 active:scale-100"
            >
              {t('actions.tryAgain')}
            </button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
              {filteredTalents.length > 0 ? (
                filteredTalents.map((talent, index) => (
                  <motion.div
                    key={talent.id}
                    ref={index === filteredTalents.length - 1 ? lastTalentElementRef : null}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <TalentCard talent={talent} />
                  </motion.div>
                ))
              ) : (
                <div className="col-span-full p-10 text-center bg-white dark:bg-gray-900 rounded-xl shadow-sm max-w-lg mx-auto">
                  <div className="inline-flex p-4 bg-gray-100 dark:bg-gray-800 rounded-full mb-4 text-gray-500 dark:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {isSearchingActive ? t('noResults.searchTitle') : t('noResults.title')}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 max-w-md mx-auto mb-6">
                    {isSearchingActive
                      ? t('noResults.searchDescription', { searchTerm })
                      : t('noResults.description')
                    }
                  </p>
                  <button
                    onClick={clearAllFilters}
                    className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-500 dark:from-indigo-800 dark:to-blue-800 text-white rounded-lg hover:from-indigo-700 hover:to-blue-600 dark:hover:from-indigo-900 dark:hover:to-blue-900 transition-all shadow-md hover:shadow-lg transform hover:scale-105 active:scale-100"
                  >
                    {isSearchingActive ? t('noResults.clearSearch') : t('noResults.clearFilters')}
                  </button>
                </div>
              )}
            </div>

            {isLoadingMore && (
              <div className="flex justify-center mt-6 bg-white dark:bg-gray-900 rounded-xl p-4">
                <InlineLoader size="medium" color="indigo" className="text-gray-700 dark:text-indigo-200" />
              </div>
            )}

            {!hasNextPage && filteredTalents.length > 0 && (
              <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                {t('endOfList')}
              </div>
            )}

            {/* Pagination Controls removed for infinite scrolling */}
          </>
        )}
      </div>

      {/* Filter Modal */}
      <FilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        initialFilters={activeFilters}
        onApplyFilters={handleApplyFilters}
      />

      {/* Search Modal */}
      <AnimatePresence>
        {showSearchModal && (
          <div className="fixed inset-0 z-50 overflow-hidden">
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 dark:bg-gray-900/80 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowSearchModal(false)}
            />

            <motion.div
              className="fixed inset-x-0 top-0 bg-white dark:bg-gray-900 rounded-b-3xl overflow-hidden"
              initial={{ y: "-100%" }}
              animate={{ y: 0 }}
              exit={{ y: "-100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
            >
              <div className="container mx-auto px-4 py-4">
                {/* Search Input */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="search"
                    className="w-full pl-10 pr-10 py-3 border border-gray-200 dark:border-gray-800 rounded-xl bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                    placeholder={t('searchModal.placeholder')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={handleSearchKeyPress}
                    autoFocus
                  />
                  {searchTerm && (
                    <button
                      onClick={clearSearch}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Recent Searches */}
                {recentSearches.length > 0 && (
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-300">{t('searchModal.recent')}</h3>
                      <button
                        onClick={() => {
                          setRecentSearches([]);
                          localStorage.removeItem('recentTalentSearches');
                        }}
                        className="text-sm bg-transparent hover:bg-transparent text-indigo-600 dark:text-indigo-400 font-medium"
                      >
                        {t('searchModal.clearAll')}
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {recentSearches.map((term, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            handleSearch(term);
                          }}
                          className="px-3 py-1.5 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-200 font-medium transition-colors flex items-center space-x-1"
                        >
                          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span>{term}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Popular Searches */}
                <div className="mt-6">
                  <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-300 mb-3">{t('searchModal.popular')}</h3>
                  <div className="flex flex-wrap gap-2">
                    {popularSearches.map((term, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          handleSearch(term);
                        }}
                        className="px-3 py-1.5 bg-indigo-50 dark:bg-indigo-900 hover:bg-indigo-100 dark:hover:bg-indigo-800 rounded-full text-sm text-indigo-700 dark:text-indigo-200 font-medium transition-colors flex items-center space-x-1"
                      >
                        <svg className="h-4 w-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
                        </svg>
                        <span>{term}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/talent" />
    </div>
  );
};

export default TalentPage;
