import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import MissionListing from '../components/mission/MissionListing';
import MissionLayout from '../components/mission/MissionLayout';
import useTranslation from '../hooks/useTranslation';
import MissionHeroBanner from '../components/mission/MissionHeroBanner';

import { missionApi } from '../services/missionApi';
import { transformApiMissionsToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';

// This will be our main Mission Page component
const MissionPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('mission');
  const [activeTab, setActiveTab] = useState('browse');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [missions, setMissions] = useState([]);

  // Mission statistics
  const [missionStats, setMissionStats] = useState({
    completed: 0,
    active: 0,
    hosted: 0
  });

  // Fetch missions from API
  useEffect(() => {
    const fetchMissions = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch all missions
        const response = await missionApi.getMissions();

        // Transform API missions to frontend format
        const transformedMissions = transformApiMissionsToFrontend(response.data.data);

        // Update missions state
        setMissions(transformedMissions);

        // Update mission stats
        const openMissions = response.data.data.filter(m => m.status === 'open').length;
        const completedMissions = response.data.data.filter(m => m.status === 'completed').length;

        // For hosted missions, we need to make a separate API call
        // This is a placeholder until we implement the hosted missions API
        const hostedMissions = 0;

        setMissionStats({
          active: openMissions,
          completed: completedMissions,
          hosted: hostedMissions
        });
      } catch (error) {
        console.error('Error fetching missions:', error);
        setError(getApiErrorMessage(error));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissions();
  }, [activeTab]);

  return (
    <MissionLayout
      title={t('listing.title')}
      showBackButton={false}
      useMainNav={true}
      useMissionMobileNav={true}
    >
      {/* Enhanced Mission Hero Banner */}
      <MissionHeroBanner
        stats={missionStats}
        onCreate={() => navigate('/missions/create')}
        onMyMissions={() => navigate('/missions/my-missions')}
        // featuredMissions={...} // Placeholder for future featured missions
      />
      {/* Missions For You Section */}
      {error ? (
        <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-md dark:shadow-indigo-900/30 p-8">
          <div className="text-center">
            <div className="flex flex-col items-center">
              <svg className="w-16 h-16 text-red-300 dark:text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 dark:text-yellow-400 mb-1">{t('landing.errorLoading')}</h3>
              <p className="text-gray-500 dark:text-gray-300 mb-4">{error}</p>
              <motion.button
                onClick={() => window.location.reload()}
                className="px-6 py-2.5 bg-indigo-600 dark:bg-yellow-400 text-white dark:text-gray-900 rounded-lg font-medium hover:bg-indigo-700 dark:hover:bg-yellow-500 transition-colors shadow-md dark:shadow-yellow-400/10"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-4 h-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {t('landing.retry')}
              </motion.button>
            </div>
          </div>
        </div>
      ) : (
        <MissionListing
          missions={missions}
          isLoading={isLoading}
          showViewToggle={false}
          viewToggleOptions={[
            { id: 'browse', label: t('landing.views.browse'), icon: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z', count: missionStats.active }
          ]}
          activeView={activeTab}
          onViewChange={setActiveTab}
        />
      )}
    </MissionLayout>
  );
};

export default MissionPage;
