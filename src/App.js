import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate, Outlet } from 'react-router-dom';
import { LanguageProvider } from './contexts/LanguageContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProfileProvider } from './contexts/ProfileContext';
import RTLProvider from './components/common/RTLProvider';
import EmailVerification from './components/auth/EmailVerification';
import EmailVerificationSuccess from './components/auth/EmailVerificationSuccess';
import EmailVerificationError from './components/auth/EmailVerificationError';
import OAuthCallback from './components/auth/OAuthCallback';
import AuthenticationHub from './components/auth/AuthenticationHub';
import './App.css';
import './styles/animations.css';
import { NotificationProvider } from './context/NotificationContext';
import ToastProvider, { useToast } from './components/common/ToastProvider';
import { PaymentProvider } from './contexts/PaymentContext';
import { OrderPaymentProvider } from './contexts/OrderPaymentContext';
import { HomepageProvider } from './contexts/HomepageContext';
import { FinancialProviders } from './features/FinancialProviders';
import LazyWelcomePage from './components/LazyWelcomePage';
import { LoadingProvider } from './contexts/LoadingContext';
import { PageLoader } from './components/ui/LoadingIndicator';
import ToastContainer from './components/notifications/ToastContainer';
import GiftToastContainer from './components/notifications/GiftToastContainer';
import orderAPI from './services/orderService';
import * as LazyComponents from './lazyComponents';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import queryClient from './config/queryClient';
import EditProfileModal from './components/modals/EditProfileModal';
import TopUpPage from './pages/TopUpPage';
import BecomeTalent from './pages/BecomeTalent';
import { LazyMissionDetailPageHost } from './lazyComponents';
import OrderToast from './components/notifications/OrderToast';
import Chatbot from './components/Chatbot';
import { AIProvider } from './contexts/AIContext';
import HomeSkeleton from './components/home/<USER>';

// Create a protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, initialCheckDone } = useAuth();
  const location = useLocation();

  // For root path, always render children
  if (location.pathname === '/') {
    return children;
  }

  // Show loading while checking authentication status
  if (!initialCheckDone) {
    return <PageLoader message="Authenticating..." color="indigo" />;
  }

  // For all other routes, require authentication
  if (!isAuthenticated) {
    return <Navigate to="/" replace state={{ from: location }} />;
  }

  return children;
};

// Component to conditionally apply padding class
const AppLayout = () => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { success: showSuccessToast, error: showErrorToast, info: showInfoToast } = useToast();
  const authenticatedPaths = ['/home', '/orders', '/talent', '/explore', '/chat', '/profile', '/wallet', '/missions', '/coaches', '/bank-accounts', '/tdash'];
  const isAuthenticatedRoute =
    location.pathname === '/' ||
    authenticatedPaths.some(path => location.pathname.startsWith(path));

  // Handle order accept/reject from toast notifications
  const handleAcceptOrder = async (orderId) => {
    try {
      await orderAPI.respondToOrder(orderId, { action: 'accept' });
      console.log(`✅ Order ${orderId} accepted successfully`);
      showSuccessToast({ title: 'Order Accepted', description: `Order #${orderId} was accepted.` }, 4000);
    } catch (error) {
      console.error(`❌ Failed to accept order ${orderId}:`, error);
      showErrorToast({ title: 'Failed to accept order', description: 'Please try again.' }, 4000);
    }
  };

  const handleRejectOrder = async (orderId) => {
    try {
      await orderAPI.respondToOrder(orderId, { action: 'reject' });
      console.log(`✅ Order ${orderId} rejected successfully`);
      showInfoToast({ title: 'Order Rejected', description: `Order #${orderId} was rejected.` }, 4000);
    } catch (error) {
      console.error(`❌ Failed to reject order ${orderId}:`, error);
      showErrorToast({ title: 'Failed to reject order', description: 'Please try again.' }, 4000);
    }
  };


  return (
    <div className="App">
      <Outlet />
      <Chatbot />
      {/* Order Toast Notifications - only show on authenticated routes */}
      {isAuthenticatedRoute && isAuthenticated && (
        <ToastContainer
          onAcceptOrder={handleAcceptOrder}
          onRejectOrder={handleRejectOrder}
        />
      )}
      {isAuthenticatedRoute && isAuthenticated && <GiftToastContainer />}
    </div>
  );
};

const EditProfileModalWrapper = () => {
  const navigate = useNavigate();
  return (
    <EditProfileModal
      isOpen={true}
      onClose={() => navigate('/profile')}
      initialSection="profile"
    />
  );
};

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <ToastProvider>
          <AuthProvider>
            <NotificationProvider>
              <ProfileProvider>
                <AIProvider>
                  <LanguageProvider>
                    <RTLProvider>
                    <LoadingProvider>
                      <FinancialProviders>
                        <PaymentProvider>
                          <OrderPaymentProvider>
                            <HomepageProvider>
                              <Routes>
                                {/* Public routes - outside AppLayout */}
                                <Route path="/auth" element={<AuthenticationHub />} />
                                <Route path="/login" element={<AuthenticationHub />} />
                                <Route path="/signup" element={<AuthenticationHub initialMode="register" />} />
                                <Route path="/welcome" element={<LazyWelcomePage />} />
                                <Route path="/topup" element={<TopUpPage />} />
                                <Route path="/become-talent" element={<BecomeTalent />} />
                                <Route path="/payment-return" element={<LazyComponents.LazyPaymentReturnPage />} />

                                {/* Protected routes - inside AppLayout */}
                                <Route element={<AppLayout />}>
                                  <Route
                                    index
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<HomeSkeleton />}> 
                                          <LazyComponents.LazyHome />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/home"
                                    element={
                                    <ProtectedRoute>
                                      <Navigate to="/" replace />
                                    </ProtectedRoute>
                                  }
                                />
                                  <Route
                                    path="/games"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading Games..." color="indigo" />}>
                                          <LazyComponents.LazyAllGames />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/talents/:talentId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading Talent..." color="indigo" />}>
                                          <LazyComponents.LazyTalentProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/orders"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyOrderManagement />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/orders/:orderId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyOrderDetails />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/talent"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading Talents..." color="indigo" />}>
                                          <LazyComponents.LazyTalent />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/:userId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/edit-profile"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <EditProfileModalWrapper />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/setup"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfileSetup />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/chat"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyChat />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/chat/:conversationId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyChat />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/explore"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyExplore />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/wallet"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading your Wallet..." color="indigo" />}>
                                          <LazyComponents.LazyWallet />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/wallet/payment-return"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyPaymentReturn />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/payment-return"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyPaymentReturnPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/bank-accounts"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyBankAccountsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/availability"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyAvailabilityPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  {/* Mission routes */}
                                  <Route
                                    path="/missions"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading missions..." color="indigo" />}>
                                          <LazyComponents.LazyMissionPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/my-missions"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading your missions..." color="indigo" />}>
                                          <LazyComponents.LazyMyMissionsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/create"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading your mission..." color="indigo" />}>
                                          <LazyComponents.LazyMissionCreatePage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId/edit"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionEditPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionDetailPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId/applicants"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionApplicantsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  <Route
                                    path="/missions/:missionId/execute"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionExecutionPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  <Route
                                    path="/missions/host/:missionId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyMissionDetailPageHost />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  {/* Authentication Routes */}
                                  <Route path="/email/verify" element={<EmailVerification />} />
                                  <Route path="/email-verification-success" element={<EmailVerificationSuccess />} />
                                  <Route path="/email-verification-error" element={<EmailVerificationError />} />
                                  <Route path="/oauth/:provider/callback" element={<OAuthCallback />} />
                                </Route>
                              </Routes>
                            </HomepageProvider>
                          </OrderPaymentProvider>
                        </PaymentProvider>
                      </FinancialProviders>
                    </LoadingProvider>
                  </RTLProvider>
                </LanguageProvider>
              </AIProvider>
            </ProfileProvider>
          </NotificationProvider>
          </AuthProvider>
        </ToastProvider>
      </Router>
    </QueryClientProvider>
  );
};

export default App;

