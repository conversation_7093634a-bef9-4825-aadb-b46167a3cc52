import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Settings from './Settings';
import GiftManagementHub from './gift/GiftManagementHub';
import profileService from '../services/profileService';
import referralService from '../services/referralService';
import MainNavigation from './navigation/MainNavigation';
import MobileNavigation from './navigation/MobileNavigation';
import { PageLoader, SectionLoader, InlineLoader } from './ui/LoadingIndicator';
import DisputeManagementModal from './modals/DisputeManagementModal';
import MyClientModal from './modals/MyClientModal';
import MyReviewsModal from './modals/MyReviewsModal';
import EditProfileModal from './modals/EditProfileModal';
import ReferralModal from './modals/ReferralModal';
import AvailabilityModal from '../pages/AvailabilityPage';
import { profileKeys } from '../queryKeys/profileKeys';
import disputeService from '../services/disputeService';
import { getCdnUrl } from '../utils/cdnUtils';
import { transformApiMissionsToFrontend } from '../utils/missionTransformers';
import PostView from './PostView';
import MyOrderModal from './modals/MyOrderModal';
import BookmarkedMissionsModal from './modals/BookmarkedMissionsModal';
import useTranslation from '../hooks/useTranslation';

// Loading Skeleton Component
const ProfileSkeleton = () => (
    <div className="animate-pulse space-y-8">
        {/* Cover Photo Skeleton */}
        <div className="h-64 bg-gray-200 rounded-b-2xl" />

        {/* Profile Info Skeleton */}
        <div className="relative px-4 sm:px-6 lg:px-8">
            <div className="absolute -top-16 left-1/2 transform -translate-x-1/2">
                <div className="w-32 h-32 bg-gray-200 rounded-full border-4 border-white" />
            </div>
            <div className="pt-20 text-center">
                <div className="h-8 bg-gray-200 rounded w-48 mx-auto mb-4" />
                <div className="h-4 bg-gray-200 rounded w-64 mx-auto" />
            </div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-2 gap-4 px-4 sm:px-6 lg:px-8">
            <div className="h-24 bg-gray-200 rounded-xl" />
            <div className="h-24 bg-gray-200 rounded-xl" />
        </div>

        {/* Content Sections Skeleton */}
        <div className="space-y-4 px-4 sm:px-6 lg:px-8">
            <div className="h-48 bg-gray-200 rounded-xl" />
            <div className="h-48 bg-gray-200 rounded-xl" />
        </div>
    </div>
);

const Profile = () => {
    const { t } = useTranslation(['profile', 'common']);
    const navigate = useNavigate();
    const location = useLocation();
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [coverOffset, setCoverOffset] = useState(0);
    const [useMockData, setUseMockData] = useState(false);
    const [showSettings, setShowSettings] = useState(false);
    const [showGiftInventory, setShowGiftInventory] = useState(false);

    // Add state for cover media carousel
    const [currentCoverIndex, setCurrentCoverIndex] = useState(0);

    // New state for API data sections
    const [likedPosts, setLikedPosts] = useState([]);
    const [bookmarkedMissions, setBookmarkedMissions] = useState([]);
    const [loadingLikedPosts, setLoadingLikedPosts] = useState(false);
    const [loadingBookmarks, setLoadingBookmarks] = useState(false);
    const [errorLikedPosts, setErrorLikedPosts] = useState(null);
    const [errorBookmarks, setErrorBookmarks] = useState(null);


    // Dispute Management Modal State
    const [isDisputeModalOpen, setIsDisputeModalOpen] = useState(false);
    const [userDisputes, setUserDisputes] = useState([]);
    const [loadingDisputes, setLoadingDisputes] = useState(false);
    const [errorDisputes, setErrorDisputes] = useState(null);

    // My Clients (Mission Management) State
    const [isClientsModalOpen, setIsClientsModalOpen] = useState(false);
    const [userMissions, setUserMissions] = useState([]);
    const [loadingMissions, setLoadingMissions] = useState(false);
    const [errorMissions, setErrorMissions] = useState(null);
    const [missionApplicants, setMissionApplicants] = useState({});
    const [loadingApplicants, setLoadingApplicants] = useState({});

    // My Reviews Modal State
    const [isReviewsModalOpen, setIsReviewsModalOpen] = useState(false);

    // Availability Modal State
    const [isAvailabilityModalOpen, setIsAvailabilityModalOpen] = useState(false);

    // Edit Profile Modal State
    const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);
    const [editSection, setEditSection] = useState('profile');

    // Voice Note State
    const [voiceNoteUrl, setVoiceNoteUrl] = useState(null);
    const [isPlayingVoiceNote, setIsPlayingVoiceNote] = useState(false);
    const [voiceNoteError, setVoiceNoteError] = useState(null);
    const [currentAudio, setCurrentAudio] = useState(null);

    const [isReferralModalOpen, setIsReferralModalOpen] = useState(false);
    const [referralStats, setReferralStats] = useState({
        totalReferrals: 0,
        activeReferrals: 0,
        pointsEarned: 0,
        recentActivity: []
    });
    const [referralLoading, setReferralLoading] = useState(false);
    const [referralError, setReferralError] = useState(null);

    const [showLikedPostsModal, setShowLikedPostsModal] = useState(false);
    const [selectedPostId, setSelectedPostId] = useState(null);
    const [isPostViewOpen, setIsPostViewOpen] = useState(false);

    const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);
    const [pendingOrdersCount, setPendingOrdersCount] = useState(0);
    const [pendingOrdersLoading, setPendingOrdersLoading] = useState(false);

    const [showBookmarkedMissionsModal, setShowBookmarkedMissionsModal] = useState(false);

    useEffect(() => {
        const params = new URLSearchParams(window.location.search);
        if (params.get('orders') === 'true') {
            setIsOrderModalOpen(true);
        }
        if (params.get('clients') === 'true') {
            setIsClientsModalOpen(true);
            params.delete('clients');
            window.history.replaceState({}, '', `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`);
        }
        if (params.get('availability') === 'true') {
            setIsAvailabilityModalOpen(true);
            params.delete('availability');
            window.history.replaceState({}, '', `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`);
        }
        if (params.get('edit') === 'services') {
            setEditSection('services');
            setIsEditProfileModalOpen(true);
            params.delete('edit');
            window.history.replaceState({}, '', `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`);
        }
    }, []);

    // Listen for external events to open modals while already on the page
    useEffect(() => {
        const openClients = () => setIsClientsModalOpen(true);
        const openOrders = () => setIsOrderModalOpen(true);
        const openAvailability = () => setIsAvailabilityModalOpen(true);
        const openServices = () => { setEditSection('services'); setIsEditProfileModalOpen(true); };
        const openSettings = () => setShowSettings(true);

        window.addEventListener('openClientsModal', openClients);
        window.addEventListener('openOrdersModal', openOrders);
        window.addEventListener('openAvailabilityModal', openAvailability);
        window.addEventListener('openServicesModal', openServices);
        window.addEventListener('openSettingsModal', openSettings);

        return () => {
            window.removeEventListener('openClientsModal', openClients);
            window.removeEventListener('openOrdersModal', openOrders);
            window.removeEventListener('openAvailabilityModal', openAvailability);
            window.removeEventListener('openServicesModal', openServices);
            window.removeEventListener('openSettingsModal', openSettings);
        };
    }, []);

    // React Query setup
    const queryClient = useQueryClient();
    const profileQueryKey = profileKeys.detail();

    const { data: profileQueryData, isLoading: isProfileLoading } = useQuery({
        queryKey: profileQueryKey,
        queryFn: () => profileService.getCompleteProfile(),
        staleTime: 5 * 60 * 1000,
        cacheTime: 30 * 60 * 1000
    });

    useEffect(() => {
        setLoading(isProfileLoading);
    }, [isProfileLoading]);

    useEffect(() => {
        if (profileQueryData && profileQueryData.success) {
            processProfileData(profileQueryData.data);
        } else if (profileQueryData && profileQueryData.error) {
            setError(profileQueryData.error);
        }
    }, [profileQueryData]);

    // Handle Scroll for Parallax Effect
    useEffect(() => {
        let timeoutId;
        const handleScroll = () => {
            if (timeoutId) return;
            timeoutId = setTimeout(() => {
                const offset = window.pageYOffset;
                setCoverOffset(offset * 0.5); // Parallax effect
                timeoutId = null;
            }, 100);
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
        return () => {
            window.removeEventListener('scroll', handleScroll);
            if (timeoutId) clearTimeout(timeoutId);
        };
    }, []);

    const processProfileData = (data) => {
        if (!data) return;

        const levelInfo = data.level || { level: 1 };
        const currentLevel = levelInfo.level || 1;

        const profileMedia = data.profile_media || {};
        let coverMedia = [];
        let coverMediaType = 'image';
        
        // Process all cover media for carousel
        if (profileMedia.video) {
            coverMedia.push({
                type: 'video',
                url: getCdnUrl(profileMedia.video),
                order: 1
            });
            coverMediaType = 'video';
        }
        
        if (profileMedia.photos && profileMedia.photos.length > 0) {
            // Sort photos by order
            const sortedPhotos = [...profileMedia.photos].sort((a, b) => (a.order || 0) - (b.order || 0));
            
            sortedPhotos.forEach((photo, index) => {
                if (photo && photo.path) {
                coverMedia.push({
                    type: 'image',
                        url: getCdnUrl(photo.path),
                    order: photo.order || index + 1
                });
                }
            });
        }

        // If no cover media, use fallback
        if (coverMedia.length === 0) {
            coverMedia = [{
                type: 'image',
                url: null,
                order: 1
            }];
        }

        let profilePictureUrl = null;
        if (data.profile_picture) {
            profilePictureUrl = getCdnUrl(data.profile_picture);
        }

        let voiceNoteUrl = null;
        if (data.voice_note) {
            voiceNoteUrl = getCdnUrl(data.voice_note);
        }

        setVoiceNoteUrl(voiceNoteUrl);
        setVoiceNoteError(null);

        const formattedServices = (data.services || []).map(service => ({
            ...service,
            title: service.name,
            description: service.description || 'No description available.',
            price: service.rates && service.rates.length > 0 ? service.rates[0].price : (service.price || null)
        }));

        const userObject = {
            uid: data.uid,
            id: data.id,
            name: data.nickname || data.name,
            nickname: data.nickname,
            email: data.email,
            mobile_number: data.mobile_number,
            country_code: data.country_code,
            gender: data.gender,
            date_of_birth: data.date_of_birth,
            profilePicture: profilePictureUrl,
            coverMedia: coverMedia, // Store all cover media
            coverMediaType: coverMediaType,
            level: currentLevel,
            experience_points: data.experience || 0,
            is_verified: data.is_verified || false,
            is_talent: data.role === 'talent',
            biography: data.biography,
            race: data.race,
            height: data.height,
            weight: data.weight,
            personalities: data.personalities || [],
            languages: data.default_language || [],
            services: formattedServices,
            constellation: data.constellation,
            referral_code: data.referral_code,
            stats: {
                exp_points: data.experience || 0,
                total_missions_completed: data.missions?.length || 0,
                total_missions_hosted: data.hosted_missions?.length || 0,
                total_hours_played: data.total_hours_played || 0,
                total_earnings: data.total_earnings || 0,
                average_rating: data.average_rating || 0,
                total_followers: data.total_followers || 0,
                bookmarked_missions: data.bookmarked_missions || [],
                liked_posts: data.social_posts || []
            }
        };

        setUser(userObject);
        setUseMockData(false);
        // Liked posts will be fetched from dedicated endpoint
        setUserMissions(data.missions || []);
        setBookmarkedMissions(data.bookmarked_missions || []);
    };

    // Function to fetch user's liked posts from backend API
    const fetchLikedPosts = async () => {
        setLoadingLikedPosts(true);
        setErrorLikedPosts(null);

        try {
            const response = await profileService.getLikedPosts();
            const posts = Array.isArray(response.data) ? response.data : (response.data?.data || []);
            setLikedPosts(posts);
        } catch (error) {
            setErrorLikedPosts('Failed to load liked posts');
            setLikedPosts([]);
        } finally {
            setLoadingLikedPosts(false);
        }
    };

    // Function to fetch user's bookmarked missions from backend API
    const fetchBookmarkedMissions = async () => {
        setLoadingBookmarks(true);
        setErrorBookmarks(null);

        try {
            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/user/bookmarked-missions`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Handle both array and paginated response formats
            const missions = Array.isArray(data) ? data : (data.data || []);
            const transformed = transformApiMissionsToFrontend(missions);
            setBookmarkedMissions(transformed.slice(0, 5)); // Show only first 5 missions
        } catch (error) {
            console.error('Error fetching bookmarked missions:', error);
            setErrorBookmarks('Failed to load bookmarked missions');
            setBookmarkedMissions([]); // Set empty array on error
        } finally {
            setLoadingBookmarks(false);
        }
    };

    // Function to fetch user's disputes from backend API
    const fetchUserDisputes = async () => {
        setLoadingDisputes(true);
        setErrorDisputes(null);

        try {
            const response = await disputeService.getUserDisputes();
            if (response.success) {
                const disputes = Array.isArray(response.data) ? response.data : [];
                setUserDisputes(disputes);
            } else {
                throw new Error(response.error || 'Failed to load disputes');
            }
        } catch (error) {
            console.error('Error fetching user disputes:', error);
            setErrorDisputes('Failed to load disputes');
            setUserDisputes([]); // Set empty array on error
        } finally {
            setLoadingDisputes(false);
        }
    };

    // Function to fetch user's created missions (for My Clients)
    const fetchUserMissions = async () => {
        setLoadingMissions(true);
        setErrorMissions(null);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('No authentication token found, using fallback data');
                setUserMissions([]);
                return;
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/my-missions`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn('Missions API returned status:', response.status, '. Using empty array.');
                setUserMissions([]);
                return;
            }

            const data = await response.json();
            setUserMissions(data.data || []);
        } catch (error) {
            console.error('Error fetching user missions:', error);
            setErrorMissions('Failed to load missions. Please try again.');
            setUserMissions([]);
        } finally {
            setLoadingMissions(false);
        }
    };

    // Function to fetch applicants for a specific mission
    const fetchMissionApplicants = async (missionId) => {
        setLoadingApplicants(prev => ({ ...prev, [missionId]: true }));

        try {
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping applicants fetch');
                setMissionApplicants(prev => ({
                    ...prev,
                    [missionId]: []
                }));
                return;
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/applicants`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Applicants API returned status: ${response.status} for mission ${missionId}. Using empty array.`);
                setMissionApplicants(prev => ({
                    ...prev,
                    [missionId]: []
                }));
                return;
            }

            const data = await response.json();

            // Handle both array and paginated response formats
            const applicants = Array.isArray(data) ? data : (data.data || []);
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: Array.isArray(applicants) ? applicants : []
            }));
        } catch (error) {
            console.error(`Error fetching applicants for mission ${missionId}:`, error);
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: []
            }));
        } finally {
            setLoadingApplicants(prev => ({ ...prev, [missionId]: false }));
        }
    };

    // Function to approve an applicant
    const approveApplicant = async (missionId, applicant) => {
        try {
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, cannot approve applicant');
                return { success: false, error: 'Authentication required' };
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/applicants/${applicant.child_id}/approve`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Approve API returned status: ${response.status}`);
                return { success: false, error: `Server returned ${response.status}` };
            }

            const data = await response.json();

            // Update local state
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: prev[missionId]?.map(a =>
                    a.id === applicant.id
                        ? { ...applicant, status: 'approved' }
                        : a
                ) || []
            }));

            return { success: true, data };
        } catch (error) {
            console.error('Error approving applicant:', error);
            return { success: false, error: error.message };
        }
    };

    // Function to reject an applicant
    const rejectApplicant = async (missionId, applicant) => {
        try {
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, cannot reject applicant');
                return { success: false, error: 'Authentication required' };
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/applicants/${applicant.child_id}/reject`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Reject API returned status: ${response.status}`);
                return { success: false, error: `Server returned ${response.status}` };
            }

            const data = await response.json();

            // Update local state
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: prev[missionId]?.map(a =>
                    a.id === applicant.id
                        ? { ...a, status: 'rejected' }
                        : a
                ) || []
            }));

            return { success: true, data };
        } catch (error) {
            console.error('Error rejecting applicant:', error);
            return { success: false, error: error.message };
        }
    };

    // Initial data fetch using new all-profile endpoint
    useEffect(() => {
        if (!profileQueryData || !profileQueryData.success) return;
        const data = profileQueryData.data;

        // Always fetch liked posts using dedicated endpoint
        fetchLikedPosts();
        // Fallback to individual endpoints only if data is missing
        if (!Array.isArray(data.bookmarked_missions)) {
            fetchBookmarkedMissions();
        }
        if (!Array.isArray(data.missions)) {
            fetchUserMissions();
        }

    }, [profileQueryData]);

    // Listen for navigation events to refresh data when returning from edit profile
    useEffect(() => {
        // This will run when the component mounts
        const handleFocus = () => {
            // Don't refresh data if any modal is open (especially KYC modal)
            const hasOpenModal = document.querySelector('[role="dialog"]') ||
                                document.querySelector('.fixed.inset-0') ||
                                showSettings;

            if (!hasOpenModal) {
                queryClient.invalidateQueries(profileQueryKey);
            } else {
            }
        };

        // Add event listener for when the window regains focus
        window.addEventListener('focus', handleFocus);

        // Clean up the event listener when the component unmounts
        return () => {
            window.removeEventListener('focus', handleFocus);
        };
    }, [showSettings]);

    const handleEditProfile = () => {
        // Open the EditProfile modal
        setIsEditProfileModalOpen(true);
    };

    const handleProfileUpdated = (updatedProfile) => {
        if (updatedProfile && user) {
            const updatedUser = { ...user };

            // Update profile picture if it was changed
            if (updatedProfile.profile_picture !== undefined) {
                if (updatedProfile.profile_picture) {
                    if (updatedProfile.profile_picture.startsWith('http')) {
                        updatedUser.profilePicture = updatedProfile.profile_picture;
                    } else {
                        // Use a more reliable cache-busting mechanism
                        const timestamp = new Date().getTime();
                        const randomString = Math.random().toString(36).substring(7);
                        updatedUser.profilePicture = `${process.env.REACT_APP_CDN_URL}/${updatedProfile.profile_picture}?t=${timestamp}&r=${randomString}`;
                    }
                } else {
                    updatedUser.profilePicture = null;
                }
            }

            // Update other basic fields if they were changed
            if (updatedProfile.nickname !== undefined) {
                updatedUser.nickname = updatedProfile.nickname;
                updatedUser.name = updatedProfile.nickname;
            }

            if (updatedProfile.biography !== undefined) {
                updatedUser.biography = updatedProfile.biography;
            }

            if (updatedProfile.gender !== undefined) {
                updatedUser.gender = updatedProfile.gender;
            }

            if (updatedProfile.date_of_birth !== undefined) {
                updatedUser.date_of_birth = updatedProfile.date_of_birth;
            }

            if (updatedProfile.height !== undefined) {
                updatedUser.height = updatedProfile.height;
            }

            if (updatedProfile.weight !== undefined) {
                updatedUser.weight = updatedProfile.weight;
            }

            if (updatedProfile.email !== undefined) {
                updatedUser.email = updatedProfile.email;
            }

            if (updatedProfile.voice_note !== undefined) {
                if (updatedProfile.voice_note) {
                    const url = updatedProfile.voice_note.startsWith('http')
                        ? updatedProfile.voice_note
                        : `${process.env.REACT_APP_CDN_URL}/${updatedProfile.voice_note}`;
                    setVoiceNoteUrl(url);
                } else {
                    setVoiceNoteUrl(null);
                }
            }

            // Handle cover media updates - now using the new coverMedia array structure
            if (updatedProfile.media || updatedProfile.profile_media) {
                const mediaData = updatedProfile.media || updatedProfile.profile_media;
                if (mediaData) {
                    let newCoverMedia = [];
                    let coverMediaType = 'image';
                    
                    // Process video if present
                    if (mediaData.video) {
                        newCoverMedia.push({
                            type: 'video',
                            url: `${process.env.REACT_APP_CDN_URL}/${mediaData.video}`,
                            order: 1
                        });
                        coverMediaType = 'video';
                    }
                    
                    // Process photos if present
                    if (mediaData.photos && mediaData.photos.length > 0) {
                        const sortedPhotos = [...mediaData.photos].sort((a, b) => (a.order || 0) - (b.order || 0));
                        
                        sortedPhotos.forEach((photo, index) => {
                            newCoverMedia.push({
                                type: 'image',
                                url: `${process.env.REACT_APP_CDN_URL}/${photo.path}`,
                                order: photo.order || index + 1
                            });
                        });
                    }
                    
                    // Update the user object with new cover media
                    updatedUser.coverMedia = newCoverMedia;
                    updatedUser.coverMediaType = coverMediaType;
                    
                    // Reset carousel index to 0 when media changes
                    setCurrentCoverIndex(0);
                }
            }

            setUser(updatedUser);

            // Refresh cached profile data
            queryClient.invalidateQueries(profileQueryKey);
        }
    };

    // Implement lazy loading for gifts
    const handleOpenGiftInventory = () => {
        setShowGiftInventory(true);
    };

    // Implement lazy loading for disputes
    const handleOpenDisputeModal = async () => {
        if (isDisputeModalOpen) return; // Prevent duplicate calls
        
        setIsDisputeModalOpen(true);
        await fetchUserDisputes();
    };

    const handleOpenClientsModal = () => {
        setIsClientsModalOpen(true);
    };

    const handleOpenAvailabilityModal = () => {
        setIsAvailabilityModalOpen(true);
    };

    const handleOpenReviewsModal = () => {
        setIsReviewsModalOpen(true);
    };

    const handleOpenReferralModal = async () => {
        setIsReferralModalOpen(true);
        setReferralLoading(true);
        setReferralError(null);
        
        try {
            const response = await referralService.getReferralStats();
            if (response.success) {
                setReferralStats(response.data);
            } else {
                throw new Error(response.error || 'Failed to fetch referral statistics');
            }
        } catch (error) {
            console.error('Error fetching referral stats:', error);
            setReferralError(error.message || 'Failed to fetch referral statistics');
        } finally {
            setReferralLoading(false);
        }
    };

    const handleCloseReferralModal = () => {
        setIsReferralModalOpen(false);
    };

    // Voice Note Handlers
    const handlePlayVoiceNote = () => {
        if (!voiceNoteUrl) {
            setVoiceNoteError('No voice note available');
            return;
        }

        // If already playing, stop current audio
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            setCurrentAudio(null);
            setIsPlayingVoiceNote(false);
            return;
        }

        setIsPlayingVoiceNote(true);
        setVoiceNoteError(null);

        // Create audio element and play
        const audio = new Audio(voiceNoteUrl);
        setCurrentAudio(audio);

        audio.onloadstart = () => {
            // voice note starts loading
        };

        audio.oncanplay = () => {
            // voice note ready to play
        };

        audio.onended = () => {
            setIsPlayingVoiceNote(false);
            setCurrentAudio(null);
        };

        audio.onerror = (e) => {
            console.error('❌ Voice note playback error:', e);
            console.error('Audio error details:', {
                error: e.target.error,
                networkState: e.target.networkState,
                readyState: e.target.readyState,
                src: e.target.src
            });
            setIsPlayingVoiceNote(false);
            setCurrentAudio(null);
            setVoiceNoteError('Failed to play voice note');
        };

        audio.onpause = () => {
            setIsPlayingVoiceNote(false);
        };

        audio.play().catch((error) => {
            console.error('❌ Error playing voice note:', error);
            setIsPlayingVoiceNote(false);
            setCurrentAudio(null);
            setVoiceNoteError('Failed to play voice note. Please check your internet connection.');
        });
    };

    const handleStopVoiceNote = () => {
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            setCurrentAudio(null);
        }
        setIsPlayingVoiceNote(false);
        setVoiceNoteError(null);
    };

    // Cleanup audio on component unmount
    useEffect(() => {
        return () => {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio.currentTime = 0;
            }
        };
    }, [currentAudio]);

    // Add carousel navigation functions
    const nextCoverMedia = () => {
        if (user && user.coverMedia && user.coverMedia.length > 1) {
            setCurrentCoverIndex((prev) => (prev + 1) % user.coverMedia.length);
        }
    };

    const prevCoverMedia = () => {
        if (user && user.coverMedia && user.coverMedia.length > 1) {
            setCurrentCoverIndex((prev) => (prev - 1 + user.coverMedia.length) % user.coverMedia.length);
        }
    };

    // Auto-advance carousel
    useEffect(() => {
        if (user && user.coverMedia && user.coverMedia.length > 1) {
            const interval = setInterval(() => {
                setCurrentCoverIndex((prev) => (prev + 1) % user.coverMedia.length);
            }, 8000); // Change every 8 seconds

            return () => clearInterval(interval);
        }
    }, [user]);

    // Handle automatic modal opening from navigation state
    useEffect(() => {
        if (location.state?.openMyClientsModal && !isClientsModalOpen) {
            setIsClientsModalOpen(true);
            // Clear the state to prevent reopening on subsequent renders
            navigate(location.pathname, { replace: true, state: {} });
        }
    }, [location.state, isClientsModalOpen, navigate, location.pathname]);

    const handlePostClick = (postId) => {
        setSelectedPostId(postId);
        setIsPostViewOpen(true);
    };

    const handleMissionClick = (missionId) => {
        navigate(`/missions/${missionId}`);
    };

    const handleClosePostView = () => {
        setIsPostViewOpen(false);
        setSelectedPostId(null);
    };

    if (loading) {
        return <PageLoader message="Loading your profile..." color="purple" />;
    }

    if (!user) {
        return (
            <>
                <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-indigo-50 to-white">
                    <div className="text-center">
                        <p className="text-gray-700 text-lg" role="alert">{t('view.errorLoadingProfile')}</p>
                        <button
                            onClick={() => {
                                // Create fallback data
                                const fallbackUser = {
                                    id: 1,
                                    first_name: 'Demo',
                                    last_name: 'User',
                                    name: 'Demo User',
                                    nickname: 'DemoUser',
                                    email: '<EMAIL>',
                                    mobile_number: '60123456789',
                                    country_code: '+60',
                                    gender: 'other',
                                    date_of_birth: '2000-01-01',
                                    profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg',
                                    coverPhoto: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
                                    level: 10,
                                    experience_points: 1500,
                                    is_verified: true,
                                    is_talent: true,
                                    biography: "Hi, I'm a demo user for testing purposes. This is a sample biography that shows what your profile could look like with real data.",
                                    stats: {
                                        wallet_balance: 5000,
                                        exp_points: 1500,
                                        bookmarked_missions: [
                                            { id: 1, title: 'Demo Mission 1', date: '2023-05-15' },
                                            { id: 2, title: 'Demo Mission 2', date: '2023-05-20' }
                                        ],
                                        liked_posts: [
                                            { id: 1, title: 'Demo Post 1', date: '2023-05-10' },
                                            { id: 2, title: 'Demo Post 2', date: '2023-05-18' }
                                        ]
                                    }
                                };

                                setUser(fallbackUser);
                                setUseMockData(true);
                            }}
                            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                            aria-label={t('view.loadDemoData')}
                        >
                            {t('view.loadDemoData')}
                        </button>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            {/* Enhanced Background with Multiple Gradients */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                {/* Animated Background Layers */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-gray-950 dark:via-gray-900 dark:to-gray-900" />
                <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/50 via-transparent to-emerald-50/50 dark:from-gray-900/80 dark:via-transparent dark:to-gray-900/80" />

                {/* Floating Background Elements */}
                <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse" />
                <div className="absolute top-40 right-20 w-96 h-96 bg-gradient-to-br from-pink-200/20 to-rose-200/20 rounded-full blur-3xl animate-pulse delay-1000" />
                <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-200/25 rounded-full blur-3xl animate-pulse delay-2000" />
            </div>

            {/* Shared Navigation */}
            <MainNavigation activeItem="/profile" />

            {/* Enhanced Profile Header with Modern Design */}
            <div className="relative h-[70vh] overflow-hidden">
                {/* Cover Photo Background with Parallax */}
                <motion.div
                    className="absolute inset-0"
                    style={{ y: coverOffset }}
                >
                    {user.coverMedia && user.coverMedia.length > 0 ? (
                        user.coverMedia[currentCoverIndex].type === 'video' ? (
                            <video
                                src={user.coverMedia[currentCoverIndex].url}
                                className="w-full h-full object-cover"
                                autoPlay
                                muted
                                loop
                                playsInline
                                preload="auto"
                                poster={`${user.coverMedia[currentCoverIndex].url}?quality=high`}
                                onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.nextSibling.style.display = 'block';
                                }}
                            />
                        ) : (
                            <img
                                src={`${user.coverMedia[currentCoverIndex].url}?quality=high&w=1920&h=1080&fit=crop&auto=format`}
                                alt="Profile Cover"
                                className="w-full h-full object-cover"
                                loading="eager"
                                decoding="async"
                                fetchpriority="high"
                                onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.nextSibling.style.display = 'block';
                                }}
                            />
                        )
                    ) : null}
                    {/* Fallback gradient background */}
                    <div
                        className={`w-full h-full bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-800 ${user.coverMedia && user.coverMedia.length > 0 ? 'hidden' : 'block'}`}
                        style={{ display: user.coverMedia && user.coverMedia.length > 0 ? 'none' : 'block' }}
                    />

                    {/* Simple overlay for text readability */}
                    <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/60" />

                    {/* Carousel Navigation Controls */}
                    {user.coverMedia && user.coverMedia.length > 1 && (
                        <>
                            {/* Previous Button */}
                            <button
                                onClick={prevCoverMedia}
                                className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-300 hover:scale-110"
                                aria-label="Previous cover media"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                                </svg>
                            </button>

                            {/* Next Button */}
                            <button
                                onClick={nextCoverMedia}
                                className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-300 hover:scale-110"
                                aria-label="Next cover media"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>

                            {/* Media Counter */}
                            <div className="absolute top-4 right-4 z-10 px-3 py-1 bg-black/50 text-white text-sm rounded-full">
                                {currentCoverIndex + 1} / {user.coverMedia.length}
                            </div>
                        </>
                    )}
                </motion.div>

                {/* Floating particles animation */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    {[...Array(8)].map((_, i) => (
                        <motion.div
                            key={i}
                            className="absolute w-2 h-2 bg-white/30 rounded-full"
                            style={{
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                            }}
                            animate={{
                                y: [-20, -120],
                                opacity: [0, 1, 0],
                                scale: [0.8, 1.2, 0.8],
                            }}
                            transition={{
                                duration: 4 + Math.random() * 3,
                                repeat: Infinity,
                                delay: Math.random() * 3,
                                ease: "easeInOut"
                            }}
                        />
                    ))}
                </div>

                {/* Profile Content */}
                <motion.div
                    className="absolute bottom-0 left-0 right-0 text-white p-6 md:p-8"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                >
                    <div className="max-w-7xl mx-auto">
                        <div className="flex flex-col md:flex-row items-center md:items-end space-y-6 md:space-y-0 md:space-x-8">
                            {/* Profile Picture with Enhanced Design */}
                            <motion.div
                                className="relative group"
                                whileHover={{ scale: 1.05 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <div className="relative">
                                    {/* Glowing ring effect */}
                                    <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full opacity-75 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />

                                    {/* Profile picture container */}
                                    <div className="relative w-40 h-40 md:w-44 md:h-44 rounded-full overflow-hidden border-4 border-white shadow-2xl">
                                        <img
                                            src={`${user.profilePicture || '/default-avatar.png'}?quality=high&w=400&h=400&fit=crop&auto=format`}
                                            alt={user.nickname || user.name}
                                            className="w-full h-full object-cover aspect-square group-hover:scale-110 transition-transform duration-500"
                                            key={`profile-pic-${user.profilePicture || 'default'}`} // use stable key to avoid unnecessary reloads
                                            loading="eager" // Prioritize loading
                                            decoding="async" // Optimize decoding
                                            fetchpriority="high"
                                            onError={(e) => {
                                                e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.nickname || user.name)}&background=6366f1&color=ffffff&size=1000&quality=high`;
                                            }}
                                        />
                                    </div>

                                    {/* Level badge */}
                                    <motion.div
                                        className="absolute -bottom-3 -right-3 px-4 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-white text-sm font-bold rounded-2xl shadow-xl border border-white"
                                        whileHover={{ scale: 1.1 }}
                                        transition={{ type: "spring", stiffness: 400 }}
                                    >
                                        <div className="flex items-center space-x-1">
                                            <span className="text-lg font-extrabold">LV {user.level}</span>
                                        </div>
                                    </motion.div>
                                </div>
                            </motion.div>

                            {/* User Info and Edit Button in two columns */}
                            <div className="flex-1 flex flex-col md:flex-row md:items-center md:justify-between w-full">
                            {/* User Info Section */}
                                <div className="text-center md:text-left space-y-3 md:space-y-0 md:mr-8 flex-1">
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.6, delay: 0.2 }}
                                >
                                        <div className="relative flex flex-col items-center md:items-start">
                                            {/* UID above nickname, smaller and lighter, glassy pill with icon */}
                                            <div className="mb-2">
                                                <span className="inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-white/10 to-indigo-400/10 backdrop-blur-md border border-white/20 rounded-full shadow-md text-sm text-gray-100/80 tracking-widest font-mono transition hover:bg-indigo-500/20">
                                                    <svg className="w-4 h-4 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 20 20">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.121 17.804A13.937 13.937 0 0110 16c1.657 0 3.24.267 4.879.804M15 10a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    </svg>
                                                    UID: {user.uid || user.id}
                                                </span>
                                            </div>
                                            {/* Nickname as main heading, gradient, shadow, badge next to name */}
                                            <div className="flex flex-col md:flex-row md:items-center gap-2">
                                                <h1 className="text-4xl md:text-6xl font-extrabold bg-gradient-to-r from-indigo-200 via-white to-purple-300 bg-clip-text text-transparent drop-shadow-lg dark:from-indigo-300 dark:via-white dark:to-purple-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                                {user.nickname || user.name}
                                            </h1>
                                    {user.is_verified && (
                                                    <span className="inline-flex items-center px-2 py-1 bg-blue-500/30 border border-blue-400/50 rounded-full ml-0 md:ml-2">
                                            <svg className="w-4 h-4 text-blue-300 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                                        <span className="text-xs text-blue-200 font-medium">{t('view.verified')}</span>
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        {/* Biography in a blurred, translucent card with accent and quote icon */}
                                        <motion.div
                                            className="relative mt-4 max-w-2xl"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.6, delay: 0.3 }}
                                >
                                            <div className="flex items-start gap-3">
                                                <div className="flex-1 bg-white/10 backdrop-blur-md rounded-2xl px-6 py-4 shadow-inner border border-white/10">
                                                    <p className="text-lg md:text-xl text-gray-200 leading-relaxed">
                                    {user.biography || t('view.noBiography')}
                                                    </p>
                            </div>
                                            </div>
                                        </motion.div>
                                    </motion.div>
                                </div>
                                {/* Action Buttons (Edit Profile) */}
                                <div className="flex justify-center md:justify-end items-center mt-6 md:mt-0 md:ml-8">
                            <motion.div
                                className="flex flex-col space-y-3 w-full"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                            >
                                {/* View Public Profile Button */}
                                <motion.button
                                    onClick={() => {
                                        if (user?.id) {
                                            navigate(`/talents/${user.id}`);
                                        }
                                    }}
                                    className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 border border-blue-400 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 font-medium"
                                    whileHover={{ scale: 1.05, y: -2 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <div className="flex items-center justify-center space-x-2">
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <span>My Space</span>
                                    </div>
                                </motion.button>
                                {/* Edit Profile Button */}
                                <motion.button
                                    onClick={handleEditProfile}
                                    className="w-full px-6 py-3 bg-black/30 border border-white/30 text-white rounded-xl hover:bg-black/50 transition-all duration-300 font-medium"
                                    whileHover={{ scale: 1.05, y: -2 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <div className="flex items-center justify-center space-x-2">
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        <span>{t('common.edit')}</span>
                                    </div>
                                </motion.button>
                            </motion.div>
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>
            </div>

            {/* Enhanced Main Content Container */}
            <div className="relative max-w-7xl rounded-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12 dark:bg-gray-950">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-8">

                        {/* Quick Sections Heading */}
                        <motion.div
                            className="flex items-center space-x-3 mb-4"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.1 }}
                        >
                            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </div>
                            <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent dark:from-indigo-300 dark:to-purple-400 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                {t('view.quickSections')}
                            </h2>
                        </motion.div>

                        {/* Quick Actions Grid */}
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
                            {/* Row 1 */}
                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenClientsModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.myClients')}</h3>
                                        <div className="text-sm text-gray-500">
                                            {loadingMissions ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="blue" />
                                                    <span>{t('common.loading')}</span>
                                                </span>
                                            ) :
                                             errorMissions ? t('common.notifications.error') :
                                             !Array.isArray(userMissions) || userMissions.length === 0 ? t('view.quick.noMissions') :
                                             t('view.quick.missionCount', { count: userMissions.length })}
                                        </div>
                                    </div>
                                </div>
                            </motion.div>

                            {/* My Orders Card - make this open the modal */}
                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={() => setIsOrderModalOpen(true)}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                        <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.myOrders')}</h3>
                                        <p className="text-sm text-gray-500">
                                            {pendingOrdersLoading ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="green" />
                                                    <span>{t('common.loading')}</span>
                                                </span>
                                            ) : (
                                                t('view.quick.pending', { count: pendingOrdersCount })
                                            )}
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={() => navigate('/missions/my-missions')}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                        <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.myMissions')}</h3>
                                        <p className="text-sm text-gray-500">{t('view.quick.activeCount', { count: 3 })}</p>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Row 2 */}
                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenDisputeModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-red-100 rounded-lg">
                                        <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.disputes')}</h3>
                                        <div className="text-sm text-gray-500">
                                            {loadingDisputes ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="red" />
                                                    <span>{t('common.loading')}</span>
                                                </span>
                                            ) :
                                             errorDisputes ? t('common.notifications.error') :
                                             !Array.isArray(userDisputes) || userDisputes.length === 0 ? t('view.quick.noneActive') :
                                             `${userDisputes.length} ${t(userDisputes.filter(dispute => dispute.status === 'submitted' || dispute.status === 'in_review').length > 0 ? 'view.quick.activeLabel' : 'view.quick.totalLabel')}`}
                                        </div>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenReviewsModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-yellow-100 rounded-lg">
                                        <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.myReviews')}</h3>
                                        <p className="text-sm text-gray-500">{t('view.quick.reviewsDesc')}</p>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenGiftInventory}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-pink-100 rounded-lg">
                                        <svg className="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.myGifts')}</h3>
                                        <p className="text-sm text-gray-500">
                                            {t('view.quick.giftsDesc')}
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Row 3 */}
                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenReferralModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-indigo-100 rounded-lg">
                                        <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.inviteFriends')}</h3>
                                        <div className="text-sm text-gray-500">
                                            {referralLoading ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="indigo" />
                                                    <span>{t('common.loading')}</span>
                                                </span>
                                            ) :
                                             referralError ? t('common.notifications.error') :
                                             t('view.quick.referrals', { count: referralStats.totalReferrals })}
                                        </div>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Voice Note Card */}
                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={voiceNoteUrl ? handlePlayVoiceNote : undefined}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className={`p-2 rounded-lg ${voiceNoteUrl ? 'bg-emerald-100' : 'bg-gray-100'}`}>
                                        {isPlayingVoiceNote ? (
                                            <motion.svg
                                                className="w-6 h-6 text-emerald-600"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                animate={{ scale: [1, 1.1, 1] }}
                                                transition={{ duration: 0.5, repeat: Infinity }}
                                            >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 9v6l4-3-4-3z" />
                                            </motion.svg>
                                        ) : voiceNoteUrl ? (
                                            <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                            </svg>
                                        ) : (
                                            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                            </svg>
                                        )}
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.voiceNote')}</h3>
                                        <div className="text-sm text-gray-500">
                                            {isPlayingVoiceNote ? (
                                                <span className="flex items-center space-x-2">
                                                    <motion.div
                                                        className="w-2 h-2 bg-emerald-500 rounded-full"
                                                        animate={{ scale: [1, 1.2, 1] }}
                                                        transition={{ duration: 0.6, repeat: Infinity }}
                                                    />
                                                    <span className="text-emerald-600 font-medium">{t('view.quick.playing')}</span>
                                                </span>
                                            ) : voiceNoteError ? (
                                                <span className="text-red-500">{t('common.notifications.error')}</span>
                                            ) : voiceNoteUrl ? (
                                                <span className="text-emerald-600">{t('view.quick.clickToPlay')}</span>
                                            ) : (
                                                <span>{t('view.quick.notRecorded')}</span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Availability Card */}
                            <motion.div
                                className="bg-white dark:bg-gray-900 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer dark:text-gray-100"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenAvailabilityModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-sky-100 rounded-lg">
                                        <svg className="w-6 h-6 text-sky-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">{t('view.quick.setAvailability')}</h3>
                                        <p className="text-sm text-gray-500">{t('view.quick.manageSchedule')}</p>
                                    </div>
                                </div>
                            </motion.div>
                        </div>

                        {/* Additional Sections */}
                        <div className="space-y-6">
                                {/* Animated background decoration */}
                                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl animate-pulse" />
                                <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                                </svg>
                                            </div>
                                            <h3 className="text-xl sm:text-2xl font-bold text-left bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent dark:from-blue-300 dark:to-indigo-400 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                                {t('view.bookmarkedMissions')}
                                            </h3>
                                        </div>
                                        <motion.button
                                            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-medium rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                            onClick={() => setShowBookmarkedMissionsModal(true)}
                                        >
                                            {t('view.viewAll')}
                                        </motion.button>
                                    </div>

                                    {loadingBookmarks ? (
                                        <SectionLoader
                                            type="dots"
                                            size="medium"
                                            message="Loading bookmarked missions..."
                                            color="blue"
                                        />
                                    ) : errorBookmarks ? (
                                        <div className="text-center py-8">
                                            <div className="p-4 bg-red-50 rounded-2xl border border-red-100">
                                                <svg className="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-red-600 font-medium">{errorBookmarks}</p>
                                                <button
                                                    onClick={fetchBookmarkedMissions}
                                                    className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                                >
                                                    Retry
                                                </button>
                                            </div>
                                        </div>
                                    ) : bookmarkedMissions.length === 0 ? (
                                        <div className="text-center py-12">
                                            <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black rounded-2xl border border-gray-800">
                                                <svg className="w-16 h-16 text-gray-400 dark:text-blue-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                                </svg>
                                                <p className="text-gray-600 dark:text-white font-medium text-lg">No bookmarked missions yet</p>
                                                <p className="text-gray-500 dark:text-white text-sm mt-2">Start exploring missions and bookmark your favorites!</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                                            {bookmarkedMissions.slice(0, 3).map((mission, index) => (
                                                <motion.div
                                                    key={mission.id}
                                                    className="group relative bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                                                    initial={{ opacity: 0, y: 20 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ duration: 0.5, delay: 0.1 * index }}
                                                    whileHover={{ scale: 1.02 }}
                                                    onClick={() => setShowBookmarkedMissionsModal(true)}
                                                >
                                                    {/* Mission Image - fixed aspect ratio like Liked Posts */}
                                                    <div className="relative w-full h-48 overflow-hidden rounded-lg">
                                                        {mission.images && mission.images.length > 0 ? (
                                                            <img
                                                                src={getCdnUrl(mission.images[0])}
                                                                alt={mission.title || 'Mission image'}
                                                                className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                                                                onError={(e) => { e.target.src = '/AuthLogo.png'; e.target.onerror = null; }}
                                                            />
                                                        ) : (
                                                            <img
                                                                src={'/AuthLogo.png'}
                                                                alt="MissionX Logo"
                                                                className="w-full h-full object-contain bg-gray-100 p-8"
                                                            />
                                                        )}
                                                    </div>
                                                    {/* Card Content */}
                                                    <div className="p-4">
                                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                                                            {mission.title || mission.name || 'Untitled Mission'}
                                                        </h4>
                                                        <p className="text-gray-600 dark:text-white text-sm line-clamp-3 mb-3">
                                                            {mission.description ? mission.description.substring(0, 80) + '...' : 'No description available'}
                                                        </p>
                                                        {/* Meta */}
                                                        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                                                            <div className="flex items-center space-x-2">
                                                                <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                                                </svg>
                                                                <span className="text-xs text-gray-600">{mission.reward ? `${mission.reward} Credits` : ''}</span>
                                                            </div>
                                                            <span className="text-xs text-gray-500 dark:text-white">
                                                                {mission.created_at ? new Date(mission.created_at).toLocaleDateString() : 'Recent'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                            {/* Enhanced Liked Posts with Real API Data */}
                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-3 bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl shadow-lg">
                                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <h3 className="text-2xl font-bold bg-gradient-to-r from-pink-700 to-rose-700 bg-clip-text text-transparent dark:from-pink-300 dark:to-rose-400 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                                {t('view.likedPosts')}
                                            </h3>
                                        </div>
                                        <motion.button
                                            className="px-4 py-2 bg-gradient-to-r from-pink-500 to-rose-600 text-white text-sm font-medium rounded-xl hover:from-pink-600 hover:to-rose-700 transition-all duration-300 shadow-lg"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        onClick={() => setShowLikedPostsModal(true)}
                                        >
                                            {t('view.viewAll')}
                                        </motion.button>
                                    </div>

                                    {loadingLikedPosts ? (
                                        <SectionLoader
                                            type="wave"
                                            size="medium"
                                            message="Loading liked posts..."
                                            color="pink"
                                        />
                                    ) : errorLikedPosts ? (
                                        <div className="text-center py-8">
                                            <div className="p-4 bg-red-50 rounded-2xl border border-red-100">
                                                <svg className="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-red-600 font-medium">{errorLikedPosts}</p>
                                                <button
                                                    onClick={fetchLikedPosts}
                                                    className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                                >
                                                    Retry
                                                </button>
                                            </div>
                                        </div>
                                    ) : likedPosts.length === 0 ? (
                                        <div className="text-center py-12">
                                            <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black rounded-2xl border border-gray-800">
                                                <svg className="w-16 h-16 text-gray-400 dark:text-pink-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                </svg>
                                                <p className="text-gray-600 dark:text-white font-medium text-lg">No liked posts yet</p>
                                                <p className="text-gray-500 dark:text-white text-sm mt-2">Start exploring and liking posts from the community!</p>
                                            </div>
                                        </div>
                                    ) : (
                                    <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                                        {likedPosts.slice(0, 3).map((post, index) => (
                                            <motion.div
                                                key={post.id}
                                                className="group relative bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.5, delay: 0.1 * index }}
                                                whileHover={{ scale: 1.02 }}
                                                onClick={() => handlePostClick(post.id)}
                                            >
                                                {/* Post Image */}
                                                {post.media_files && post.media_files.length > 0 && (
                                                    <div className="relative w-full h-48 overflow-hidden rounded-lg">
                                                        <img
                                                            src={getCdnUrl(post.media_files[0].optimized || post.media_files[0].original)}
                                                            alt={post.title || 'Post image'}
                                                            className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                                                            onError={(e) => {
                                                                e.target.src = '/images/profile-placeholder.svg';
                                                                e.target.onerror = null;
                                                            }}
                                                        />
                                                    </div>
                                                )}

                                                {/* Post Content */}
                                                <div className="p-4">
                                                    <h4 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                                                        {post.title || 'Untitled Post'}
                                                            </h4>
                                                    <p className="text-gray-600 dark:text-white text-sm line-clamp-3 mb-3">
                                                        {post.description || 'No description available'}
                                                    </p>

                                                    {/* Post Meta */}
                                                    <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                                                        <div className="flex items-center space-x-2">
                                                                        <svg className="w-4 h-4 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                                        </svg>
                                                            <span className="text-xs text-gray-600">{post.likes_count || 0}</span>
                                                        </div>
                                                        <span className="text-xs text-gray-500 dark:text-white">
                                                            {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'Recent'}
                                                        </span>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        ))}
                                                                    </div>
                                                                )}
                            </div>

                            {/* Liked Posts Modal */}
                            <AnimatePresence>
                                {showLikedPostsModal && (
                                    <motion.div
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="fixed inset-0 z-50 overflow-y-auto bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
                                                onClick={() => setShowLikedPostsModal(false)}
                                            >
                                            <motion.div
                                            initial={{ opacity: 0, scale: 0.95, y: 20 }}
                                            animate={{ opacity: 1, scale: 1, y: 0 }}
                                            exit={{ opacity: 0, scale: 0.95, y: 20 }}
                                            className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900 dark:to-gray-800 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
                                            onClick={e => e.stopPropagation()}
                                        >
                                            {/* Animated background decorations */}
                                            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-pink-400/20 to-rose-400/20 rounded-full blur-2xl animate-pulse" />
                                            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-rose-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                                            {/* Header */}
                                            <div className="relative z-10 p-6 border-b border-white/20">
                                              <div className="flex items-center justify-between">
                                                <div className="flex items-center space-x-3">
                                                  <div className="p-3 bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl shadow-lg">
                                                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                    </svg>
                                                  </div>
                                                  <div>
                                                    <h3 className="text-2xl text-left font-bold bg-gradient-to-r from-pink-700 to-rose-700 dark:from-pink-300 dark:to-rose-400 bg-clip-text text-transparent dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                                      {t('view.likedPosts')}
                                                    </h3>
                                                    <p className="text-gray-600 dark:text-white text-sm">
                                                      All your liked posts in one place
                                                    </p>
                                                  </div>
                                                </div>
                                                {/* Close Button */}
                                                <motion.button
                                                            onClick={() => setShowLikedPostsModal(false)}
                                                  className="p-3 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 hover:bg-white/30 transition-all duration-300 shadow-sm hover:shadow-md"
                                                  whileHover={{ scale: 1.05, rotate: 90, backgroundColor: 'rgba(255,255,255,0.3)' }}
                                                  whileTap={{ scale: 0.95 }}
                                                  initial={{ opacity: 0, scale: 0.8 }}
                                                  animate={{ opacity: 1, scale: 1 }}
                                                  transition={{ duration: 0.2 }}
                                                        >
                                                  <svg className="w-5 h-5 text-gray-600 dark:text-white hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                                        </svg>
                                                </motion.button>
                                              </div>
                                                    </div>

                                            {/* Content */}
                                            <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
                                              {likedPosts.length === 0 ? (
                                                <div className="text-center py-16">
                                                  <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-2xl border border-gray-200 max-w-md mx-auto">
                                                    <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                                                      <svg className="w-10 h-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                      </svg>
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('view.noLikedPosts')}</h3>
                                                    <p className="text-gray-600 dark:text-white">You haven't liked any posts yet.</p>
                                                  </div>
                                                </div>
                                              ) : (
                                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                                        {likedPosts.map((post, index) => (
                                                            <motion.div
                                                                key={post.id}
                                                      className="group relative bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                                                                initial={{ opacity: 0, y: 20 }}
                                                                animate={{ opacity: 1, y: 0 }}
                                                                transition={{ duration: 0.5, delay: 0.1 * index }}
                                                                whileHover={{ scale: 1.02 }}
                                                                onClick={() => handlePostClick(post.id)}
                                                            >
                                                                {/* Post Image */}
                                                                {post.media_files && post.media_files.length > 0 && (
                                                                    <div className="relative w-full h-48 overflow-hidden rounded-lg">
                                                                        <img
                                                            src={getCdnUrl(post.media_files[0].optimized || post.media_files[0].original)}
                                                                            alt={post.title || 'Post image'}
                                                                            className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                                                            onError={e => {
                                                                                e.target.src = '/images/profile-placeholder.svg';
                                                                                e.target.onerror = null;
                                                                            }}
                                                                        />
                                                                    </div>
                                                                )}
                                                      {/* Card Content */}
                                                                <div className="p-4">
                                                                    <h4 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                                                                        {post.title || 'Untitled Post'}
                                                                    </h4>
                                                                    <p className="text-gray-600 dark:text-white text-sm line-clamp-3 mb-3">
                                                                        {post.description || 'No description available'}
                                                                    </p>
                                                        {/* Meta */}
                                                                    <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                                                                        <div className="flex items-center space-x-2">
                                                                            <svg className="w-4 h-4 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                                                                                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                                            </svg>
                                                                            <span className="text-xs text-gray-600">{post.likes_count || 0}</span>
                                                            </div>
                                                                        <span className="text-xs text-gray-500 dark:text-white">
                                                                {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'Recent'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </div>
                                              )}
                                </div>
                            </motion.div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>

                        {/* Other sections remain the same */}
                    </div>

                    {/* Sidebar - Profile Details and Services */}
                    <div className="space-y-8">
                        {/* Profile Details Card */}
                        <motion.div
                            className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/80 backdrop-blur-2xl border border-white/30 dark:border-gray-800/60 rounded-3xl p-8 shadow-2xl overflow-hidden dark:text-white"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.1 }}
                            whileHover={{ y: -5, scale: 1.02 }}
                        >
                            {/* Animated background decoration */}
                            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-2xl animate-pulse" />
                            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                            <div className="relative z-10">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent dark:from-indigo-300 dark:to-purple-400 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                        {t('view.profileDetails.title')}
                                    </h3>
                                </div>

                                <div className="space-y-4">
                                    {/* User Information - Connected to Backend API */}
                                    <div className="space-y-3">
                                        {/* Experience Points Information */}
                                        {user.stats?.exp_points !== undefined && user.level !== undefined && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.experience')}</span>
                                                <div className="text-right">
                                                    <div className="text-sm font-medium text-gray-800 dark:text-white">
                                                        {t('view.profileDetails.level')} {user.level} • {user.stats.exp_points} XP
                                                    </div>
                                                    <div className="text-xs text-gray-500 dark:text-white">
                                                        {t('view.profileDetails.xpToLevel', { xp: 1000 - (user.stats.exp_points % 1000), level: user.level + 1 })}
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {user.date_of_birth && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.birthday')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white">
                                                    {new Date(user.date_of_birth).toLocaleDateString()}
                                                </span>
                                            </div>
                                        )}

                                        {user.gender && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.gender')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white capitalize">{user.gender}</span>
                                            </div>
                                        )}

                                        {user.height && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.height')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white">{user.height} cm</span>
                                            </div>
                                        )}

                                        {user.weight && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.weight')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white">{user.weight} kg</span>
                                            </div>
                                        )}

                                        {user.race && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.race')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white">
                                                    {typeof user.race === 'object' ? user.race.name : user.race}
                                                </span>
                                            </div>
                                        )}

                                        {user.languages && user.languages.length > 0 && (
                                            <div className="py-2">
                                                <span className="text-sm text-left text-gray-600 block mb-2 dark:text-white">{t('view.profileDetails.languages')}</span>
                                                <div className="flex flex-wrap gap-1">
                                                    {user.languages.slice(0, 3).map((lang, index) => (
                                                        <span key={index} className="px-2 py-1 bg-blue-100/80 text-blue-700 text-xs rounded-full border border-blue-200/50 dark:bg-gray-900/80 dark:text-white">
                                                            {typeof lang === 'object' ? lang.name : lang}
                                                        </span>
                                                    ))}
                                                    {user.languages.length > 3 && (
                                                        <span className="px-2 py-1 bg-gray-100/80 text-gray-600 text-xs rounded-full border border-gray-200/50 dark:bg-gray-900/80 dark:text-white">
                                                            +{user.languages.length - 3} more
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {user.personalities && user.personalities.length > 0 && (
                                            <div className="py-2">
                                                <span className="text-sm text-left text-gray-600 block mb-2 dark:text-white">{t('view.profileDetails.personalities')}</span>
                                                <div className="flex flex-wrap gap-1">
                                                    {user.personalities.slice(0, 3).map((personality, index) => (
                                                        <span key={index} className="px-2 py-1 bg-purple-100/80 text-purple-700 text-xs rounded-full border border-purple-200/50 dark:bg-gray-900/80 dark:text-white">
                                                            {typeof personality === 'object' ? personality.name : personality}
                                                        </span>
                                                    ))}
                                                    {user.personalities.length > 3 && (
                                                        <span className="px-2 py-1 bg-gray-100/80 text-gray-600 text-xs rounded-full border border-gray-200/50 dark:bg-gray-900/80 dark:text-white">
                                                            +{user.personalities.length - 3} more
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {user.constellation && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.constellation')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white">{user.constellation}</span>
                                            </div>
                                        )}

                                        {typeof user.stats?.total_followers !== 'undefined' && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600 dark:text-white">{t('view.profileDetails.totalFollowers')}</span>
                                                <span className="text-sm font-medium text-gray-800 dark:text-white">{user.stats.total_followers}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </motion.div>

                        {/* Enhanced Invite Friends Card */}
                        <motion.div
                            className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/80 backdrop-blur-2xl border border-white/30 dark:border-gray-800/60 rounded-3xl p-8 shadow-2xl overflow-hidden dark:text-white"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            whileHover={{ y: -5, scale: 1.02 }}
                        >
                            {/* Animated background decoration */}
                            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl animate-pulse" />
                            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                            <div className="relative z-10">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent dark:from-blue-300 dark:to-indigo-400 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                        {t('view.quick.inviteFriends')}
                                    </h3>
                                </div>

                                <div className="space-y-6">
                                    <p className="text-gray-600 text-base leading-relaxed dark:text-white">
                                        {t('view.inviteCard.description')}
                                    </p>

                                    {/* Referral Code Display */}
                                    <div className="p-5 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/80 dark:to-indigo-900/80 backdrop-blur-sm rounded-2xl border border-blue-100/50 dark:bg-gray-900/80">
                                        <div className="text-center space-y-4">
                                            <div className="flex items-center justify-center space-x-2 mb-3">
                                                <svg className="w-5 h-5 text-blue-600 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                                </svg>
                                                <span className="text-sm font-medium text-blue-700 dark:text-white">{t('view.inviteCard.yourCode')}</span>
                                            </div>

                                            <div className="relative">
                                                <input
                                                    type="text"
                                                    value={user.referral_code || 'Loading...'}
                                                    readOnly
                                                    className="w-full bg-white/90 border-2 border-blue-200/50 rounded-xl px-6 py-4 text-center text-2xl font-bold text-blue-800 font-mono tracking-wider focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 shadow-inner"
                                                />
                                                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl pointer-events-none" />
                                            </div>

                                            <motion.button
                                                onClick={async () => {
                                                    try {
                                                        await navigator.clipboard.writeText(user.referral_code || '');
                                                        // You can add a success toast notification here
                                                    } catch (error) {
                                                        // Fallback for older browsers
                                                        const textArea = document.createElement('textarea');
                                                        textArea.value = user.referral_code || '';
                                                        document.body.appendChild(textArea);
                                                        textArea.select();
                                                        document.execCommand('copy');
                                                        document.body.removeChild(textArea);
                                                        // You can add a success toast notification here
                                                    }
                                                }}
                                                className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg flex items-center justify-center space-x-2"
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                            >
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                <span>{t('view.inviteCard.copy')}</span>
                                            </motion.button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>

                    {/* Gift Inventory Modal */}
                    <GiftManagementHub
                        isOpen={showGiftInventory}
                        onClose={() => setShowGiftInventory(false)}
                    />

                    {/* Settings Modal */}
                    <AnimatePresence>
                        {showSettings && (
                            <Settings onClose={() => setShowSettings(false)} />
                        )}
                    </AnimatePresence>

                    {/* Dispute Management Modal */}
                    <DisputeManagementModal
                        isOpen={isDisputeModalOpen}
                        onClose={() => setIsDisputeModalOpen(false)}
                        disputes={userDisputes}
                        loading={loadingDisputes}
                        error={errorDisputes}
                        onRefresh={fetchUserDisputes}
                    />

                    {/* My Clients Modal */}
                    <MyClientModal
                        isOpen={isClientsModalOpen}
                        onClose={() => setIsClientsModalOpen(false)}
                    />

                    {/* My Reviews Modal */}
                    <MyReviewsModal
                        isOpen={isReviewsModalOpen}
                        onClose={() => setIsReviewsModalOpen(false)}
                        user={user}
                    />

                    {/* Availability Modal */}
                    <AvailabilityModal
                        isOpen={isAvailabilityModalOpen}
                        onClose={() => setIsAvailabilityModalOpen(false)}
                    />

                    {/* Edit Profile Modal */}
                    <EditProfileModal
                        isOpen={isEditProfileModalOpen}
                        initialSection={editSection}
                        onClose={() => {
                            setIsEditProfileModalOpen(false);
                            setEditSection('profile');
                        }}
                        onProfileUpdated={handleProfileUpdated}
                        profileQueryKey={profileQueryKey}
                    />

                    {/* Referral Modal */}
                    <AnimatePresence>
                        {isReferralModalOpen && (
                            <ReferralModal
                                isOpen={isReferralModalOpen}
                                onClose={handleCloseReferralModal}
                                loading={referralLoading}
                                error={referralError}
                                stats={referralStats}
                            />
                        )}
                    </AnimatePresence>

                    {/* PostView Modal */}
                    <PostView
                        postId={selectedPostId}
                        isOpen={isPostViewOpen}
                        onClose={handleClosePostView}
                    />

                    {/* Bookmarked Missions Modal */}
                    <BookmarkedMissionsModal
                        isOpen={showBookmarkedMissionsModal}
                        onClose={() => setShowBookmarkedMissionsModal(false)}
                        missions={bookmarkedMissions}
                    />
                </div>
            </div>

            {/* Mobile Navigation */}
            <MobileNavigation activeItem="/profile" />

            {/* Render MyOrderModal */}
            <MyOrderModal isOpen={isOrderModalOpen} onClose={() => setIsOrderModalOpen(false)} />
            </>
    );
};

export default Profile;