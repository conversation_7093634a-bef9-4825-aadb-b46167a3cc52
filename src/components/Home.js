import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import GamesModal from './GamesModal';
import MainNavigation from './navigation/MainNavigation';
import MobileNavigation from './navigation/MobileNavigation';
import { useProfile } from '../contexts/ProfileContext';
import { useHomepage, useHomepageSection } from '../contexts/HomepageContext';
import {
    GameCardSkeletonGrid,
    TalentCardSkeletonGrid,
    NewTalentCardSkeletonGrid,
    MissionBannerSkeleton
} from './ui/Skeletons';
import {
    EmptyGamesState,
    EmptyTalentsState,
    EmptyCarouselState,
    EmptyMissionsState
} from './ui/EmptyStates';
import LazyImage from './ui/LazyImage';
import PaymentHistoryCard from '../features/wallet/components/payment/PaymentHistoryCard';
import { useAuth } from '../contexts/AuthContext';
import { creditsAPI } from '../services/creditsService';
import { useWalletQuery } from '../features/wallet/hooks/useWalletQuery';
import { FaInstagram, FaTiktok, FaLinkedin, FaGooglePlay, FaApple, FaFacebook, FaArrowUp } from 'react-icons/fa';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';
import GiftToastContainer from './notifications/GiftToastContainer';
import useTranslation from '../hooks/useTranslation';
import MobileAppPromoModal from './common/MobileAppPromoModal';
import AuthenticatedUserWelcome from './home/<USER>';
import NewTalentCard from './NewTalentCard';
import HomeSkeleton from './home/<USER>';
import profileService from '../services/profileService';

// Hero Banner Carousel Component
const HeroBannerCarousel = () => {
    const { t } = useTranslation(['home', 'common']);
    const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [direction, setDirection] = useState('right');
    const [isPaused, setIsPaused] = useState(false);
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
    const [isHovering, setIsHovering] = useState(false);
    const [clickRipple, setClickRipple] = useState(null);
    const navigate = useNavigate();

    // Use the homepage context for carousel slides
    const {
        carouselSlides: slides,
        loading,
        error
    } = useHomepage();

    // Auto-advance slides
    useEffect(() => {
        if (slides && slides.length > 1 && !isPaused) {
            const timer = setInterval(() => {
                setCurrentSlideIndex((prevIndex) => (prevIndex + 1) % slides.length);
            }, 5000);

            return () => clearInterval(timer);
            }
    }, [slides, isPaused]);

    // Handle slide click with ripple effect
    const handleSlideClick = (slide, event) => {
        // Create ripple effect
        const rect = event.currentTarget.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        setClickRipple({ x, y, timestamp: Date.now() });
        
        // Handle navigation
        if (slide.isClickable && slide.buttonUrl) {
            const url = slide.buttonUrl;
            if (/^https?:\/\//i.test(url)) {
                window.open(url, '_blank');
            } else {
                navigate(url);
            }
        }
    };

    // Handle mouse movement for interactive shine
    const handleMouseMove = (event) => {
        const rect = event.currentTarget.getBoundingClientRect();
        const x = ((event.clientX - rect.left) / rect.width) * 100;
        const y = ((event.clientY - rect.top) / rect.height) * 100;
        setMousePosition({ x, y });
    };

    // Handle mouse enter/leave for hover effects
    const handleMouseEnter = () => {
        setIsHovering(true);
    };

    const handleMouseLeave = () => {
        setIsHovering(false);
        setMousePosition({ x: 0, y: 0 });
    };

    // Show loading state
    if (loading) {
        return <MissionBannerSkeleton />;
    }

    // Show error state
    if (error) {
        return <EmptyCarouselState />;
    }

    // Show empty state
    if (!slides || slides.length === 0) {
        return <EmptyCarouselState />;
    }

    const currentSlide = slides[currentSlideIndex];
    const nextSlides = slides.slice(currentSlideIndex + 1).concat(slides.slice(0, currentSlideIndex));

    return (
        <div
            className="bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-800 overflow-hidden mb-6 rounded-2xl relative group w-full px-2 aspect-[1372/592] animate-gradient-shift shadow-lg"
            onMouseEnter={() => {
                setIsPaused(true);
                handleMouseEnter();
            }}
            onMouseLeave={() => {
                setIsPaused(false);
                handleMouseLeave();
            }}
            onMouseMove={handleMouseMove}
            style={{ backgroundSize: '200% 200%' }}
        >
            {/* Enhanced background patterns and effects */}
            <div className="absolute inset-0 opacity-10 bg-noise mix-blend-overlay"></div>
            <div className="absolute inset-0 bg-blue-900/[0.05] bg-[length:20px_20px]"></div>

            {/* Enhanced glow effects with subtle animation */}
            <div className="absolute -top-20 -right-20 w-96 h-96 bg-blue-400/20 rounded-full filter blur-3xl animate-pulse-slow"></div>
            <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-indigo-400/20 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>

            {/* Animated particles with enhanced movement */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {/* Animated lines with smoother transitions */}
                <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
                <div className="absolute top-0 right-0 w-[1px] h-full bg-gradient-to-b from-transparent via-white/40 to-transparent animate-shimmer" style={{ animationDelay: '0.5s' }}></div>
                <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer" style={{ animationDelay: '1s' }}></div>
                <div className="absolute top-0 left-0 w-[1px] h-full bg-gradient-to-b from-transparent via-white/40 to-transparent animate-shimmer" style={{ animationDelay: '1.5s' }}></div>

                {/* Floating elements with enhanced animation */}
                <div className="absolute top-[15%] left-[15%] w-2 h-2 bg-white/30 rounded-full animate-float-slow"></div>
                <div className="absolute top-[25%] left-[75%] w-2 h-2 bg-white/30 rounded-full animate-float-slow" style={{ animationDelay: '1s' }}></div>
                <div className="absolute top-[45%] left-[35%] w-2 h-2 bg-white/30 rounded-full animate-float-slow" style={{ animationDelay: '2s' }}></div>
                <div className="absolute top-[65%] left-[85%] w-2 h-2 bg-white/30 rounded-full animate-float-slow" style={{ animationDelay: '3s' }}></div>
                <div className="absolute top-[85%] left-[25%] w-2 h-2 bg-white/30 rounded-full animate-float-slow" style={{ animationDelay: '4s' }}></div>
            </div>

            {/* Advanced Slide Container - Phase 3: Ken Burns & Interactive Effects */}
            <div
                className="absolute inset-0 transition-all duration-700 ease-out"
                onClick={(e) => handleSlideClick(currentSlide, e)}
                style={{ cursor: currentSlide.isClickable ? 'pointer' : 'default' }}
            >
                {/* Enhanced Image Container with Advanced Animations - Phase 3 */}
                {currentSlide.mediaFiles && currentSlide.mediaFiles[0] && (
                    <div className="absolute inset-0 z-0 overflow-hidden">
                        {/* Step 3.1: Ken Burns Effect with Parallax & Zoom */}
                        <img
                            src={`${process.env.REACT_APP_CDN_URL}/${currentSlide.mediaFiles[0].original}`}
                            alt={currentSlide.title || 'Carousel slide'}
                            className="w-full h-full object-cover transition-all duration-[15s] ease-out"
                            loading="eager"
                            fetchpriority="high"
                            style={{
                                transform: `
                                    scale(${isHovering ? 1.12 : 1.05})
                                    translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)
                                `,
                                filter: isHovering ? 'brightness(1.15) contrast(1.05)' : 'brightness(1.05) contrast(1.02)'
                            }}
                        />
                        
                        {/* Enhanced vignette effect for better image focus */}
                        <div className="absolute inset-0 bg-radial-gradient-vignette opacity-30 animate-pulse-slow"></div>
                        
                        {/* Subtle overlay for better image contrast */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                        {/* Primary Shine Sweep Effect */}
                        <div className="shine-sweep animate-shine-sweep"></div>

                        {/* Additional Sparkle Effects for Enhanced Visual Appeal */}
                        <div className="shine-sparkle animate-shine-sparkle" style={{ top: '20%', left: '15%', animationDelay: '0s' }}></div>
                        <div className="shine-sparkle animate-shine-sparkle" style={{ top: '35%', right: '20%', animationDelay: '0.5s' }}></div>
                        <div className="shine-sparkle animate-shine-sparkle" style={{ bottom: '25%', left: '25%', animationDelay: '1s' }}></div>
                        <div className="shine-sparkle animate-shine-sparkle" style={{ bottom: '40%', right: '15%', animationDelay: '1.5s' }}></div>

                        {/* Click Ripple Effect */}
                        {clickRipple && (
                            <div
                                className="absolute w-4 h-4 bg-white/60 rounded-full pointer-events-none z-30 animate-ping"
                                style={{
                                    left: clickRipple.x,
                                    top: clickRipple.y,
                                    transform: 'translate(-50%, -50%)'
                                }}
                                onAnimationEnd={() => setClickRipple(null)}
                            ></div>
                        )}

                        {/* Depth Layers with Different Animation Speeds */}
                        <div 
                            className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-transparent pointer-events-none z-5"
                            style={{
                                transform: `translate(${mousePosition.x * 0.01}px, ${mousePosition.y * 0.01}px)`,
                                transition: 'transform 0.3s ease-out'
                            }}
                        ></div>
                        <div 
                            className="absolute inset-0 bg-gradient-to-tl from-indigo-400/10 to-transparent pointer-events-none z-5"
                            style={{
                                transform: `translate(${mousePosition.x * -0.01}px, ${mousePosition.y * -0.01}px)`,
                                transition: 'transform 0.5s ease-out'
                            }}
                        ></div>
                    </div>
                )}

                {/* New Badge - Top Left (if applicable) */}
                {currentSlide.isNew && (
                    <div className="absolute top-6 left-6 z-20">
                        <div className="inline-flex items-center px-3 py-1.5 rounded-full font-semibold text-xs bg-gradient-to-r from-green-400 to-blue-400 text-white shadow-lg shadow-green-400/40 animate-pulse">
                            <span className="w-2 h-2 bg-white rounded-full mr-2 animate-ping"></span>
                            <span className="z-10">{t('talent.new')}</span>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

function Home() {
    const { t } = useTranslation(['home', 'common']);
    const { isAuthenticated, initialCheckDone, user } = useAuth();
    const navigate = useNavigate();
    // Subscribe to profile context to ensure profile data is loaded
    const profileContext = useProfile();
    const { useBalance } = useWalletQuery();
    const { 
        data: walletBalance,
        isLoading: balanceLoading,
        isError: balanceError,
        error: balanceErrorMessage
    } = useBalance();
    const [activeTab, setActiveTab] = useState('recommended');
    const [isGamesModalOpen, setIsGamesModalOpen] = useState(false);
    const [selectedGame, setSelectedGame] = useState(null);
    const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(false);
    const [hasSeenQuickActions, setHasSeenQuickActions] = useState(false);
    const [income, setIncome] = useState(0);
    const [spent, setSpent] = useState(0);
    const [isLoadingIncome, setIsLoadingIncome] = useState(false);
    const [isLoadingSpent, setIsLoadingSpent] = useState(false);
    const [errorIncome, setErrorIncome] = useState(null);
    const [errorSpent, setErrorSpent] = useState(null);
    // Scroll Up Button State
    const [showScrollUp, setShowScrollUp] = useState(false);
    // Prevent duplicate income/spent fetches in development (StrictMode)
    const hasFetchedTransactions = useRef(false);
    // Toast state to prevent multiple welcomes
    const [welcomeToastShown, setWelcomeToastShown] = useState(false);
    const [showAvailabilityReminder, setShowAvailabilityReminder] = useState(false);
    // Mobile App Promo Modal logic - Show by default on landing page
    const [showMobileAppPromo, setShowMobileAppPromo] = useState(false);

    useEffect(() => {
        // Only greet once per session after successful profile fetch
        if (!isAuthenticated) {
            if (typeof window !== 'undefined') {
                sessionStorage.removeItem('chatbotWelcomeSent');
            }
            return;
        }

        const hasWelcomed =
            typeof window !== 'undefined' &&
            sessionStorage.getItem('chatbotWelcomeSent');
        if (hasWelcomed) return;

        const sendWelcome = async () => {
            try {
                const data = await profileService.getProfile();
                const nickname = data?.nickname;
                if (!nickname) return;

                const body = `Hello ${nickname}, welcome back to mission x, i missed you, wanna hop on some talents or missions?`;
                window.dispatchEvent(new CustomEvent('chatbot-welcome', { detail: { body } }));

                const pendingCount = Array.isArray(data?.pending_order) ? data.pending_order.length : 0;
                if (pendingCount > 0) {
                    const orderBody = `Heads up! ${pendingCount} customer${pendingCount > 1 ? 's have' : ' has'} orders waiting for your go-ahead. Shall we review them or explore new quests?`;
                    window.dispatchEvent(
                        new CustomEvent('chatbot-order-summary', { detail: { body: orderBody } })
                    );
                }

                if (typeof window !== 'undefined') {
                    sessionStorage.setItem('chatbotWelcomeSent', 'true');
                }
            } catch (err) {
                console.error('Welcome message failed:', err);
            }
        };

        sendWelcome();
    }, [isAuthenticated]);

    useEffect(() => {
        // Show modal by default when visiting the landing page, unless previously dismissed
        if (typeof window !== 'undefined' && !sessionStorage.getItem('mobileAppPromoModalDismissed')) {
            setShowMobileAppPromo(true);
        }
    }, []);
    const handleCloseMobileAppPromo = () => {
        setShowMobileAppPromo(false);
        if (typeof window !== 'undefined') {
            sessionStorage.setItem('mobileAppPromoModalDismissed', 'true');
        }
    };

    // Get homepage data from context with section-specific hooks
    const {
        availableMissionsCount: apiMissionsCount,
        refresh,
        silentRefresh,
        dataStatus,
        refreshMissionsCount,
        error: homepageDataError
    } = useHomepage();

    // Use section-specific hooks for better data management
    const { data: apiNewTalents, loading: newTalentsLoading } = useHomepageSection('newTalents');
    const { data: apiRecommendedTalents, loading: recommendedTalentsLoading } = useHomepageSection('recommendedTalents');
    const { data: apiOnlineTalents, loading: onlineTalentsLoading } = useHomepageSection('onlineTalents');
    const { games: apiPopularGames, loading: gamesLoading } = useHomepageSection('popularGames');
    const { data: apiTopTalents, loading: topTalentsLoading } = useHomepageSection('topTalents');

    // Combine loading states
    const homepageLoading = newTalentsLoading || recommendedTalentsLoading ||
                           onlineTalentsLoading || gamesLoading || topTalentsLoading;

    // Artificial delay to showcase skeleton loading
    const [loadingDelay, setLoadingDelay] = useState(true);
    useEffect(() => {
        const timer = setTimeout(() => setLoadingDelay(false), 1000);
        return () => clearTimeout(timer);
    }, []);

    const isLoading = homepageLoading || loadingDelay;
    const [showSkeleton, setShowSkeleton] = useState(true);
    const [contentReady, setContentReady] = useState(false);
    useEffect(() => {
        if (!isLoading) {
            const timer = setTimeout(() => setShowSkeleton(false), 300);
            return () => clearTimeout(timer);
        }
        setShowSkeleton(true);
    }, [isLoading]);
    useEffect(() => {
        if (!isLoading) {
            setContentReady(true);
        }
    }, [isLoading]);

    // Check if data is stale
    const isStale = dataStatus === 'stale';

    // Get error state from the context
    const homepageError = homepageDataError;

    // Use the data directly from the context without additional transformation
    // since it's already transformed by the data transformers
    const transformedPopularGames = useMemo(() => {
        // Display up to 10 games as provided by the API
        return (apiPopularGames || []).slice(0, 10);
    }, [apiPopularGames]);

    const transformedRecommendedTalents = useMemo(() => {
        // Display up to 10 talents as provided by the API
        return (apiRecommendedTalents?.talents || []).slice(0, 10);
    }, [apiRecommendedTalents]);

    const transformedNewTalents = useMemo(() => {
        // Display up to 10 talents as provided by the API
        return (apiNewTalents?.talents || []).slice(0, 10);
    }, [apiNewTalents]);

    const transformedOnlineTalents = useMemo(() => {
        // Display up to 10 talents as provided by the API
        return (apiOnlineTalents?.talents || []).slice(0, 10);
    }, [apiOnlineTalents]);

    const transformedTopTalents = useMemo(() => {
        // Display up to 4 talents as provided by the API
        return (apiTopTalents?.talents || []).slice(0, 4);
    }, [apiTopTalents]);

    // Profile data is loaded via the ProfileContext

    // Effect to handle quick actions menu state
    useEffect(() => {
        if (isQuickActionsOpen && !hasSeenQuickActions) {
            // Mark that the user has seen the quick actions menu
            setHasSeenQuickActions(true);
            localStorage.setItem('hasSeenQuickActions', 'true');
        }
    }, [isQuickActionsOpen, hasSeenQuickActions]);

    useEffect(() => {
        const fetchTransactionData = async () => {
            if (!isAuthenticated || hasFetchedTransactions.current) return;
            hasFetchedTransactions.current = true;

            try {
                setIsLoadingIncome(true);
                setIsLoadingSpent(true);
                setErrorIncome(null);
                setErrorSpent(null);

                const [incomeData, spentData] = await Promise.all([
                    creditsAPI.getIncome(),
                    creditsAPI.getSpent()
                ]);

                setIncome(incomeData.total || 0);
                setSpent(spentData.total || 0);
            } catch (error) {
                console.error('Error fetching transaction data:', error);
                if (error.response?.status === 401) {
                    // Handle unauthorized error
                    setErrorIncome(t('wallet.errorLoginIncome'));
                    setErrorSpent(t('wallet.errorLoginSpent'));
                } else {
                    setErrorIncome(t('wallet.errorLoadIncome'));
                    setErrorSpent(t('wallet.errorLoadSpent'));
                }
            } finally {
                setIsLoadingIncome(false);
                setIsLoadingSpent(false);
            }
        };

        if (isAuthenticated) {
            fetchTransactionData();
        } else {
            // Reset ref when user logs out so next login fetches again
            hasFetchedTransactions.current = false;
        }
    }, [isAuthenticated]);

    useEffect(() => {
        let timeoutId;
        const handleScroll = () => {
            if (timeoutId) return;
            timeoutId = setTimeout(() => {
                setShowScrollUp(window.scrollY > 400);
                timeoutId = null;
            }, 100);
        };
        window.addEventListener('scroll', handleScroll, { passive: true });
        return () => {
            window.removeEventListener('scroll', handleScroll);
            if (timeoutId) clearTimeout(timeoutId);
        };
    }, []);

    const handleScrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // Show notice on login if not already shown this session
    useEffect(() => {
        if (!isAuthenticated) return;
        if (typeof window !== 'undefined' && sessionStorage.getItem('availabilityReminderShown') === 'true') return;
        setShowAvailabilityReminder(true);
    }, [isAuthenticated]);

    // Handler for CTA (navigate to profile availability section)
    const handleGoToAvailability = () => {
        setShowAvailabilityReminder(false);
        if (typeof window !== 'undefined') {
            sessionStorage.setItem('availabilityReminderShown', 'true');
            window.location.href = '/profile';
        }
    };

    // Handler for dismiss
    const handleDismissAvailabilityReminder = () => {
        setShowAvailabilityReminder(false);
        if (typeof window !== 'undefined') {
            sessionStorage.setItem('availabilityReminderShown', 'true');
        }
    };

    // Rest of your component code...
    return (
        <div className="relative min-h-screen">
            <AnimatePresence>
                {showSkeleton && (
                    <motion.div
                        key="skeleton"
                        className="absolute inset-0 z-10"
                        initial={{ opacity: 1 }}
                        animate={{ opacity: isLoading ? 1 : 0 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <HomeSkeleton />
                    </motion.div>
                )}
            </AnimatePresence>
            {contentReady && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: isLoading ? 0 : 1 }}
                    transition={{ duration: 0.3 }}
                >
                    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white dark:from-gray-950 dark:via-blue-950 dark:to-gray-950">
            {/* Mobile App Promo Modal - Shows by default on landing page */}
            <MobileAppPromoModal open={showMobileAppPromo} onClose={handleCloseMobileAppPromo} />
            <MainNavigation activeItem="/home" />
            {/* Skip to Main Content Link - Hidden visually but accessible to screen readers and keyboard users */}
            <a
                href="#main-content"
                className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-[100] focus:px-4 focus:py-2 focus:bg-indigo-600 focus:text-white focus:rounded-md focus:shadow-lg focus:outline-none"
            >
                {t('skip')}
            </a>

            {/* Games Modal */}
            <GamesModal isOpen={isGamesModalOpen} onClose={() => setIsGamesModalOpen(false)} />

            {/* Enhanced Floating Quick Action Button with Accessibility Improvements */}
            <div className="fixed right-6 bottom-24 md:bottom-6 z-40">
                <div className="relative">
                    {/* Main Action Button with Enhanced Accessibility */}
                    <button
                        onClick={() => setIsQuickActionsOpen(!isQuickActionsOpen)}
                        className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center relative z-10 hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gradient-to-r dark:from-gray-800 dark:to-gray-700 dark:text-gray-100"
                        aria-label={t('quickActions.menuLabel')}
                        aria-expanded={isQuickActionsOpen}
                        aria-haspopup="true"
                        aria-controls="quick-actions-menu"
                    >
                        <span className="sr-only">{isQuickActionsOpen ? t('quickActions.closeMenu') : t('quickActions.openMenu')}</span>
                        {isQuickActionsOpen ? (
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        ) : (
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        )}

                        {/* Enhanced animation for new users */}
                        {!hasSeenQuickActions && (
                            <span className="absolute inset-0 rounded-full bg-indigo-600 animate-ping opacity-20 dark:bg-gray-700"></span>
                        )}
                    </button>

                    {/* Enhanced Quick Action Menu with Beautiful Modern Styling */}
                    {isQuickActionsOpen && (
                        <div
                            id="quick-actions-menu"
                            className="absolute bottom-16 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/50 w-80 overflow-hidden animate-modal-slide-in"
                            role="menu"
                            aria-orientation="vertical"
                            aria-labelledby="quick-actions-button"
                            style={{
                                background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)',
                                backdropFilter: 'blur(20px)',
                                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
                            }}
                        >
                            {/* Enhanced Header with Gradient and Glow Effects */}
                            <div className="relative p-4 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 dark:from-gray-800 dark:via-gray-700 dark:to-gray-900 text-white overflow-hidden">
                                {/* Background Pattern */}
                                <div className="absolute inset-0 opacity-20">
                                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                                    <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full transform translate-x-16 -translate-y-16"></div>
                                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full transform -translate-x-12 translate-y-12"></div>
                                </div>

                                <div className="relative z-10">
                                    <h3 className="font-bold text-lg" id="quick-actions-heading">{t('quickActions.title')}</h3>
                                    <p className="text-sm text-white/80 dark:text-gray-300">{t('quickActions.subtext')}</p>
                                </div>
                            </div>

                            <div className="p-3 space-y-2">
                                {/* Browse Missions Action - Inspired by Game Cards */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/missions/my-missions');
                                    }}
                                    className="group relative w-full p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-gray-700 dark:hover:to-gray-600 rounded-2xl transition-all duration-300 text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 border border-blue-100/50 dark:border-gray-600/50 hover:border-blue-200 dark:hover:border-gray-500 hover:shadow-lg hover:scale-[1.02] overflow-hidden"
                                    role="menuitem"
                                >
                                    {/* Subtle glow effect */}
                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-indigo-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                    <div className="relative flex items-center">
                                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 flex items-center justify-center text-white mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg" aria-hidden="true">
                                            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                                            </svg>
                                        </div>
                                        <div className="flex-1">
                                            <div className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-indigo-700 dark:group-hover:text-indigo-300 transition-colors text-base">{t('quickActions.browseMissions')}</div>
                                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('quickActions.browseMissionsDesc')}</div>
                                        </div>
                                        <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                {/* Create Mission Action */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/missions/create');
                                    }}
                                    className="group relative w-full p-4 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 hover:from-purple-100 hover:to-indigo-100 dark:hover:from-gray-700 dark:hover:to-gray-600 rounded-2xl transition-all duration-300 text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 border border-purple-100/50 dark:border-gray-600/50 hover:border-purple-200 dark:hover:border-gray-500 hover:shadow-lg hover:scale-[1.02] overflow-hidden"
                                    role="menuitem"
                                >
                                    <div className="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-indigo-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                    <div className="relative flex items-center">
                                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 dark:from-purple-600 dark:to-indigo-700 flex items-center justify-center text-white mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg" aria-hidden="true">
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                            </svg>
                                        </div>
                                        <div className="flex-1">
                                            <div className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-base">{t('quickActions.createMission')}</div>
                                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('quickActions.createMissionDesc')}</div>
                                        </div>
                                        <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                {/* Add Credits Action */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/wallet');
                                    }}
                                    className="group relative w-full p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-gray-800 dark:to-gray-700 hover:from-green-100 hover:to-emerald-100 dark:hover:from-gray-700 dark:hover:to-gray-600 rounded-2xl transition-all duration-300 text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 border border-green-100/50 dark:border-gray-600/50 hover:border-green-200 dark:hover:border-gray-500 hover:shadow-lg hover:scale-[1.02] overflow-hidden"
                                    role="menuitem"
                                >
                                    <div className="absolute inset-0 bg-gradient-to-br from-green-400/5 to-emerald-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                    <div className="relative flex items-center">
                                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 dark:from-green-600 dark:to-emerald-700 flex items-center justify-center text-white mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg" aria-hidden="true">
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </div>
                                        <div className="flex-1">
                                            <div className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-green-700 dark:group-hover:text-green-300 transition-colors text-base">{t('quickActions.addCredits')}</div>
                                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('quickActions.addCreditsDesc')}</div>
                                        </div>
                                        <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                {/* My Clients Action - Replaces Messages */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/profile', { state: { openMyClientsModal: true } });
                                    }}
                                    className="group relative w-full p-4 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-gray-800 dark:to-gray-700 hover:from-orange-100 hover:to-amber-100 dark:hover:from-gray-700 dark:hover:to-gray-600 rounded-2xl transition-all duration-300 text-left focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 border border-orange-100/50 dark:border-gray-600/50 hover:border-orange-200 dark:hover:border-gray-500 hover:shadow-lg hover:scale-[1.02] overflow-hidden"
                                    role="menuitem"
                                >
                                    <div className="absolute inset-0 bg-gradient-to-br from-orange-400/5 to-amber-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                    <div className="relative flex items-center">
                                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500 to-amber-600 dark:from-orange-600 dark:to-amber-700 flex items-center justify-center text-white mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg" aria-hidden="true">
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                            </svg>
                                        </div>
                                        <div className="flex-1">
                                            <div className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-orange-700 dark:group-hover:text-orange-300 transition-colors text-base">My Clients</div>
                                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">Manage your client relationships</div>
                                        </div>
                                        <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <svg className="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
                
            <main id="main-content" className="max-w-screen-2xl mx-auto px-8 py-8 mb-20 md:mb-0">
                {/* Stale data indicator */}
                {isStale && (
                    <div className="mb-4 bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 dark:border-yellow-600 p-4 rounded-md flex items-center justify-between">
                        <div className="flex items-center">
                            <svg className="h-5 w-5 text-yellow-400 dark:text-yellow-200 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p className="text-sm text-yellow-700 dark:text-yellow-200">
                                {t('staleData.notice')}
                            </p>
                        </div>
                        <button
                            onClick={() => refresh()}
                            className="text-sm text-yellow-700 dark:text-yellow-200 hover:text-yellow-900 dark:hover:text-yellow-100 font-medium flex items-center"
                        >
                            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            {t('staleData.refresh')}
                        </button>
                    </div>
                )}

                {/* Authenticated User Welcome Section */}
                <AuthenticatedUserWelcome />

                {/* Hero Banner Carousel (Replacing static hero banner) */}
                <HeroBannerCarousel />

                {/* Enhanced Popular Game Section */}
                <div className="mb-12">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-2 mb-4 py-3 sm:space-x-3 sm:mb-6 sm:py-5">
                        <div className="flex items-center justify-center w-8 h-8 rounded-lg sm:w-10 sm:h-10 sm:rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 dark:from-purple-900 dark:to-indigo-900 shadow-md text-white">
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 sm:text-2xl">{t('sections.popularGames.title')}</h2>
                            <div className="ml-1 sm:ml-2 flex items-center justify-center">
                                <span className="relative flex h-2 w-2 sm:h-3 sm:w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-purple-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-2 w-2 sm:h-3 sm:w-3 bg-purple-500"></span>
                                </span>
                            </div>
                        </div>
                        <div className="ml-1 px-1.5 py-0.5 text-xs sm:ml-2 sm:px-2.5 sm:py-1 sm:text-sm bg-indigo-100 dark:bg-indigo-900 rounded-full font-medium text-indigo-800 dark:text-indigo-200">
                            {apiPopularGames?.length || 0}
                        </div>
                        <div className="flex-1 flex justify-end">
                        <button
                                className="text-indigo-600 dark:text-indigo-200 text-xs font-medium hover:text-indigo-800 dark:hover:text-indigo-100 transition-colors inline-flex items-center group bg-indigo-50 dark:bg-gray-800 px-2 py-0.5 rounded sm:text-sm sm:px-3 sm:py-1 sm:rounded-lg"
                            onClick={() => {
                                if (isAuthenticated) {
                                    setIsGamesModalOpen(true);
                                } else {
                                    navigate('/login');
                                }
                            }}
                        >
                            {t('sections.popularGames.seeAll')}
                            <svg className="w-3 h-3 ml-1 sm:w-4 sm:h-4 sm:ml-1 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                        </div>
                    </div>

                    {/* Enhanced Game Cards */}
                    {homepageLoading ? (
                        <GameCardSkeletonGrid count={4} />
                    ) : homepageError || !transformedPopularGames?.length ? (
                        // Show empty state when there's an error or no games
                        <EmptyGamesState onExplore={() => setIsGamesModalOpen(true)} />
                    ) : (
                        <PopularGamesSlider
                            games={transformedPopularGames.slice(0, 10)}
                            isAuthenticated={isAuthenticated}
                            navigate={navigate}
                        />
                    )}
                </div>

                {/* Top Talents Section */}
                <div className="mb-12">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-2 mb-4 py-3 sm:space-x-3 sm:mb-6 sm:py-5">
                        <div className="flex items-center justify-center w-8 h-8 rounded-lg sm:w-10 sm:h-10 sm:rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 dark:from-amber-900 dark:to-orange-900 shadow-md text-white">
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 sm:text-2xl">Top Talents</h2>
                            <div className="ml-1 sm:ml-2 flex items-center justify-center">
                                <span className="relative flex h-2 w-2 sm:h-3 sm:w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-2 w-2 sm:h-3 sm:w-3 bg-amber-500"></span>
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Top Talents Grid */}
                    {homepageLoading ? (
                        <TalentCardSkeletonGrid count={4} columns={4} />
                    ) : homepageError || !transformedTopTalents?.length ? (
                        <EmptyTalentsState type="top" onExplore={() => navigate(isAuthenticated ? '/talent' : '/login')} />
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                            {transformedTopTalents.slice(0, 4).map((talent) => (
                                <div key={talent.id} className="relative group">
                                    {/* PopularTalents.png frame as base container */}
                                    <div className="relative w-full aspect-[3/4] rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                        {/* PopularTalents.png frame - base layer */}
                                        <div className="absolute inset-0 z-10">
                                            <img
                                                src="/PopularTalents.png"
                                                alt=""
                                                className="w-full h-full object-cover rounded-xl"
                                            />
                                        </div>

                                        {/* Circular profile picture - positioned behind frame */}
                                        <div className="absolute inset-0 z-0 flex items-center justify-center pt-8">
                                            <div className="w-24 h-24 sm:w-28 sm:h-28 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
                                                <img
                                                    src={talent.image || '/images/profile-placeholder.svg'}
                                                    alt={talent.name}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        </div>

                                        {/* Talent info overlay - single line layout */}
                                        <div className="absolute bottom-0 left-0 right-0 z-20 p-4">
                                            <div className="bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2">
                                                <div className="flex items-center justify-between text-white">
                                                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                                                        <h3 className="font-bold text-sm truncate">{talent.name}</h3>
                                                        {talent.verified && (
                                                            <svg className="w-3 h-3 text-blue-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                            </svg>
                                                        )}
                                                    </div>
                                                    <p className="text-xs opacity-90 flex-shrink-0 ml-2">Lv.{talent.level}</p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Hover effect */}
                                        <div className="absolute inset-0 bg-gradient-to-t from-amber-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl z-30"></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Enhanced CTA Banner - Responsive Mission Section */}
                <div className="relative flex flex-col lg:flex-row items-center justify-between gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
                    {/* Glassy Content Card - Reduced padding for mobile */}
                    <div className="max-w-xl w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-indigo-100/40 dark:border-gray-800/40 p-4 sm:p-6 lg:p-8 xl:p-10 z-10">
                            {/* Badge - Smaller on mobile */}
                        <div className="inline-flex items-center bg-gradient-to-r from-yellow-200/60 to-yellow-400/30 px-2.5 py-0.5 sm:px-3 sm:py-1 rounded-full mb-2 sm:mb-4 text-left self-start">
                                <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-yellow-300 rounded-full mr-1.5 sm:mr-2 animate-pulse"></span>
                            <span className="text-indigo-700 dark:text-yellow-100 text-xs sm:text-sm font-medium">{t('hero.badge')}</span>
                            </div>
                        {/* Heading - Responsive text sizes */}
                        <h3 className="text-base sm:text-lg lg:text-xl xl:text-2xl font-bold text-left text-gray-900 dark:text-white mb-2 sm:mb-3 [text-shadow:0_2px_5px_rgba(0,0,0,0.08)]">
                                {apiMissionsCount > 0
                                    ? t('hero.moreMissions', { count: apiMissionsCount })
                                    : t('hero.excitingMissions')}
                            </h3>
                        {/* Description - Responsive text sizes */}
                        <p className="text-gray-700 text-left dark:text-gray-200 font-semibold mb-3 sm:mb-4 lg:mb-6 max-w-lg text-sm sm:text-base lg:text-lg">
                                {t('hero.description')}
                            </p>
                        {/* CTA Buttons - Smaller buttons and spacing on mobile */}
                            <div className="flex flex-wrap gap-2 sm:gap-3 lg:gap-4">
                                <button
                                className="group bg-gradient-to-r from-indigo-500 to-blue-500 text-white px-4 sm:px-5 lg:px-6 py-2 sm:py-2.5 lg:py-3 rounded-lg lg:rounded-xl font-bold lg:font-extrabold hover:from-indigo-600 hover:to-blue-600 hover:shadow-xl transition-all duration-300 inline-flex items-center relative overflow-hidden text-sm sm:text-base"
                                    onClick={() => navigate(isAuthenticated ? '/missions' : '/login')}
                                >
                                    <span className="relative z-10 font-semibold">{t('hero.applyNow')}</span>
                                    <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-1 sm:ml-2 relative z-10 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg>
                                </button>
                                <button
                                className="group bg-white/80 dark:bg-gray-800/80 border border-indigo-200 dark:border-gray-700 text-indigo-700 dark:text-white px-4 sm:px-5 lg:px-6 py-2 sm:py-2.5 lg:py-3 rounded-lg lg:rounded-xl font-bold lg:font-extrabold hover:bg-indigo-50 dark:hover:bg-gray-900 hover:shadow-lg transition-all duration-300 inline-flex items-center text-sm sm:text-base"
                                    onClick={() => navigate(isAuthenticated ? '/missions' : '/login')}
                                >
                                    <span className="font-semibold">{t('hero.browse')}</span>
                                    <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-1 sm:ml-2 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>
                            {/* Stats - Smaller spacing and icons on mobile */}
                            <div className="flex gap-3 sm:gap-4 lg:gap-6 mt-3 sm:mt-4 lg:mt-6">
                                <div className="flex items-center">
                                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-fuchsia-400 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    <div>
                                    <div className="text-indigo-900 dark:text-white font-medium text-sm sm:text-base">500+</div>
                                    <div className="text-fuchsia-600 dark:text-fuchsia-200 text-xs font-medium">{t('hero.stats.activePlayers')}</div>
                                    </div>
                                </div>
                                <div className="flex items-center">
                                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-fuchsia-400 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                    <div className="text-indigo-900 dark:text-white font-medium text-sm sm:text-base">98%</div>
                                    <div className="text-fuchsia-600 dark:text-fuchsia-200 text-xs font-medium">{t('hero.stats.completionRate')}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/* Decorative/Background Effects - Hidden on very small screens */}
                    <div className="absolute inset-0 pointer-events-none z-0">
                        <div className="absolute inset-0 opacity-10 bg-noise mix-blend-overlay"></div>
                        <div className="hidden sm:block absolute right-0 bottom-0 w-32 h-32 lg:w-64 lg:h-64 bg-white/20 dark:bg-white/10 rounded-full transform translate-x-10 translate-y-10 lg:translate-x-20 lg:translate-y-20 blur-xl lg:blur-2xl"></div>
                        <div className="hidden sm:block absolute left-0 top-0 w-20 h-20 lg:w-40 lg:h-40 bg-white/20 dark:bg-white/10 rounded-full transform -translate-x-10 -translate-y-10 lg:-translate-x-20 lg:-translate-y-20 blur-xl lg:blur-2xl"></div>
                        {/* Animated Particles - Reduced for mobile */}
                        <div className="hidden md:block absolute inset-0 overflow-hidden">
                            <div className="absolute h-2 w-2 lg:h-3 lg:w-3 rounded-full bg-white/30 top-[15%] left-[10%] animate-float-particle" style={{ animationDuration: '8s' }}></div>
                            <div className="absolute h-2 w-2 lg:h-4 lg:w-4 rounded-full bg-white/20 top-[35%] left-[15%] animate-float-particle" style={{ animationDuration: '12s', animationDelay: '1s' }}></div>
                            <div className="absolute h-2 w-2 lg:h-3 lg:w-3 rounded-full bg-white/25 top-[65%] left-[5%] animate-float-particle" style={{ animationDuration: '10s', animationDelay: '2s' }}></div>
                        </div>
                    </div>
                    {/* Image Section - Smaller and visible on tablets+ */}
                    <div className="hidden lg:block relative z-10">
                        <div className="absolute -top-6 -left-6 lg:-top-10 lg:-left-10 w-16 h-16 lg:w-32 lg:h-32 bg-yellow-300/30 rounded-full opacity-40 animate-spin-slow blur-sm lg:blur-md"></div>
                        <div className="relative bg-purple-800/20 dark:bg-purple-900/30 backdrop-blur-md rounded-xl lg:rounded-2xl p-3 lg:p-6 border border-purple-400/10 shadow-lg lg:shadow-2xl group-hover:scale-105 transition-transform duration-500">
                                <LazyImage
                                    src="/missionAwards.svg"
                                    alt="Mission target"
                                className="h-24 lg:h-32 xl:h-40 object-contain relative z-10 transition-transform group-hover:scale-110 duration-500 drop-shadow-lg lg:drop-shadow-xl"
                                    fallbackSrc="/images/game-placeholder.svg"
                                    placeholderSrc="/images/game-placeholder-low.svg"
                                    blurUp={true}
                                />
                                {/* Decorative elements - Smaller */}
                            <div className="absolute top-2 right-2 lg:top-4 lg:right-4 w-4 h-4 lg:w-8 lg:h-8 rounded-full bg-yellow-400/40 animate-pulse-slow"></div>
                            <div className="absolute bottom-2 left-2 lg:bottom-4 lg:left-4 w-3 h-3 lg:w-6 lg:h-6 rounded-full bg-fuchsia-400/40 animate-float"></div>
                        </div>
                    </div>
                </div>

                {/* Enhanced Recommended Section */}
                <div className="mb-12 relative">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6 py-5">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-fuchsia-500 to-indigo-600 shadow-md text-white">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h2 className="text-xl font-bold text-gray-800 dark:text-white sm:text-2xl">{t('sections.recommended.title')}</h2>
                    </div>

                    {/* Loading State */}
                    {homepageLoading ? (
                        <TalentCardSkeletonGrid count={4} columns={4} />
                    ) : homepageError || !transformedRecommendedTalents?.length ? (
                        // Show empty state when there's an error or no recommended talents
                        <EmptyTalentsState type="recommended" onExplore={() => navigate(isAuthenticated ? '/talent' : '/login')} />
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            {transformedRecommendedTalents.map((talent) => (
                                <NewTalentCard key={talent.id} talent={talent} badgeType="recommended" />
                            ))}
                        </div>
                    )}
                </div>

                {/* Section Title with Icon */}
                <div className="flex items-center space-x-3 mt-12 mb-6 py-5">
                    <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 shadow-md text-white">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h2 className="text-xl font-bold text-gray-800 dark:text-white sm:text-2xl">{t('wallet.title')}</h2>
                </div>

                {/* Enhanced Wallet Card - Single Card with Better Accessibility */}
                <div className="mb-12">
                    <div
                        className="bg-gradient-to-br from-indigo-50 via-white to-blue-50 dark:from-indigo-900 dark:via-gray-900 dark:to-blue-950 rounded-2xl p-8 cursor-pointer shadow-lg hover:shadow-xl transition-all transform hover:scale-[1.01] duration-300 border border-indigo-100/50 dark:border-indigo-800/60 dark:shadow-indigo-900/20 relative overflow-hidden group"
                        onClick={() => navigate('/wallet')}
                        tabIndex="0"
                        role="button"
                        aria-label={t('wallet.viewWalletAria')}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                navigate('/wallet');
                            }
                        }}
                    >
                        {/* Background patterns with reduced animation for better performance */}
                        <div className="absolute inset-0 bg-grid-gray/[0.03] dark:bg-grid-gray/[0.08] bg-[length:20px_20px]"></div>
                        <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-indigo-200/20 dark:bg-indigo-900/30 rounded-full blur-xl"></div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {/* Left Column - Wallet Info */}
                            <div className="relative z-10">
                                {/* Content Header */}
                                <div className="flex items-center space-x-4 mb-6">
                                    <div className="w-14 h-14 bg-gradient-to-br from-indigo-500 to-blue-600 dark:from-indigo-800 dark:to-blue-900 rounded-xl flex items-center justify-center shadow-lg text-white group-hover:rotate-3 transition-transform duration-300">
                                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="text-xl sm:text-2xl font-bold text-left text-gray-800 dark:text-indigo-100 group-hover:translate-x-1 transition-transform duration-300">{t('wallet.myWallet')}</h3>
                                        <p className="text-md sm:text-lg text-left text-gray-500 dark:text-indigo-200 mt-1">{t('wallet.manageCredits')}</p>
                                    </div>
                                </div>

                                {/* Balance with Enhanced Accessibility */}
                                <div className="mb-6">
                                    <p className="text-sm font-medium text-gray-500 dark:text-indigo-200 mb-2">{t('wallet.currentBalance')}</p>
                                    {isAuthenticated ? (
                                        <div className="flex items-center bg-gradient-to-r from-indigo-500 to-blue-600 dark:from-indigo-800 dark:to-blue-900 px-6 py-3 rounded-xl shadow-md group-hover:shadow-lg transition-all duration-300 w-fit">
                                            {balanceLoading ? (
                                                <div className="w-24 h-8 bg-indigo-400/50 dark:bg-indigo-900/50 rounded animate-pulse"></div>
                                            ) : balanceError ? (
                                                <span className="text-white text-sm">{t('wallet.errorLoadingBalance')}</span>
                                            ) : (
                                                <span className="font-bold text-white text-2xl flex items-center gap-1">
                                                    <img src="/In-AppAssets/xcoin.png" alt="currency" className="w-6 h-6" />
                                                    {walletBalance?.toLocaleString() || 0}
                                                </span>
                                            )}
                                        </div>
                                    ) : null}
                                </div>

                                {/* Transaction summary with currency icon */}
                                {isAuthenticated ? (
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="bg-indigo-50 dark:bg-gradient-to-r dark:from-indigo-900 dark:to-blue-950 p-4 rounded-xl border border-indigo-100 dark:border-indigo-800 shadow-sm dark:shadow-indigo-900/10">
                                            <p className="text-xs text-indigo-500 dark:text-indigo-200 mb-1">{t('wallet.income')}</p>
                                            {isLoadingIncome ? (
                                                <div className="flex items-center justify-center h-8">
                                                    <div className="w-5 h-5 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                                                </div>
                                            ) : errorIncome ? (
                                                <p className="text-sm text-red-500 dark:text-red-400">{errorIncome}</p>
                                            ) : (
                                                <p className="text-xl font-bold items-center justify-center text-indigo-700 dark:text-white flex gap-1">
                                                    <img src="/In-AppAssets/xcoin.png" alt="Currency Icon" className="w-5 h-5 inline-block" />
                                                    {income.toLocaleString()}
                                                </p>
                                            )}
                                        </div>
                                        <div className="bg-indigo-50 dark:bg-gradient-to-r dark:from-indigo-900 dark:to-blue-950 p-4 rounded-xl border border-indigo-100 dark:border-indigo-800 shadow-sm dark:shadow-indigo-900/10">
                                            <p className="text-xs text-indigo-500 dark:text-indigo-200 mb-1">{t('wallet.spent')}</p>
                                            {isLoadingSpent ? (
                                                <div className="flex items-center justify-center h-8">
                                                    <div className="w-5 h-5 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                                                </div>
                                            ) : errorSpent ? (
                                                <p className="text-sm text-red-500 dark:text-red-400">{errorSpent}</p>
                                            ) : (
                                                <p className="text-xl font-bold items-center justify-center text-indigo-700 dark:text-white flex gap-1">
                                                    <img src="/In-AppAssets/xcoin.png" alt="Currency Icon" className="w-5 h-5 inline-block" />
                                                    {spent.toLocaleString()}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-100 dark:border-gray-800 shadow-lg shadow-indigo-50/50 dark:shadow-indigo-900/10 overflow-auto">
                                        <div className="p-1">
                                            <PaymentHistoryCard
                                                variant="minimal"
                                                limit={3}
                                                showFilters={false}
                                                showRefreshButton={false}
                                                autoRefresh={false}
                                                className="p-4 bg-white dark:bg-gray-900"
                                            />
                                        </div>
                                    </div>
                                )}

                                {/* Quick Actions */}
                                <div className="mt-6 flex space-x-3">
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigate('/wallet');
                                        }}
                                        className="px-4 py-2 bg-indigo-600 dark:bg-indigo-800 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 dark:hover:bg-indigo-900 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    >
                                        {t('wallet.addCredits')}
                                    </button>
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigate('/wallet');
                                        }}
                                        className="px-4 py-2 bg-white dark:bg-gray-900 text-indigo-600 dark:text-indigo-200 border border-indigo-200 dark:border-indigo-800 rounded-lg text-sm font-medium hover:bg-indigo-50 dark:hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    >
                                        {t('wallet.withdraw')}
                                    </button>
                                </div>
                            </div>

                            {/* Right Column - Transaction History Preview */}
                            <div className="relative z-10 hidden md:block flex-1 ml-8">
                                <div className="flex items-center mb-6">
                                    <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-indigo-500 via-blue-500 to-indigo-600 flex items-center justify-center mr-4 shadow-lg shadow-indigo-100">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                        <h4 className="text-2xl font-bold text-gray-800 dark:text-white tracking-tight">{t('wallet.recentTransactions')}</h4>
                                        <p className="text-gray-500 dark:text-gray-100 text-left text-sm mt-1">{t('wallet.latestActivities')}</p>
                                            </div>
                                        </div>
                                {isAuthenticated ? (
                                    <>
                                        <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-100 dark:border-gray-800 shadow-lg shadow-indigo-50/50 dark:shadow-indigo-900/10 overflow-auto">
                                            <div className="p-1">
                                                <PaymentHistoryCard
                                                    variant="minimal"
                                                    limit={3}
                                                    showFilters={false}
                                                    showRefreshButton={false}
                                                    autoRefresh={false}
                                                    className="p-4 bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
                                                />
                                    </div>
                                </div>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        navigate('/wallet');
                                    }}
                                            className="mt-6 text-sm bg-transparent hover:bg-transparent text-indigo-600 dark:text-white font-medium hover:text-indigo-800 dark:hover:text-indigo-200 transition-colors flex items-center group"
                                >
                                    {t('wallet.viewAll')}
                                            <svg className="w-4 h-4 ml-1.5 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                                    </>
                                ) : (
                                    <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-100 dark:border-gray-800 shadow-lg shadow-indigo-50/50 dark:shadow-indigo-900/10 overflow-hidden">
                                        <div className="p-6 flex flex-col items-center justify-center space-y-4">
                                            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-500 via-blue-500 to-indigo-600 dark:from-indigo-800 dark:via-blue-900 dark:to-indigo-900 flex items-center justify-center shadow-lg shadow-indigo-100 dark:shadow-indigo-900/10">
                                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                            </div>
                                            <p className="text-gray-600 dark:text-white text-center">{t('wallet.signInTransactions')}</p>
                                            <button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate('/login');
                                                }}
                                                className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-800 dark:to-blue-900 text-white rounded-xl font-medium hover:from-indigo-700 hover:to-blue-700 dark:hover:from-indigo-900 dark:hover:to-blue-950 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-lg shadow-indigo-500/25 dark:shadow-indigo-900/10"
                                            >
                                                Login / Signup
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Animated coins with reduced animation for better performance */}
                        <div className="absolute bottom-4 right-4 flex space-x-[-8px]">
                            <div className="w-8 h-8 rounded-full bg-yellow-400 shadow-md opacity-80 rotate-0 group-hover:rotate-[25deg] transition-transform z-20">
                                <div className="absolute inset-1 bg-yellow-300 rounded-full flex items-center justify-center text-yellow-700 text-[10px] font-bold">₩</div>
                            </div>
                            <div className="w-8 h-8 rounded-full bg-indigo-400 shadow-md opacity-80 rotate-0 group-hover:rotate-[-15deg] transition-transform z-10">
                                <div className="absolute inset-1 bg-indigo-300 rounded-full flex items-center justify-center text-indigo-700 text-[10px] font-bold">₩</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* New Talents Section */}
                <div className="mb-12 relative">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6 py-5">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-green-500 to-teal-500 shadow-md text-white">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-xl font-bold text-gray-800 dark:text-white sm:text-2xl">{t('sections.newTalents.title')}</h2>
                            <div className="ml-2 flex items-center justify-center">
                                <span className="relative flex h-3 w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                                </span>
                            </div>
                        </div>
                    </div>

                    {homepageLoading ? (
                        <NewTalentCardSkeletonGrid count={4} />
                    ) : homepageError || !transformedNewTalents?.length ? (
                        // Show empty state when there's an error or no new talents
                        <EmptyTalentsState type="new" onExplore={() => navigate(isAuthenticated ? '/talent' : '/login')} />
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            {transformedNewTalents.map((talent) => (
                                <NewTalentCard key={talent.id} talent={talent} badgeType="new" />
                            ))}
                        </div>
                    )}
                </div>

                {/* Online Talents Section */}
                <div className="mb-12 relative">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6 py-5">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-red-500 to-orange-500 shadow-md text-white">
                            <svg className="w-5 h-5 animate-pulse-slow" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-xl font-bold text-gray-800 dark:text-white sm:text-2xl">{t('sections.onlineTalents.title')}</h2>
                            <div className="ml-2 flex items-center justify-center">
                                <span className="relative flex h-3 w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                                </span>
                            </div>
                        </div>
                    </div>

                    {homepageLoading ? (
                        <TalentCardSkeletonGrid count={4} columns={4} />
                    ) : homepageError || !transformedOnlineTalents?.length ? (
                        // Show empty state when there's an error or no online talents
                        <EmptyTalentsState type="online" onExplore={() => navigate(isAuthenticated ? '/talent' : '/login')} />
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            {transformedOnlineTalents.map((talent) => (
                                <NewTalentCard key={talent.id} talent={talent} badgeType="online" />
                            ))}
                        </div>
                    )}
                </div>
            </main>
            {/* MissionX Modern Footer */}
            <footer className="bg-gradient-to-tr from-[#241f36] to-indigo-950 text-white pt-8 pb-16 px-4 md:px-0 border-t border-indigo-900/40 shadow-[0_-2px_24px_0_rgba(36,31,54,0.25)]">
                {/* App Preview Section - visually prominent, above the grid */}
                <div className="relative flex flex-col md:flex-row items-center justify-between max-w-7xl mx-auto mb-12 px-2 md:px-0">
                  {/* Text and CTA */}
                  <div className="flex-1 flex flex-col items-center md:items-start z-10">
                    <h3 className="text-3xl font-bold mb-3 text-white drop-shadow">Experience MissionX on Mobile!</h3>
                    <p className="text-indigo-100 mb-6 max-w-md text-center md:text-left">
                      Download our app for the best experience, anytime, anywhere.
                    </p>
                    <div className="flex space-x-4 mb-8">
                      {/* Use the same App Download Buttons as in the footer grid for consistency */}
                      <a href="https://play.google.com/store/apps/details?id=com.missionx.user" className="flex items-center bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-600 hover:to-blue-600 transition-colors rounded-2xl px-5 py-4 space-x-4 shadow-lg border border-indigo-800 group">
                        <FaGooglePlay className="w-8 h-8 text-[#ffffff]" style={{ filter: 'drop-shadow(0 1px 0 #fff)' }} />
                        <span className="flex flex-col items-start ml-2">
                          <span className="text-xs text-indigo-100 group-hover:text-white">{t('footer.getItOn')}</span>
                          <span className="font-bold text-lg text-white group-hover:underline">{t('footer.googlePlay')}</span>
                        </span>
                      </a>
                      <a href="https://apps.apple.com/my/app/mission-x-gigs-for-youth/id6743386705" className="flex items-center bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-600 hover:to-blue-600 transition-colors rounded-2xl px-5 py-4 space-x-4 shadow-lg border border-indigo-800 group">
                        <FaApple className="w-8 h-8 text-white" />
                        <span className="flex flex-col items-start ml-2">
                          <span className="text-xs text-indigo-100 group-hover:text-white">{t('footer.downloadOn')}</span>
                          <span className="font-bold text-lg text-white group-hover:underline">{t('footer.appleStore')}</span>
                        </span>
                      </a>
                    </div>
                  </div>
                  {/* Mockup Images */}
                  <div className="flex-1 flex items-end justify-center md:justify-end relative min-h-[340px]">
                    {/* Angled wallet mockup in the back */}
                    <img
                      src="/Dompet.png"
                      alt="MissionX Wallet"
                      className="absolute left-0 bottom-0 w-44 md:w-60 opacity-70 blur-[1px] z-0 transform -rotate-6"
                      style={{ filter: 'drop-shadow(0 8px 32px rgba(0,0,0,0.25))' }}
                    />
                    {/* Main homepage mockup in front */}
                    <img
                      src="/HomepageApp.png"
                      alt="MissionX App Home"
                      className="relative w-52 md:w-72 rounded-2xl shadow-2xl z-10"
                      style={{ filter: 'drop-shadow(0 12px 48px rgba(24,25,38,0.45))' }}
                    />
                  </div>
                </div>
                <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10 md:gap-16">
                    {/* Logo & Brand */}
                    <div className="flex flex-col items-start space-y-4">
                        <div className="flex items-center space-x-3">
                            <img src="/missionx-logo.png" alt="MissionX Logo" className="w-12 h-12 rounded-xl shadow-lg" />
                            <span className="text-2xl font-bold text-left tracking-tight text-white font-sans">{t('footer.missionX')}</span>
                        </div>
                        <p className="text-sm text-left text-indigo-100 max-w-xs mt-2">{t('hero.tagline')}</p>
                        <div className="flex space-x-3 mt-3">
                            {/* Social icon links */}
                            <a href="#" aria-label="Instagram" className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800 hover:bg-indigo-700 shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-indigo-400">
                                <FaInstagram className="w-5 h-5" />
                            </a>
                            <a href="#" aria-label="TikTok" className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800 hover:bg-indigo-700 shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-indigo-400">
                                <FaTiktok className="w-5 h-5" />
                            </a>
                            <a href="#" aria-label="LinkedIn" className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800 hover:bg-indigo-700 shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-indigo-400">
                                <FaLinkedin className="w-5 h-5" />
                            </a>
                            <a href="#" aria-label="Facebook" className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800 hover:bg-indigo-700 shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-indigo-400">
                                <FaFacebook className="w-5 h-5" />
                            </a>
                        </div>
                        <span className="text-xs text-indigo-100">&copy; {new Date().getFullYear()} {t('footer.missionX')}. {t('footer.rightsReserved')}</span>
                    </div>
                    {/* Get Started Links */}
                    <nav aria-label={t('footer.getStarted')} className="mt-2">
                        <h4 className="font-bold text-lg text-left mb-5 text-indigo-100 tracking-wide">{t('footer.getStarted')}</h4>
                        <ul className="space-y-3 text-indigo-200 text-left text-base">
                            <li><a href="/home" className="hover:text-white hover:underline underline-offset-4 transition-colors">{t('footer.homepage')}</a></li>
                        </ul>
                    </nav>
                    {/* Company Links */}
                    <nav aria-label={t('footer.company')} className="mt-2">
                        <h4 className="font-bold text-lg mb-5 text-indigo-100 text-left tracking-wide">{t('footer.company')}</h4>
                        <ul className="space-y-3 text-indigo-200 text-left text-base">
                            <li><a href="https://missionx.com.my/" className="hover:text-white hover:underline underline-offset-4 transition-colors">{t('footer.aboutUs')}</a></li>
                            <li><a href="https://missionx.com.my/terms-of-service/" className="hover:text-white hover:underline underline-offset-4 transition-colors">{t('footer.termsOfService')}</a></li>
                            <li><a href="https://missionx.com.my/privacy-policy/" className="hover:text-white hover:underline underline-offset-4 transition-colors">{t('footer.privacyPolicy')}</a></li>
                            <li><a href="https://missionx.com.my/terms-and-conditions/" className="hover:text-white hover:underline underline-offset-4 transition-colors">{t('footer.termsAndConditions')}</a></li>
                        </ul>
                    </nav>
                    {/* App Download Buttons */}
                    <div className="mt-2">
                        <h4 className="font-bold text-lg mb-5 text-indigo-100 tracking-wide">{t('footer.getMobile')}</h4>
                        <div className="flex flex-col space-y-4">
                            <a href="https://play.google.com/store/apps/details?id=com.missionx.user" className="flex items-center bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-600 hover:to-blue-600 transition-colors rounded-2xl px-5 py-4 space-x-4 shadow-lg border border-indigo-800 group">
                                <FaGooglePlay className="w-8 h-8 text-[#ffffff]" style={{ filter: 'drop-shadow(0 1px 0 #fff)' }} />
                                <span className="flex flex-col items-start ml-2">
                                    <span className="text-xs text-indigo-100 group-hover:text-white">{t('footer.getItOn')}</span>
                                    <span className="font-bold text-lg text-white group-hover:underline">{t('footer.googlePlay')}</span>
                                </span>
                            </a>
                            <a href="#" className="flex items-center bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-600 hover:to-blue-600 transition-colors rounded-2xl px-5 py-4 space-x-4 shadow-lg border border-indigo-800 group">
                                <FaApple className="w-8 h-8 text-white" />
                                <span className="flex flex-col items-start ml-2">
                                    <span className="text-xs text-indigo-100 group-hover:text-white">{t('footer.downloadOn')}</span>
                                    <span className="font-bold text-lg text-white group-hover:underline">{t('footer.appleStore')}</span>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </footer>
            <MobileNavigation activeItem="/home" />
            {/* Scroll Up Button */}
            {showScrollUp && (
                <button
                    onClick={handleScrollToTop}
                    aria-label={t('scrollToTop')}
                    className="fixed z-50 left-2 bottom-1 md:bottom-1 w-12 h-12 rounded-full bg-gradient-to-br from-indigo-600 to-blue-600 text-white shadow-xl flex items-center justify-center transition-opacity duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                    style={{ opacity: showScrollUp ? 1 : 0 }}
                >
                    <FaArrowUp className="w-6 h-6" />
                </button>
            )}
            {isAuthenticated && <GiftToastContainer />}
            <ToastContainer />
                </div>
            </motion.div>
        )}
    </div>
);
}

export default Home;

// --- PopularGamesSlider Component ---
function PopularGamesSlider({ games, isAuthenticated, navigate }) {
    const scrollRef = useRef(null);

    const scrollBy = (amount) => {
        if (scrollRef.current) {
            scrollRef.current.scrollBy({ left: amount, behavior: 'smooth' });
        }
    };

    return (
        <div className="relative w-full">
            {/* Left Slider Button */}
            <button
                className="absolute left-0 top-1/2 -translate-y-1/2 z-20 bg-black/40 hover:bg-black/70 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg transition-all duration-200 focus:outline-none"
                style={{ display: games.length > 4 ? 'flex' : 'none' }}
                onClick={() => scrollBy(-300)}
                aria-label="Scroll left"
            >
                <svg className="w-4 h-4 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
            {/* Right Slider Button */}
            <button
                className="absolute right-0 top-1/2 -translate-y-1/2 z-20 bg-black/40 hover:bg-black/70 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg transition-all duration-200 focus:outline-none"
                style={{ display: games.length > 4 ? 'flex' : 'none' }}
                onClick={() => scrollBy(300)}
                aria-label="Scroll right"
            >
                <svg className="w-4 h-4 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
            <div
                ref={scrollRef}
                className="flex flex-row gap-2 overflow-x-auto pb-2 scrollbar-hide sm:gap-3"
                style={{ scrollBehavior: 'smooth' }}
            >
                {games.map((gameData, idx) => (
                    <div
                        key={gameData.id}
                        className="group relative rounded-2xl sm:rounded-3xl border-2 border-white/20 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 shadow-[0_6px_24px_0_rgba(24,25,38,0.35)] hover:shadow-[0_8px_32px_0_rgba(24,25,38,0.55)] sm:shadow-[0_8px_32px_0_rgba(24,25,38,0.45)] sm:hover:shadow-[0_12px_48px_0_rgba(24,25,38,0.65)] backdrop-blur-2xl transition-all duration-300 min-w-[140px] max-w-[160px] h-[200px] sm:min-w-[180px] sm:max-w-[220px] sm:h-[280px] flex flex-col cursor-pointer overflow-hidden"
                        style={{ animationDelay: `${idx * 100}ms` }}
                        onClick={() => {
                            if (isAuthenticated) {
                                navigate('/talent', { state: { initialFilters: { serviceCategoryId: 1, serviceTypeId: gameData.id } } });
                            } else {
                                navigate('/login');
                            }
                        }}
                    >
                        {/* Glowing ring effect */}
                        <div className="absolute inset-0 z-0 rounded-2xl sm:rounded-3xl pointer-events-none"
                            style={{
                                background: 'radial-gradient(circle at 50% 60%, rgba(165,180,252,0.35) 0%, rgba(192,132,252,0.25) 40%, rgba(236,72,153,0.18) 80%, transparent 100%)',
                                filter: 'blur(12px)',
                                opacity: 0.85
                            }}
                        />
                        {/* Game Image */}
                        <img
                            src={gameData.image}
                            alt={gameData.name}
                            className="w-full h-full object-cover object-center scale-105 rounded-2xl sm:rounded-3xl group-hover:scale-110 transition-transform duration-500"
                            style={{ boxShadow: '0 0 0 4px rgba(255,255,255,0.08) sm:0 0 0 6px rgba(255,255,255,0.10)' }}
                        />
                        {/* Overlay for readability */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-80 group-hover:opacity-90 transition-opacity rounded-2xl sm:rounded-3xl"></div>
                        {/* Bottom gradient overlay */}
                        <div className="absolute bottom-0 left-0 w-full h-16 sm:h-20 z-10 pointer-events-none rounded-b-2xl sm:rounded-b-3xl"
                            style={{
                                background: 'linear-gradient(to top, rgba(24,25,38,0.85) 0%, rgba(24,25,38,0.0) 100%)'
                            }}
                        />
                        {/* Game Name with gradient text */}
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20 text-center w-11/12 sm:bottom-6">
                            <span className="block text-sm font-extrabold sm:text-lg bg-gradient-to-r from-indigo-200 via-white to-purple-300 bg-clip-text text-transparent drop-shadow">
                                {gameData.name}
                            </span>
                        </div>
                        {/* Optional: Popular badge */}
                        {gameData.isPopular && (
                            <span className="absolute top-2 left-2 px-2 py-0.5 rounded-full bg-gradient-to-r from-pink-500 to-indigo-500 text-white text-xs font-bold shadow backdrop-blur-md border border-white/20 z-30 sm:top-4 sm:left-4 sm:px-3 sm:py-1 sm:text-xs">
                                POPULAR
                            </span>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
}
// --- End PopularGamesSlider ---
