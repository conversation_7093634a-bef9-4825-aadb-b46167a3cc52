import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../common/ToastProvider';
import { useAuth } from '../../contexts/AuthContext';
import { FaChevronLeft, FaChevronRight, FaFlag } from 'react-icons/fa';
import { missionApi } from '../../services/missionApi';
import ReportMissionModal from '../ReportMissionModal';

const MissionCard = ({ mission, onApply, hideApplyButton = false }) => {
  const navigate = useNavigate();
  const toast = useToast();
  const { isAuthenticated, user } = useAuth();
  const [isHovered, setIsHovered] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(mission.bookmarked || false);
  const [isApplying, setIsApplying] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState('');
  const images = mission.images && mission.images.length > 0 ? mission.images : [mission.image || '/images/mission-default.jpg'];
  const [currentImage, setCurrentImage] = useState(0);
  const hasMultipleImages = images.length > 1;

  const [showReportModal, setShowReportModal] = useState(false);

  // Application state
  const [isApplied, setIsApplied] = useState(mission.is_applied || false);
  const [applyStatus, setApplyStatus] = useState(mission.apply_status || '');
  const [canWithdraw, setCanWithdraw] = useState(mission.can_withdraw || false);
  const [currentChildId] = useState(mission.current_user_child_id);
  const [isWithdrawing, setIsWithdrawing] = useState(false);

  // Check if user is the host of this mission
  const isHost = user && mission.host && user.id === mission.host.id;

  // Calculate slots filled/remaining text
  const getSlotsText = () => {
    const filled = mission.slots_filled || 0;
    const total = mission.slots_total || 5;
    const remaining = total - filled;

    if (remaining === 0) {
      return 'All slots filled';
    } else if (remaining === 1) {
      return '1 slot remaining';
    } else {
      return `${remaining} slots remaining`;
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Calculate time remaining until mission starts
  useEffect(() => {
    if (!mission.date) return;

    const updateTimeRemaining = () => {
      const now = new Date();
      const missionDate = new Date(mission.date);
      const timeDiff = missionDate - now;

      if (timeDiff <= 0) {
        // Mission has started or passed
        if (mission.status === 'Open') {
          setTimeRemaining('Starting now!');
        } else {
          setTimeRemaining('');
        }
        return;
      }

      // Calculate days, hours, minutes
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setTimeRemaining(`Starts in ${days}d ${hours}h`);
      } else if (hours > 0) {
        setTimeRemaining(`Starts in ${hours}h ${minutes}m`);
      } else {
        setTimeRemaining(`Starts in ${minutes}m`);
      }
    };

    // Update immediately
    updateTimeRemaining();

    // Update every minute
    const interval = setInterval(updateTimeRemaining, 60000);

    return () => clearInterval(interval);
  }, [mission.date, mission.status]);

  // Check if mission is joinable
  const canJoin = mission.status === 'Open' && !isHost && mission.slots_filled < mission.slots_total;

  // Handle apply button click
  const handleApply = (e) => {
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error('Please log in to apply for missions');
      navigate('/login', { state: { from: `/missions/${mission.id}` } });
      return;
    }

    if (isHost) {
      navigate(`/missions/${mission.id}/manage`);
      return;
    }

    if (!canJoin) {
      if (mission.status !== 'Open') {
        toast.error(`Cannot join a mission with status: ${mission.status}`);
      } else if (mission.slots_filled >= mission.slots_total) {
        toast.error('This mission is already full');
      }
      return;
    }

    setIsApplying(true);

    // If onApply prop is provided, call it
    if (onApply) {
      onApply(mission.id)
        .then(() => {
          toast.success('Application submitted successfully!');
          setIsApplied(true);
          setApplyStatus('pending');
        })
        .catch((error) => {
          toast.error(error.message || 'Failed to apply for mission');
        })
        .finally(() => {
          setIsApplying(false);
        });
    } else {
      // Otherwise, navigate to the mission detail page
      navigate(`/missions/${mission.id}`, { state: hideApplyButton ? { hideApplyButton: true } : undefined });
      setIsApplying(false);
    }
  };

  // Toggle bookmark
  const toggleBookmark = async (e) => {
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error('Please log in to bookmark missions');
      return;
    }

    const newBookmarkState = !isBookmarked;
    setIsBookmarked(newBookmarkState);

    if (newBookmarkState) {
      // Add API call to bookmark the mission
      try {
        await missionApi.bookmarkMission(mission.id);
        toast.success('Mission added to bookmarks');
      } catch (error) {
        setIsBookmarked(false); // revert UI
        toast.error(error.message || 'Failed to bookmark mission');
      }
    } else {
      // Optionally, add API call to remove bookmark here
      toast.info('Mission removed from bookmarks');
    }
  };

  // Withdraw application
  const handleWithdraw = async (e) => {
    e.stopPropagation();
    if (!currentChildId || isWithdrawing) return;
    setIsWithdrawing(true);
    try {
      await missionApi.withdrawApplication(mission.id, currentChildId);
      setApplyStatus('withdrawn');
      setCanWithdraw(false);
      toast.success('Application withdrawn');
    } catch (error) {
      toast.error(error.message || 'Failed to withdraw application');
    } finally {
      setIsWithdrawing(false);
    }
  };

  // Share mission
  const shareMission = (e) => {
    e.stopPropagation();
    // Add share functionality
    if (navigator.share) {
      navigator.share({
        title: mission.title,
        text: `Check out this mission: ${mission.title}`,
        url: window.location.origin + `/missions/${mission.id}`
      })
      .then(() => {
        toast.success('Mission shared successfully!');
      })
      .catch((error) => {
        if (error.name !== 'AbortError') {
          toast.error('Failed to share mission');
        }
      });
    } else {
      // Fallback for browsers that don't support navigator.share
      navigator.clipboard.writeText(window.location.origin + `/missions/${mission.id}`)
        .then(() => {
          toast.success('Mission link copied to clipboard!');
        })
        .catch(() => {
          toast.error('Failed to copy mission link');
        });
    }
  };

  const handlePrev = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
  };
  const handleNext = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev + 1) % images.length);
  };

  return (
    <>
    <motion.div
      className="bg-white/95 dark:bg-gray-900/90 rounded-3xl border-2 border-indigo-200 dark:border-yellow-400 shadow-xl dark:shadow-indigo-900/30 hover:shadow-2xl dark:hover:shadow-yellow-400/20 transition-all duration-300 overflow-hidden h-full flex flex-col group relative scale-100 hover:scale-[1.025]"
      initial={{ opacity: 0, y: 20, scale: 1 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      whileHover={{
        y: -10,
        scale: 1.025,
        boxShadow: '0 12px 32px 0 rgba(60, 80, 180, 0.18)',
        transition: { duration: 0.25, ease: 'easeOut' }
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={() => navigate(`/missions/${mission.id}`, { state: hideApplyButton ? { hideApplyButton: true } : undefined })}
    >
      {/* Decorative elements */}
      <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 dark:from-yellow-400/10 dark:to-pink-400/10 rounded-full blur-xl"></div>
      <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-blue-500/10 to-indigo-500/10 dark:from-blue-900/10 dark:to-indigo-900/10 rounded-full blur-xl"></div>

      {/* Mission Image Carousel with Overlay */}
      <div className="relative h-52 overflow-hidden rounded-t-3xl">
        <motion.img
          key={images[currentImage]}
          src={images[currentImage]}
          alt={mission.title}
          className="w-full h-full object-cover rounded-t-3xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        />
        {/* Carousel Arrows */}
        {hasMultipleImages && (
          <>
            <button
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/70 dark:bg-gray-900/80 hover:bg-white dark:hover:bg-gray-900 text-indigo-700 dark:text-yellow-400 rounded-full p-2 shadow-md dark:shadow-yellow-400/10 z-10"
              onClick={handlePrev}
              aria-label="Previous image"
            >
              <FaChevronLeft />
            </button>
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/70 dark:bg-gray-900/80 hover:bg-white dark:hover:bg-gray-900 text-indigo-700 dark:text-yellow-400 rounded-full p-2 shadow-md dark:shadow-yellow-400/10 z-10"
              onClick={handleNext}
              aria-label="Next image"
            >
              <FaChevronRight />
            </button>
          </>
        )}
        {/* Bottom gradient overlay for text readability */}
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/50 to-transparent dark:from-black/80 pointer-events-none rounded-b-none rounded-t-3xl" />
        {/* Dot indicators */}
        {hasMultipleImages && (
          <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-2 z-10">
            {images.map((_, idx) => (
              <span
                key={idx}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentImage ? 'bg-white dark:bg-yellow-400 shadow-lg scale-110' : 'bg-white/40 dark:bg-yellow-400/40'}`}
              />
            ))}
          </div>
        )}
        {/* Image overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent dark:from-black/80"></div>

        {/* Status indicator */}
        {mission.status && (
          <div className={`absolute top-3 left-3 px-3 py-1 rounded-full ${
            mission.status.toLowerCase() === 'open'
              ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300 border border-green-200/50 dark:border-green-700/50'
              : mission.status.toLowerCase() === 'in progress'
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 border border-blue-200/50 dark:border-blue-700/50'
                : mission.status.toLowerCase() === 'completed'
                  ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 border border-purple-200/50 dark:border-purple-700/50'
                  : mission.status.toLowerCase() === 'cancelled'
                    ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300 border border-red-200/50 dark:border-red-700/50'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 border border-gray-200/50 dark:border-gray-700/50'
          } text-xs font-medium flex items-center shadow-sm`}>
            <span className={`w-2 h-2 rounded-full ${
              mission.status.toLowerCase() === 'open'
                ? 'bg-green-500 animate-pulse'
                : mission.status.toLowerCase() === 'in progress'
                  ? 'bg-blue-500 animate-pulse'
                  : mission.status.toLowerCase() === 'completed'
                    ? 'bg-purple-500'
                    : mission.status.toLowerCase() === 'cancelled'
                      ? 'bg-red-500'
                      : 'bg-gray-500'
            } mr-1.5`}></span>
            {mission.status}
          </div>
        )}

        {/* Tags overlay */}
        <div className="absolute bottom-3 left-3 right-3 flex flex-wrap gap-2">
          {mission.level_requirement && (
            <motion.span
              className="bg-indigo-900/80 dark:bg-yellow-400/80 text-indigo-100 dark:text-gray-900 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-indigo-700/50 dark:border-yellow-400/50 shadow-sm flex items-center"
              whileHover={{ scale: 1.05 }}
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              LV{mission.level_requirement.min || 1}-{mission.level_requirement.max || 99}
            </motion.span>
          )}

          {mission.style && (
            <motion.span
              className="bg-purple-900/80 dark:bg-pink-400/80 text-purple-100 dark:text-gray-900 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-purple-700/50 dark:border-pink-400/50 shadow-sm flex items-center"
              whileHover={{ scale: 1.05 }}
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              {mission.style}
            </motion.span>
          )}

          {mission.category && (
            <motion.span
              className="bg-blue-900/80 dark:bg-indigo-400/80 text-blue-100 dark:text-gray-900 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-blue-700/50 dark:border-indigo-400/50 shadow-sm flex items-center"
              whileHover={{ scale: 1.05 }}
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
              </svg>
              {mission.category}
            </motion.span>
          )}
        </div>

        {/* Quick action buttons */}
        <div className="absolute top-3 right-3 flex space-x-2">
          {/* Report button */}
          {isAuthenticated && !isHost && (
            <motion.button
              className="w-8 h-8 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 dark:text-yellow-400 hover:text-indigo-600 dark:hover:text-yellow-300 hover:shadow-md dark:hover:shadow-yellow-400/20 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.stopPropagation();
                setShowReportModal(true);
              }}
              aria-label="Report mission"
            >
              <FaFlag className="w-4 h-4" />
            </motion.button>
          )}

          {/* Bookmark button */}
          <motion.button
            className={`w-8 h-8 ${isBookmarked ? 'bg-indigo-600 dark:bg-yellow-400 text-white dark:text-gray-900' : 'bg-white/80 dark:bg-gray-900/80 text-gray-600 dark:text-yellow-400'} backdrop-blur-sm rounded-full flex items-center justify-center hover:shadow-md dark:hover:shadow-yellow-400/20 transition-all duration-300`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleBookmark}
            aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
          >
            <svg className="w-4 h-4" fill={isBookmarked ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
          </motion.button>

          {/* Share button */}
          <motion.button
            className="w-8 h-8 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 dark:text-yellow-400 hover:text-indigo-600 dark:hover:text-yellow-400 hover:shadow-md dark:hover:shadow-yellow-400/20 transition-all duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={shareMission}
            aria-label="Share mission"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
          </motion.button>
        </div>

        {/* Mission title overlay for small screens */}
        <div className="md:hidden absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/80 to-transparent">
          <h3 className="font-bold text-white text-lg">{mission.title}</h3>
        </div>
      </div>

      {/* Mission Content */}
      <div className="p-5 flex-grow flex flex-col relative">
        {/* Title - hidden on small screens as it's shown in the image overlay */}
        <h3 className="hidden md:block font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-yellow-400 dark:to-pink-400 text-xl mb-3">{mission.title}</h3>

        {/* Enhanced Bounty, Date, and Countdown Grid Layout */}
        <div className="mb-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Credits Card */}
            <motion.div
              className="flex items-center justify-center bg-white/80 dark:bg-gray-900/80 border border-indigo-100 dark:border-yellow-400 rounded-xl shadow-sm dark:shadow-yellow-400/10 py-3 px-4 space-x-2 min-h-[56px]"
              whileHover={{ scale: 1.04 }}
            >
              <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-semibold text-indigo-700 dark:text-yellow-400 text-base">{mission.bounty} <span className="font-normal text-sm">credits</span></span>
            </motion.div>
            {/* Start Date/Time Card */}
            <motion.div
              className="flex items-center justify-center bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-700 rounded-xl shadow-sm dark:shadow-indigo-900/10 py-3 px-4 min-h-[56px]"
              whileHover={{ scale: 1.04 }}
            >
              <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="text-gray-700 dark:text-yellow-400 text-base font-medium">{mission.date ? formatDate(mission.date) : 'Date TBD'}</span>
            </motion.div>
          </div>
          {/* Countdown - visually separated below */}
          {timeRemaining && (
            <motion.div
              className="mt-4 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-yellow-100 dark:to-pink-100 border border-indigo-100 dark:border-yellow-400 rounded-xl py-2 px-4 text-center text-indigo-700 dark:text-yellow-400 text-sm font-semibold shadow-sm dark:shadow-yellow-400/10 tracking-wide"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <svg className="w-4 h-4 inline-block mr-1 text-indigo-400 align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {timeRemaining}
            </motion.div>
          )}
        </div>

        {/* Slots information */}
        <div className="mb-5">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600 font-medium flex items-center">
              <svg className="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span className="dark:text-yellow-400">{getSlotsText()}</span>
            </span>
            <span className="text-sm font-bold text-indigo-700 bg-indigo-50 px-2 py-0.5 rounded-md">
              {mission.slots_filled || 0}/{mission.slots_total || 5}
            </span>
          </div>
          <div className="w-full bg-gray-100 rounded-full h-2.5 overflow-hidden shadow-inner">
            <motion.div
              className="bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-yellow-400 dark:to-pink-400 h-2.5 rounded-full relative"
              initial={{ width: 0 }}
              animate={{ width: `${((mission.slots_filled || 0) / (mission.slots_total || 5)) * 100}%` }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              {/* Animated shine effect */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer transform -translate-x-full"></div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Host information */}
        <div className="mt-auto pt-4 border-t border-indigo-50 flex items-center justify-between">
          <div className="flex items-center">
            <div className="relative">
              <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gradient-to-br from-indigo-100 to-blue-100 p-0.5">
                <img
                  src={mission.host?.avatar || '/images/default-avatar.jpg'}
                  alt={mission.host?.name || 'Host'}
                  className="w-full h-full object-cover rounded-full"
                />
              </div>
              {/* Online status indicator */}
              {mission.host?.online && (
                <span className="absolute bottom-0 right-2 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
              )}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800 dark:text-yellow-400">
                {mission.host?.name || 'Anonymous Host'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-300 flex items-center">
                <svg className="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                LV{mission.host?.level || '??'}
              </p>
            </div>
          </div>

          {/* Action button - different states based on mission status and user role */}
          <AnimatePresence>
            {(!hideApplyButton && (isHovered || isHost || mission.status !== 'Open')) && (
              <motion.button
                className={`text-sm font-medium px-4 py-1.5 rounded-lg shadow-md flex items-center ${
                  isApplying ? 'bg-gray-400 cursor-not-allowed' :
                  isHost ? 'bg-green-600 hover:bg-green-700 text-white' :
                  mission.status === 'Open' ? 'bg-gradient-to-r from-indigo-600 to-blue-600 text-white hover:shadow-indigo-500/20' :
                  mission.status === 'In Progress' ? 'bg-blue-600 text-white' :
                  mission.status === 'Completed' ? 'bg-purple-600 text-white' :
                  'bg-gray-600 text-white'
                }`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.15 }}
                onClick={handleApply}
                disabled={isApplying}
              >
                {isApplying ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Applying...
                  </>
                ) : isHost ? (
                  <>
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Manage
                  </>
                ) : mission.status === 'Open' ? (
                  <>
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                    </svg>
                    Apply Now
                  </>
                ) : mission.status === 'In Progress' ? (
                  <>
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View Details
                  </>
                ) : mission.status === 'Completed' ? (
                  <>
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    View Results
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    View Details
                  </>
                )}
              </motion.button>
            )}
          </AnimatePresence>
          {hideApplyButton && isApplied && (
            <div className="mt-2 text-xs text-center">
              {applyStatus === 'pending' && (
                <motion.button
                  className="px-3 py-1 rounded-md bg-red-500 text-white shadow"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleWithdraw}
                  disabled={isWithdrawing}
                >
                  {isWithdrawing ? 'Withdrawing...' : 'Withdraw Application'}
                </motion.button>
              )}
              {applyStatus === 'approved' && canWithdraw && (
                <motion.button
                  className="px-3 py-1 rounded-md bg-red-500 text-white shadow"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleWithdraw}
                  disabled={isWithdrawing}
                >
                  {isWithdrawing ? 'Withdrawing...' : 'Withdraw Application'}
                </motion.button>
              )}
              {applyStatus === 'approved' && !canWithdraw && (
                <span className="font-semibold text-green-600">Application Approved</span>
              )}
              {applyStatus === 'rejected' && (
                <span className="font-semibold text-red-600">Application Rejected</span>
              )}
              {applyStatus === 'withdrawn' && (
                <span className="text-gray-600">You have withdrawn from this Mission</span>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
    <ReportMissionModal
      isOpen={showReportModal}
      onClose={() => setShowReportModal(false)}
      missionId={mission.id}
    />
    </>
  );
};

export default MissionCard;
