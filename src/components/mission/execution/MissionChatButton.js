import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useToast } from '../../common/ToastProvider';
import { findOrCreateMissionChat } from '../../../utils/missionChatUtils';

/**
 * MissionChatButton Component
 * 
 * This component provides a button to navigate to the chat page with the mission chat context.
 * It replaces the MissionChat component in the mission execution page.
 */
const MissionChatButton = ({ 
  mission, 
  currentUser,
  className = ''
}) => {
  const navigate = useNavigate();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // Handle opening the mission chat
  const handleOpenMissionChat = async () => {
    if (!mission || !mission.id) {
      toast.error('Mission information is missing');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Get all participants including the host
      const participants = [
        ...(mission.participants || []).map(p => p.id),
        mission.host?.id
      ].filter(Boolean); // Remove any undefined/null values
      
      // Make sure current user is included
      if (currentUser?.id && !participants.includes(currentUser.id)) {
        participants.push(currentUser.id);
      }
      
      // Find or create a chat for this mission
      const missionChat = await findOrCreateMissionChat(
        mission.id,
        mission.title,
        participants
      );
      
      // Navigate to the chat page with the mission chat selected
      navigate(`/chat/${missionChat.id}`);
      
    } catch (error) {
      console.error('Error opening mission chat:', error);
      toast.error('Failed to open mission chat');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-xl shadow-sm border border-indigo-100/50 overflow-hidden ${className}`}
    >
      <div className="p-6 flex flex-col items-center text-center">
        <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        
        <h3 className="text-lg font-medium text-gray-800 mb-2">Mission Chat</h3>
        
        <p className="text-gray-600 mb-6">
          Communicate with all mission participants in the chat.
        </p>
        
        <button
          onClick={handleOpenMissionChat}
          disabled={isLoading}
          className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Opening Chat...
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Open Mission Chat
            </>
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default MissionChatButton;
