import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import bankAccountService from '../services/bankAccountService';

const BankAccounts = ({ 
  onSelectAccount = null, 
  selectedAccountId = null,
  isSelectionMode = false
}) => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteConfirmId, setDeleteConfirmId] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    bank_name: '',
    account_number: '',
    account_holder_name: '',
    swift_code: '',
    is_primary: false
  });

  // Fetch bank accounts on component mount
  useEffect(() => {
    fetchBankAccounts();
  }, []);

  // Fetch bank accounts from API
  const fetchBankAccounts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await bankAccountService.getBankAccounts();
      setBankAccounts(response.data.bank_accounts);
    } catch (err) {
      console.error('Error fetching bank accounts:', err);
      setError('Failed to load bank accounts. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (editingAccount) {
        // Update existing account
        await bankAccountService.updateBankAccount(editingAccount.id, formData);
      } else {
        // Add new account
        await bankAccountService.addBankAccount(formData);
      }
      
      // Refresh bank accounts list
      await fetchBankAccounts();
      
      // Reset form
      resetForm();
    } catch (err) {
      console.error('Error saving bank account:', err);
      setError(err.message || 'Failed to save bank account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async (id) => {
    setIsSubmitting(true);
    try {
      await bankAccountService.deleteBankAccount(id);
      await fetchBankAccounts();
      setDeleteConfirmId(null);
    } catch (err) {
      console.error('Error deleting bank account:', err);
      setError(err.message || 'Failed to delete bank account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Set account as primary
  const handleSetPrimary = async (id) => {
    try {
      await bankAccountService.setPrimaryBankAccount(id);
      await fetchBankAccounts();
    } catch (err) {
      console.error('Error setting primary account:', err);
      setError(err.message || 'Failed to set primary account. Please try again.');
    }
  };

  // Edit bank account
  const handleEdit = (account) => {
    setFormData({
      bank_name: account.bank_name,
      account_number: account.account_number,
      account_holder_name: account.account_holder_name,
      swift_code: account.swift_code || '',
      is_primary: account.is_primary
    });
    setEditingAccount(account);
    setShowAddForm(true);
  };

  // Reset form state
  const resetForm = () => {
    setFormData({
      bank_name: '',
      account_number: '',
      account_holder_name: '',
      swift_code: '',
      is_primary: false
    });
    setEditingAccount(null);
    setShowAddForm(false);
  };

  // Animation variants
  const formVariants = {
    hidden: { opacity: 0, height: 0, overflow: 'hidden' },
    visible: { opacity: 1, height: 'auto', overflow: 'visible' },
    exit: { opacity: 0, height: 0, overflow: 'hidden' }
  };

  // Loading skeleton
  if (isLoading && !bankAccounts.length) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <div className="h-6 bg-gray-200 rounded w-40 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-xl animate-pulse"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Add Account button */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold text-gray-900">
          {isSelectionMode ? 'Select Bank Account' : 'My Bank Accounts'}
        </h2>
        {!isSelectionMode && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowAddForm(!showAddForm)}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center"
            disabled={isSubmitting}
          >
            {showAddForm ? (
              <>Cancel</>
            ) : (
              <>
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Bank Account
              </>
            )}
          </motion.button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="p-4 mb-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">{typeof error === 'object' ? JSON.stringify(error) : String(error)}</p>
        </div>
      )}

      {/* Add/Edit Account Form */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={formVariants}
            transition={{ duration: 0.3 }}
            className="bg-gray-50 p-4 rounded-xl mb-6 border border-gray-200"
          >
            <h3 className="text-md font-medium mb-4">
              {editingAccount ? 'Edit Bank Account' : 'Add New Bank Account'}
            </h3>
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label htmlFor="bank_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Bank Name*
                  </label>
                  <select
                    id="bank_name"
                    name="bank_name"
                    value={formData.bank_name}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  >
                    <option value="">Select a bank</option>
                    <option value="Maybank">Maybank</option>
                    <option value="CIMB Bank">CIMB Bank</option>
                    <option value="Public Bank">Public Bank</option>
                    <option value="RHB Bank">RHB Bank</option>
                    <option value="Hong Leong Bank">Hong Leong Bank</option>
                    <option value="Bank Islam">Bank Islam</option>
                    <option value="OCBC Bank">OCBC Bank</option>
                    <option value="HSBC Bank">HSBC Bank</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="account_number" className="block text-sm font-medium text-gray-700 mb-1">
                    Account Number*
                  </label>
                  <input
                    type="text"
                    id="account_number"
                    name="account_number"
                    value={formData.account_number}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="account_holder_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Account Holder Name*
                  </label>
                  <input
                    type="text"
                    id="account_holder_name"
                    name="account_holder_name"
                    value={formData.account_holder_name}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="swift_code" className="block text-sm font-medium text-gray-700 mb-1">
                    SWIFT/BIC Code <span className="text-gray-500 text-xs">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="swift_code"
                    name="swift_code"
                    value={formData.swift_code}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              <div className="flex items-center mb-4">
                <input
                  type="checkbox"
                  id="is_primary"
                  name="is_primary"
                  checked={formData.is_primary}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="is_primary" className="ml-2 block text-sm text-gray-700">
                  Set as primary account
                </label>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors flex items-center"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    'Save Bank Account'
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>

      {/* No accounts message */}
      {!isLoading && bankAccounts.length === 0 && (
        <div className="bg-gray-50 rounded-xl p-6 text-center">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
          <p className="text-gray-500 mb-4">You don't have any bank accounts yet</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors inline-flex items-center"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Your First Bank Account
          </button>
        </div>
      )}

      {/* Bank accounts list */}
      {bankAccounts.length > 0 && (
        <div className="space-y-3">
          {bankAccounts.map(account => (
            <div 
              key={account.id} 
              className={`relative bg-white rounded-xl border p-4 transition-all ${
                isSelectionMode ? 'cursor-pointer hover:shadow-md' : ''
              } ${selectedAccountId === account.id ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-200'}`}
              onClick={() => isSelectionMode && onSelectAccount && onSelectAccount(account)}
            >
              {account.is_primary && (
                <div className="absolute top-2 right-2">
                  <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    Primary
                  </span>
                </div>
              )}
              
              <div className="flex items-start">
                <div className="bg-gray-100 rounded-lg p-3 mr-4">
                  <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{account.bank_name}</h3>
                  <p className="text-gray-500 text-sm mt-1">
                    {/* Mask account number for privacy */}
                    {account.account_number.replace(/^(.{4})(.*)(.{4})$/, '$1••••$3')}
                  </p>
                  <p className="text-gray-500 text-sm">{account.account_holder_name}</p>
                </div>
                
                {!isSelectionMode && (
                  <div className="flex space-x-2 ml-4">
                    {!account.is_primary && (
                      <button
                        onClick={() => handleSetPrimary(account.id)}
                        className="p-1 text-gray-400 hover:text-indigo-600 transition-colors"
                        title="Set as primary"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </button>
                    )}
                    <button
                      onClick={() => handleEdit(account)}
                      className="p-1 text-gray-400 hover:text-indigo-600 transition-colors"
                      title="Edit account"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </button>
                    {!account.is_primary && (
                      <button
                        onClick={() => setDeleteConfirmId(account.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete account"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                )}
                
                {isSelectionMode && selectedAccountId === account.id && (
                  <div className="ml-4">
                    <div className="bg-indigo-500 rounded-full p-1 text-white">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Delete confirmation */}
              {deleteConfirmId === account.id && (
                <div className="mt-3 p-3 bg-red-50 rounded-lg">
                  <p className="text-sm text-red-700 mb-2">Are you sure you want to delete this bank account?</p>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => setDeleteConfirmId(null)}
                      className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => handleDeleteConfirm(account.id)}
                      className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors flex items-center"
                      disabled={isSubmitting}
                    >
                      {isSubmitting && deleteConfirmId === account.id ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Deleting...
                        </>
                      ) : (
                        'Yes, Delete'
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BankAccounts;