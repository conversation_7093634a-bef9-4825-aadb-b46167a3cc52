import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactDOM from 'react-dom';
import reportService from '../services/reportService';
import { useToast } from './common/ToastProvider';

const ReportMissionModal = ({ isOpen, onClose, missionId }) => {
  const { success: showSuccessToast, error: showErrorToast } = useToast();
  const [types, setTypes] = useState([]);
  const [loadingTypes, setLoadingTypes] = useState(false);
  const [form, setForm] = useState({ reportType: '', description: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isOpen) return;
    const fetchTypes = async () => {
      setLoadingTypes(true);
      try {
        const res = await reportService.getReportTypes();
        setTypes(Array.isArray(res) ? res : res.data || []);
      } catch (err) {
        console.error('Failed to fetch report types:', err);
        showErrorToast('Failed to load report types');
        setTypes([]);
      } finally {
        setLoadingTypes(false);
      }
    };
    fetchTypes();
    setForm({ reportType: '', description: '' });
    setError(null);
  }, [isOpen, showErrorToast]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const description = form.description.trim();
    if (!form.reportType || description.length < 10 || description.length > 1000) {
      setError('Please select a type and provide a description between 10 and 1000 characters.');
      return;
    }
    setLoading(true);
    try {
      await reportService.reportMission(missionId, {
        report_type: form.reportType,
        description,
      });
      showSuccessToast('Report submitted');
      onClose();
    } catch (err) {
      console.error('Failed to submit report:', err);
      setError('Failed to submit report.');
      showErrorToast('Failed to submit report');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <AnimatePresence>
          {isOpen && (
            <motion.div
              className="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/50 dark:bg-black/80 backdrop-blur-sm px-4 pb-4 sm:pb-0"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={onClose}
            >
              <motion.div
                className="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-t-3xl sm:rounded-3xl shadow-2xl dark:shadow-red-900/20 w-full max-w-lg mx-auto overflow-hidden border border-gray-200/50 dark:border-gray-700/50"
                initial={{ scale: 0.95, y: 100, opacity: 0 }}
                animate={{ scale: 1, y: 0, opacity: 1 }}
                exit={{ scale: 0.95, y: 100, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                transition={{ type: "spring", duration: 0.5, bounce: 0.1 }}
              >
                {/* Header with gradient */}
                <div className="relative bg-gradient-to-br from-red-500 to-orange-600 dark:from-red-600 dark:to-orange-700 px-6 py-8 sm:px-8 sm:py-10">
                  <div className="absolute inset-0 bg-black/10 dark:bg-black/20"></div>
                  
                  {/* Close button */}
                  <motion.button
                    onClick={onClose}
                    className="absolute top-4 right-4 p-2.5 bg-white/20 dark:bg-black/20 backdrop-blur-md rounded-xl border border-white/30 dark:border-gray-600/50 hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-300 shadow-lg hover:shadow-xl z-20 group"
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.2, delay: 0.1 }}
                    aria-label="Close report modal"
                  >
                    <svg className="w-5 h-5 text-white group-hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </motion.button>
    
                  {/* Title and icon */}
                  <div className="relative flex items-center justify-center gap-3">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="p-3 bg-white/20 dark:bg-black/20 rounded-2xl backdrop-blur-sm"
                    >
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
                      </svg>
                    </motion.div>
                    <motion.h2 
                      className="text-2xl sm:text-3xl font-bold text-white tracking-tight text-center"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                    >
                      Report Talent
                    </motion.h2>
                  </div>
    
                  {/* Subtitle */}
                  <motion.p
                    className="text-center text-white/90 text-base mt-3"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    Help us maintain a safe community
                  </motion.p>
                </div>
    
                {/* Content */}
                <div className="px-6 py-6 sm:px-8 sm:py-8">
                  <motion.form
                    onSubmit={handleSubmit}
                    className="space-y-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    {/* Report Type Selection */}
                    <div className="space-y-3">
                      <label className="block text-base font-semibold text-gray-900 dark:text-gray-100 mb-3">
                        What are you reporting?
                      </label>
                      {loadingTypes ? (
                        <div className="flex items-center justify-center py-8">
                          <div className="flex items-center gap-3">
                            <div className="w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                            <span className="text-gray-600 dark:text-gray-400 text-sm">Loading report types...</span>
                          </div>
                        </div>
                      ) : (
                        <div className="relative">
                          <select
                            value={form.reportType}
                            onChange={(e) => setForm({ ...form, reportType: e.target.value })}
                            className="w-full px-4 py-3.5 rounded-2xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm font-medium transition-all duration-300 focus:border-red-500 dark:focus:border-red-400 focus:ring-4 focus:ring-red-500/20 dark:focus:ring-red-400/20 focus:outline-none appearance-none cursor-pointer hover:border-gray-300 dark:hover:border-gray-600"
                          >
                            <option value="" disabled>Choose a reason...</option>
                            {types.map((t) => (
                              <option key={t.id} value={t.id} className="py-2">
                                {t.name || t.title}
                              </option>
                            ))}
                          </select>
                          <div className="absolute right-4 top-1/2 -translate-y-1/2 pointer-events-none">
                            <svg className="w-5 h-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
    
                    {/* Description */}
                    <div className="space-y-3">
                      <label className="block text-base font-semibold text-gray-900 dark:text-gray-100">
                        Additional Details
                      </label>
                      <div className="relative">
                        <textarea
                          rows="4"
                          value={form.description}
                          onChange={(e) => setForm({ ...form, description: e.target.value })}
                          className="w-full px-4 py-3.5 rounded-2xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm font-medium transition-all duration-300 focus:border-red-500 dark:focus:border-red-400 focus:ring-4 focus:ring-red-500/20 dark:focus:ring-red-400/20 focus:outline-none resize-none placeholder-gray-400 dark:placeholder-gray-500"
                          placeholder="Please provide specific details about what happened. The more information you provide, the better we can address the issue."
                        />
                        <div className="absolute bottom-3 right-3 text-xs text-gray-400 dark:text-gray-500 font-medium">
                          {form.description.length}/500
                        </div>
                      </div>
                    </div>
    
                    {/* Error Message */}
                    <AnimatePresence>
                      {error && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.95, y: -10 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          exit={{ opacity: 0, scale: 0.95, y: -10 }}
                          className="flex items-center gap-3 p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800/50 rounded-2xl"
                        >
                          <div className="p-1 bg-red-100 dark:bg-red-900/50 rounded-lg flex-shrink-0">
                            <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <p className="text-sm font-medium text-red-800 dark:text-red-300">{error}</p>
                        </motion.div>
                      )}
                    </AnimatePresence>
    
                    {/* Action Buttons */}
                    <div className="flex flex-col-reverse sm:flex-row gap-3 pt-4">
                      <motion.button
                        type="button"
                        onClick={onClose}
                        className="flex-1 px-6 py-3.5 rounded-2xl border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-semibold text-base transition-all duration-300 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-750 focus:outline-none focus:ring-4 focus:ring-gray-500/20"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        type="submit"
                        disabled={loading || !form.reportType || !form.description.trim()}
                        className="flex-1 px-6 py-3.5 rounded-2xl bg-gradient-to-r from-red-500 to-orange-600 dark:from-red-600 dark:to-orange-700 text-white font-bold text-base transition-all duration-300 hover:from-red-600 hover:to-orange-700 dark:hover:from-red-700 dark:hover:to-orange-800 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-4 focus:ring-red-500/30 shadow-lg hover:shadow-xl disabled:hover:shadow-lg flex items-center justify-center gap-2"
                        whileHover={{ scale: loading ? 1 : 1.02 }}
                        whileTap={{ scale: loading ? 1 : 0.98 }}
                      >
                        {loading ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            <span>Submitting...</span>
                          </>
                        ) : (
                          <>
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            <span>Submit Report</span>
                          </>
                        )}
                      </motion.button>
                    </div>
                  </motion.form>
                </div>
    
                {/* Bottom handle for mobile */}
                <div className="flex justify-center pb-4 sm:hidden">
                  <div className="w-12 h-1.5 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>,
    document.body
  );
};

export default ReportMissionModal;

