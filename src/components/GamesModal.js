import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import useTranslation from '../hooks/useTranslation';

// Alphabetical sections for game categories
const LETTERS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

const GamesModal = ({ isOpen, onClose }) => {
    const [activeTab, setActiveTab] = useState('popular');
    const [games, setGames] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const modalRef = useRef(null);
    const { isAuthenticated } = useAuth();
    const [cache, setCache] = useState({
        popular: { data: null, timestamp: null },
        all: { data: null, timestamp: null }
    });
    const navigate = useNavigate();
    const { t } = useTranslation(['games', 'common']);

    // Prevent body scroll when modal is open
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    // Close on click outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // Fetch popular games
    const fetchPopularGames = useCallback(async () => {
        try {
            const response = await api.get('/homepage/popular-service-types');
            if (response && response.data) {
                const formattedGames = response.data.map(game => ({
                    id: game.id,
                    name: game.name,
                    image: game.icon_path ? `${process.env.REACT_APP_CDN_URL}/${game.icon_path}` : '/images/games/default-game.jpg',
                    rating: game.rating || 4.0,
                    popular: true,
                    description: game.description || ''
                }));
                return formattedGames;
            }
            return [];
        } catch (err) {
            console.error('Error fetching popular games:', err);
            throw new Error(t('games:error.failedPopular'));
        }
    }, [t]);

    // Fetch all games using service types endpoint
    const fetchAllGames = useCallback(async () => {
        try {
            const response = await api.get('/talents/service-types', {
                params: {
                    service_category_id: 1
                }
            });

            if (response && response.data) {
                const formattedGames = response.data.map(game => ({
                    id: game.id,
                    name: game.name,
                    image: game.icon_path ? `${process.env.REACT_APP_CDN_URL}/${game.icon_path}` : '/images/games/default-game.jpg',
                    rating: game.rating || 4.0,
                    popular: game.is_popular || false,
                    description: game.description || ''
                }));
                return formattedGames;
            }
            return [];
        } catch (err) {
            console.error('Error fetching service types:', err);
            if (err.response?.status === 401) {
                throw new Error(t('games:error.unauthorized'));
            }
            throw new Error(t('games:error.failed'));
        }
    }, [t]);

    // Fetch games data with caching
    useEffect(() => {
        let isMounted = true;

        const fetchGames = async () => {
            if (!isOpen) return;

            setLoading(true);
            setError(null);

            try {
                const cacheKey = activeTab;
                const cacheData = cache[cacheKey];
                const now = Date.now();
                const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

                // Use cached data if available and not expired
                if (cacheData && cacheData.data && (now - cacheData.timestamp < CACHE_DURATION)) {
                    if (isMounted) {
                        setGames(cacheData.data);
                        setLoading(false);
                    }
                    return;
                }

                // Fetch fresh data
                const fetchedGames = activeTab === 'popular' 
                    ? await fetchPopularGames()
                    : await fetchAllGames();

                if (isMounted) {
                    // Update cache
                    setCache(prev => ({
                        ...prev,
                        [cacheKey]: {
                            data: fetchedGames,
                            timestamp: now
                        }
                    }));

                    setGames(fetchedGames);
                    setLoading(false);
                }
            } catch (err) {
                if (isMounted) {
                console.error('Error fetching games:', err);
                    setError(err.message);
                setGames([]);
                setLoading(false);
                }
            }
        };

        fetchGames();

        return () => {
            isMounted = false;
        };
    }, [isOpen, activeTab, fetchPopularGames, fetchAllGames, cache]);

    // Group games alphabetically
    const alphabeticalGames = {};
    LETTERS.forEach(letter => {
        alphabeticalGames[letter] = games.filter(game =>
            game.name.toUpperCase().startsWith(letter)
        );
    });

    // Get popular games
    const popularGames = games.filter(game => game.popular);

    // Enhanced game card with better visuals and interactions
    const renderGameCard = (game) => (
        <div
            key={game.id}
            className="bg-gradient-to-b from-white to-indigo-50/30 dark:from-gray-900/60 dark:to-indigo-900/40 rounded-2xl shadow-md dark:shadow-indigo-900/30 overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg dark:hover:shadow-indigo-900/50 border border-indigo-100/50 dark:border-indigo-900/60 group relative cursor-pointer"
            onClick={() => {
                navigate('/talent', { state: { initialFilters: { serviceCategoryId: 1, serviceTypeId: game.id } } });
                onClose();
            }}
        >
            {/* Image Container with Overlay */}
            <div className="h-40 overflow-hidden relative bg-gray-100 dark:bg-gray-900">
                <img
                    src={game.image || "https://via.placeholder.com/300x180?text=Game+Image"}
                    alt={game.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 filter group-hover:brightness-110"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent dark:from-indigo-900/80 dark:via-indigo-900/30 dark:to-transparent opacity-60 group-hover:opacity-40 transition-opacity"></div>

                {/* Game Rating Badge */}
                <div className="absolute top-3 right-3 bg-white/90 dark:bg-indigo-900/90 backdrop-blur-sm px-2 py-1 rounded-lg shadow-md dark:shadow-indigo-900/30 flex items-center">
                    <svg className="w-3.5 h-3.5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="text-xs font-medium text-gray-800 dark:text-indigo-200 ml-1">{game.rating}</span>
                </div>

                {/* Popular Badge */}
                {game.popular && (
                    <div className="absolute top-3 left-3 bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-700 dark:to-blue-700 text-white dark:text-indigo-100 px-2.5 py-1 rounded-lg shadow-md dark:shadow-indigo-900/30 flex items-center">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span className="text-xs font-medium">{t('games:badgePopular')}</span>
                    </div>
                )}

                {/* Game Name on Image */}
                <div className="absolute bottom-0 left-0 w-full p-3">
                    <h3 className="font-bold text-white dark:text-indigo-100 text-shadow-sm truncate">{game.name}</h3>
                </div>
            </div>

            {/* Game Info Section */}
            <div className="p-3 bg-white dark:bg-gray-900">
                {/* Game Stats */}
                <div className="flex justify-between items-center text-xs text-gray-600 dark:text-indigo-200">
                    <div className="flex items-center">
                        <svg className="w-3.5 h-3.5 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span>{t('games:players', { count: Math.floor(Math.random() * 10) + 1 })}</span>
                    </div>
                    <div className="flex items-center">
                        <svg className="w-3.5 h-3.5 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{t('games:avgTime', { count: Math.floor(Math.random() * 60) + 30 })}</span>
                    </div>
                </div>

                {/* View Button - Appears on Hover */}
                <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity transform translate-y-2 group-hover:translate-y-0">
                    <button
                        className="w-full py-1.5 bg-indigo-600 dark:bg-indigo-700 hover:bg-indigo-700 dark:hover:bg-indigo-800 text-white dark:text-indigo-100 text-xs font-medium rounded-lg transition-colors flex items-center justify-center"
                        onClick={e => {
                            e.stopPropagation();
                            navigate('/talent', { state: { initialFilters: { serviceCategoryId: 1, serviceTypeId: game.id } } });
                            onClose();
                        }}
                    >
                        <span>{t('games:viewTalents')}</span>
                        <svg className="w-3.5 h-3.5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 bg-black/70 dark:bg-black/90 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                        className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl dark:shadow-indigo-900/40 w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col border border-indigo-100/50 dark:border-indigo-900/60"
                        ref={modalRef}
                    >
                        {/* Enhanced Modal Header with Banner */}
                        <div className="relative">
                            {/* Background Banner */}
                            <div className="h-32 bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-900 dark:to-blue-900 relative overflow-hidden">
                                {/* Background Pattern */}
                                <div className="absolute inset-0 bg-grid-white/[0.05] dark:bg-grid-indigo-900/[0.08] bg-[length:20px_20px]"></div>

                                {/* Decorative Elements */}
                                <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/10 dark:bg-indigo-900/20 rounded-full blur-xl"></div>
                                <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 dark:bg-indigo-900/20 rounded-full blur-lg"></div>

                                {/* Close Button */}
                                <button
                                    onClick={onClose}
                                    className="absolute top-4 right-4 p-2 rounded-full bg-white/20 dark:bg-indigo-900/40 backdrop-blur-sm hover:bg-white/30 dark:hover:bg-indigo-900/60 transition-colors text-white z-10"
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>

                                {/* Header Content */}
                                <div className="absolute inset-0 flex items-center p-6">
                                    <div className="w-16 h-16 rounded-xl bg-white/10 dark:bg-indigo-900/30 backdrop-blur-sm flex items-center justify-center mr-4 shadow-lg dark:shadow-indigo-900/30">
                                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold text-white text-left dark:text-indigo-200">{t('games:libraryTitle')}</h2>
                                        <p className="text-indigo-100 dark:text-indigo-300 text-sm mt-1">{t('games:libraryDescription')}</p>
                                    </div>
                                </div>
                            </div>

                            {/* Enhanced Tab Navigation */}
                            <div className="bg-white dark:bg-gray-900 px-6 py-4 border-b border-indigo-100/50 dark:border-indigo-900/60 flex flex-wrap items-center justify-between gap-4">
                                <div className="flex items-center">
                                    <span className="text-gray-500 dark:text-indigo-300 text-sm mr-3">{t('games:browseBy')}</span>
                                    <div className="flex space-x-2">
                                        <button
                                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                                                activeTab === 'popular'
                                                    ? 'bg-indigo-600 text-white shadow-md dark:bg-indigo-700 dark:text-indigo-100'
                                                    : 'bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-100 dark:hover:bg-indigo-800'
                                            }`}
                                            onClick={() => setActiveTab('popular')}
                                        >
                                            <div className="flex items-center">
                                                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                                </svg>
                                                <span>{t('games:popular')}</span>
                                            </div>
                                        </button>
                                        <button
                                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                                                activeTab === 'alphabetical'
                                                    ? 'bg-indigo-600 text-white shadow-md dark:bg-indigo-700 dark:text-indigo-100'
                                                    : 'bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-100 dark:hover:bg-indigo-800'
                                            }`}
                                            onClick={() => setActiveTab('alphabetical')}
                                        >
                                            <div className="flex items-center">
                                                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                                                </svg>
                                                <span>{t('games:tabAlphabetical')}</span>
                                            </div>
                                        </button>
                                    </div>
                                </div>

                                {/* Search Input */}
                                {/* <div className="relative">
                                    <input
                                        type="text"
                                        placeholder="Search games..."
                                        className="py-2 pl-9 pr-4 rounded-lg text-sm border border-indigo-100 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 w-64"
                                    />
                                    <svg className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div> */}
                            </div>
                        </div>

                        {/* Enhanced Modal Content */}
                        <div className="overflow-y-auto p-6 flex-grow custom-scrollbar bg-white dark:bg-gray-900">
                            {loading ? (
                                <div className="flex items-center justify-center h-64 bg-white dark:bg-gray-900 rounded-xl">
                                    <div className="flex flex-col items-center">
                                        <div className="flex space-x-2 mb-3">
                                            <div className="w-3 h-3 bg-indigo-500 dark:bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                                            <div className="w-3 h-3 bg-indigo-500 dark:bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
                                            <div className="w-3 h-3 bg-indigo-500 dark:bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
                                        </div>
                                        <p className="text-indigo-500 dark:text-indigo-300 text-sm">{t('games:loading')}</p>
                                    </div>
                                </div>
                            ) : error ? (
                                <div className="text-center p-8 bg-red-50 dark:bg-red-900 rounded-xl border border-red-100 dark:border-red-700">
                                    <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <h3 className="text-lg font-bold text-red-700 dark:text-red-300 mb-2">{t('games:error.unableToLoad')}</h3>
                                    <p className="text-red-600 dark:text-red-200 mb-4">{error}</p>
                                    <button
                                        className="px-4 py-2 bg-red-600 dark:bg-red-700 text-white rounded-lg hover:bg-red-700 dark:hover:bg-red-800 transition-colors shadow-md"
                                        onClick={() => window.location.reload()}
                                    >
                                        {t('games:retry')}
                                    </button>
                                </div>
                            ) : activeTab === 'popular' ? (
                                <div>
                                    <h3 className="text-xl font-bold text-gray-800 dark:text-indigo-200 mb-5 flex items-center">
                                        <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                        {t('games:popular')}
                                    </h3>
                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-5">
                                        {popularGames.length > 0 ? (
                                            popularGames.map(game => renderGameCard(game))
                                        ) : (
                                            <div className="col-span-full text-center py-12 bg-indigo-50 dark:bg-indigo-900 rounded-xl border border-indigo-100 dark:border-indigo-900/60">
                                                <svg className="w-12 h-12 text-indigo-400 dark:text-indigo-200 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-indigo-700 dark:text-indigo-200 font-medium">{t('games:noPopular')}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ) : (
                                <div>
                                    <h3 className="text-xl font-bold text-gray-800 dark:text-indigo-200 mb-5 flex items-center">
                                        <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                                        </svg>
                                        {t('games:directory')}
                                    </h3>

                                    {/* Alphabet Quick Navigation */}
                                    <div className="flex flex-wrap gap-1 mb-6 bg-indigo-50 dark:bg-indigo-900 p-3 rounded-lg border border-indigo-100 dark:border-indigo-900/60">
                                        {LETTERS.map(letter => {
                                            const hasGames = alphabeticalGames[letter].length > 0;
                                            return (
                                                <button
                                                    key={letter}
                                                    className={`w-7 h-7 flex items-center justify-center rounded-md text-xs font-medium ${
                                                        hasGames
                                                            ? 'bg-white dark:bg-indigo-800 text-indigo-700 dark:text-indigo-200 shadow-sm hover:bg-indigo-600 dark:hover:bg-indigo-700 hover:text-white transition-colors'
                                                            : 'text-indigo-300 dark:text-indigo-700 bg-gray-200 dark:bg-gray-800 hover:bg-indigo-400 dark:hover:bg-indigo-700 hover:text-white cursor-default'
                                                    }`}
                                                    disabled={!hasGames}
                                                    onClick={() => {
                                                        if (hasGames) {
                                                            document.getElementById(`section-${letter}`).scrollIntoView({ behavior: 'smooth' });
                                                        }
                                                    }}
                                                >
                                                    {letter}
                                                </button>
                                            );
                                        })}
                                    </div>

                                    <div className="space-y-8">
                                        {LETTERS.map(letter => {
                                            const gamesForLetter = alphabeticalGames[letter];
                                            if (gamesForLetter.length === 0) return null;

                                            return (
                                                <div key={letter} id={`section-${letter}`} className="scroll-mt-4">
                                                    <div className="flex items-center mb-4">
                                                        <div className="w-10 h-10 rounded-xl bg-indigo-600 dark:bg-indigo-800 text-white dark:text-indigo-200 flex items-center justify-center font-bold mr-3 shadow-md dark:shadow-indigo-900/30">
                                                            {letter}
                                                        </div>
                                                        <h3 className="text-lg font-bold text-gray-800 dark:text-indigo-200 border-b border-indigo-100 dark:border-indigo-900/60 pb-1 flex-grow">
                                                            {t('games:letterGames', { letter })}
                                                        </h3>
                                                    </div>
                                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-5">
                                                        {gamesForLetter.map(game => renderGameCard(game))}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Footer with Stats */}
                        <div className="bg-indigo-50/80 dark:bg-indigo-900/80 backdrop-blur-sm border-t border-indigo-100 dark:border-indigo-900/60 p-4">
                            <div className="flex flex-wrap justify-between items-center">
                                <p className="text-sm text-gray-600 dark:text-indigo-200">
                                    {t('games:showingCount', { count: games.length })}
                                </p>
                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={onClose}
                                        className="px-4 py-2 bg-white dark:bg-indigo-900 border border-indigo-200 dark:border-indigo-900/60 text-indigo-700 dark:text-indigo-200 rounded-lg text-sm font-medium hover:bg-indigo-50 dark:hover:bg-indigo-800 transition-colors"
                                    >
                                        {t('common:buttons.close')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default GamesModal;