import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import useTranslation from '../../hooks/useTranslation';
import { useAuth } from '../../contexts/AuthContext';

const EmailVerificationModal = ({ onClose }) => {
    const { t } = useTranslation(['settings', 'common']);
    const { user, updateUser, checkAuth } = useAuth();
    const [step, setStep] = useState('initial'); // 'initial', 'loading', 'success', 'error'
    const [error, setError] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    // Check if email is already verified
    const isEmailVerified = user?.email_verified_at || user?.email_verified;

    const handleSendVerification = async () => {
        if (!user?.email) {
            setError('No email address found. Please update your profile with an email address first.');
            setStep('error');
            return;
        }

        setIsLoading(true);
        setError(null);
        setStep('loading');

        try {
            const token = localStorage.getItem('token');
            const apiBaseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

            const response = await axios.post(
                `${apiBaseUrl}/email/send-verification`,
                { email: user.email },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                }
            );

            if (response.data.message) {
                setStep('success');
            } else {
                throw new Error('Unexpected response format');
            }
        } catch (err) {
            console.error('Email verification error:', err);

            let errorMessage = 'Failed to send verification email. Please try again.';

            if (err.response?.status === 422) {
                const validationErrors = err.response.data.errors;
                if (validationErrors?.email) {
                    errorMessage = Array.isArray(validationErrors.email)
                        ? validationErrors.email[0]
                        : validationErrors.email;
                } else {
                    errorMessage = err.response.data.message || errorMessage;
                }
            } else if (err.response?.status === 500) {
                errorMessage = err.response.data.message || 'Server error occurred. Please try again later.';
            } else if (err.response?.data?.message) {
                errorMessage = err.response.data.message;
            }

            setError(errorMessage);
            setStep('error');
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        if (!isLoading) {
            onClose();
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Escape' && !isLoading) {
            onClose();
        }
    };

    useEffect(() => {
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isLoading]);

    // Check for email verification status updates periodically
    useEffect(() => {
        let intervalId;

        // Only check if we're in success state and email is not yet verified
        if (step === 'success' && !isEmailVerified) {
            intervalId = setInterval(async () => {
                try {
                    // Refresh user data to check if email was verified
                    await checkAuth();
                } catch (error) {
                    console.error('Error checking email verification status:', error);
                }
            }, 5000); // Check every 5 seconds
        }

        return () => {
            if (intervalId) {
                clearInterval(intervalId);
            }
        };
    }, [step, isEmailVerified, checkAuth]);

    // Show success message when email becomes verified
    useEffect(() => {
        if (isEmailVerified && step === 'success') {
            // Email was verified! Update the step to show verified state
            setTimeout(() => {
                setStep('verified');
            }, 1000);
        }
    }, [isEmailVerified, step]);

    return (
        <motion.div
            className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm z-[60] flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => e.target === e.currentTarget && handleClose()}
        >
            <motion.div
                className="bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 rounded-2xl w-full max-w-md mx-4 shadow-2xl border border-gray-100 dark:border-gray-700 overflow-hidden"
                initial={{ scale: 0.95, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.95, opacity: 0, y: 20 }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-900 dark:to-purple-900 p-6 text-white">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold">{t('account.emailVerification.title')}</h3>
                            </div>
                            <button
                                onClick={handleClose}
                                disabled={isLoading}
                                className="p-2 hover:bg-white/20 dark:hover:bg-gray-800 rounded-full transition-all duration-200 hover:rotate-90 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6 text-gray-800 dark:text-gray-200">
                    {isEmailVerified ? (
                        // Already Verified State
                        <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="text-center space-y-4"
                        >
                            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-indigo-100 mb-2">Email Already Verified</h4>
                                <p className="text-gray-600 dark:text-indigo-100">
                                    Your email address <span className="font-medium text-indigo-600">{user?.email}</span> is already verified.
                                </p>
                            </div>
                            <button
                                onClick={handleClose}
                                className="w-full bg-indigo-600 dark:bg-indigo-700 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                            >
                                Close
                            </button>
                        </motion.div>
                    ) : (
                        // Verification Flow
                        <div className="space-y-6">
                            {step === 'initial' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Verify Your Email</h4>
                                        <p className="text-gray-600 mb-4">
                                            We'll send a verification link to <span className="font-medium text-indigo-600">{user?.email || 'your email address'}</span>
                                        </p>
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
                                            <div className="flex items-start space-x-2">
                                                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p>Click the link in the email to complete verification. The link will expire in 24 hours.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={handleSendVerification}
                                        disabled={!user?.email}
                                        className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Send Verification Email
                                    </button>
                                    {!user?.email && (
                                        <p className="text-sm text-red-600">
                                            Please add an email address to your profile first.
                                        </p>
                                    )}
                                </motion.div>
                            )}

                            {step === 'loading' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-indigo-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Sending Email...</h4>
                                        <p className="text-gray-600">
                                            Please wait while we send the verification email.
                                        </p>
                                    </div>
                                </motion.div>
                            )}

                            {step === 'success' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Email Sent!</h4>
                                        <p className="text-gray-600 mb-4">
                                            We've sent a verification link to <span className="font-medium text-indigo-600">{user?.email}</span>
                                        </p>
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-sm text-green-800">
                                            <div className="flex items-start space-x-2">
                                                <svg className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium">Check your email</p>
                                                    <p>Click the verification link to complete the process. Don't forget to check your spam folder!</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={async () => {
                                            // Refresh user data to get updated verification status
                                            await checkAuth();
                                            handleClose();
                                        }}
                                        className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                                    >
                                        Close
                                    </button>
                                </motion.div>
                            )}

                            {step === 'error' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Error Occurred</h4>
                                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800 mb-4">
                                            {error}
                                        </div>
                                    </div>
                                    <div className="flex space-x-3">
                                        <button
                                            onClick={() => setStep('initial')}
                                            className="flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                                        >
                                            Try Again
                                        </button>
                                        <button
                                            onClick={handleClose}
                                            className="flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 transition-colors font-medium"
                                        >
                                            Close
                                        </button>
                                    </div>
                                </motion.div>
                            )}

                            {step === 'verified' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Email Verified Successfully!</h4>
                                        <p className="text-gray-600 mb-4">
                                            Your email address <span className="font-medium text-indigo-600">{user?.email}</span> has been verified.
                                        </p>
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-sm text-green-800">
                                            <div className="flex items-start space-x-2">
                                                <svg className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium">Verification Complete</p>
                                                    <p>You can now access all email-related features and receive important notifications.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={handleClose}
                                        className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
                                    >
                                        Continue
                                    </button>
                                </motion.div>
                            )}
                        </div>
                    )}
                </div>
            </motion.div>
        </motion.div>
    );
};

export default EmailVerificationModal;
