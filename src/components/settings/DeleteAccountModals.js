import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiAlertTriangle } from 'react-icons/fi';

export const DeleteAccountConfirmModal = ({ isOpen, onClose, onConfirm, t }) => {
    const cancelRef = useRef(null);

    useEffect(() => {
        if (isOpen) {
            cancelRef.current?.focus();
        }
    }, [isOpen]);

    const handleKeyDown = (e) => {
        if (e.key === 'Escape') onClose();
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 z-[100]" role="dialog" aria-modal="true" aria-labelledby="delete-account-title">
                    <motion.div
                        className="fixed inset-0 bg-black/50"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={onClose}
                    />
                    <motion.div
                        className="relative w-full max-w-md mx-auto mt-24 p-6 rounded-xl bg-white dark:bg-gray-900 shadow-lg"
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.9, opacity: 0 }}
                        onKeyDown={handleKeyDown}
                    >
                        <div className="flex items-center mb-4">
                            <FiAlertTriangle className="w-6 h-6 text-red-600 mr-2" />
                            <h2 id="delete-account-title" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('deleteAccount.title')}
                            </h2>
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                            {t('deleteAccount.warning')}
                        </p>
                        <ul className="mt-3 list-disc list-inside text-sm text-gray-700 dark:text-gray-300">
                            <li>Profile and personal information</li>
                            <li>Missions and activity history</li>
                            <li>Wallet balance and transaction records</li>
                        </ul>
                        <div className="mt-6 flex justify-end gap-3">
                            <button
                                ref={cancelRef}
                                onClick={onClose}
                                className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 focus:outline-none"
                            >
                                {t('deleteAccount.cancelButton')}
                            </button>
                            <button
                                onClick={onConfirm}
                                className="px-4 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 focus:outline-none"
                            >
                                {t('deleteAccount.confirmButton')}
                            </button>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export const DeleteAccountPasswordModal = ({ isOpen, onClose, onDelete, password, setPassword, isLoading, error, t }) => {
    const inputRef = useRef(null);

    useEffect(() => {
        if (isOpen) {
            inputRef.current?.focus();
        }
    }, [isOpen]);

    const handleKeyDown = (e) => {
        if (e.key === 'Escape') onClose();
        if (e.key === 'Enter') onDelete();
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 z-[100]" role="dialog" aria-modal="true" aria-labelledby="delete-account-password-title">
                    <motion.div
                        className="fixed inset-0 bg-black/50"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={onClose}
                    />
                    <motion.div
                        className="relative w-full max-w-md mx-auto mt-24 p-6 rounded-xl bg-white dark:bg-gray-900 shadow-lg"
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.9, opacity: 0 }}
                        onKeyDown={handleKeyDown}
                    >
                        <div className="mb-4">
                            <h2 id="delete-account-password-title" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('deleteAccount.enterPassword')}
                            </h2>
                        </div>
                        <input
                            ref={inputRef}
                            type="password"
                            className="w-full px-3 py-2 border rounded-md mb-2 dark:bg-gray-800 dark:border-gray-700"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            aria-label={t('deleteAccount.enterPassword')}
                        />
                        {error && <p className="text-red-600 text-sm mb-2">{error}</p>}
                        <div className="mt-4 flex justify-end gap-3">
                            <button
                                onClick={onClose}
                                className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 focus:outline-none"
                            >
                                {t('deleteAccount.cancelButton')}
                            </button>
                            <button
                                onClick={onDelete}
                                disabled={isLoading}
                                className="px-4 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 flex items-center gap-2 focus:outline-none"
                            >
                                {isLoading && (
                                    <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                                )}
                                {t('deleteAccount.confirmButton')}
                            </button>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default { DeleteAccountConfirmModal, DeleteAccountPasswordModal };
