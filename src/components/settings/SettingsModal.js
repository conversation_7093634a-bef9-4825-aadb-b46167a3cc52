import React from 'react';
import { motion } from 'framer-motion';

const SettingsModal = ({ title, children, onClose }) => (
    <motion.div
        className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
    >
        <motion.div
            className="bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 rounded-2xl w-full max-w-xl mx-4 shadow-2xl border border-gray-100 dark:border-gray-700"
            initial={{ scale: 0.95, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
        >
            <div className="p-6">
                <div className="flex justify-between items-center mb-6 border-b border-gray-100 pb-4 dark:border-gray-700">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">{title}</h3>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-red-100 dark:hover:bg-red-900 bg-white dark:bg-gray-900 rounded-full transition-all duration-200 hover:rotate-90"
                    >
                        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div className="text-gray-800 dark:text-gray-200">
                    {children}
                </div>
            </div>
        </motion.div>
    </motion.div>
);

export default SettingsModal;
