import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOrderPayment } from '../contexts/OrderPaymentContext';
import CreditPackageCard from './CreditPackageCard';
import walletAPI from '../services/walletService';

/**
 * InsufficientBalanceModal component
 * 
 * This modal is shown when a user has insufficient balance to complete an order.
 * It allows them to top up their wallet directly from the order flow.
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {number} props.requiredAmount - The amount required for the order
 * @param {number} props.currentBalance - The user's current balance
 * @param {Function} props.onTopUpSuccess - Function to call when top-up is successful
 */
const InsufficientBalanceModal = ({ 
  isOpen, 
  onClose, 
  requiredAmount, 
  currentBalance, 
  onTopUpSuccess 
}) => {
  const [creditPackages, setCreditPackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [step, setStep] = useState('select'); // steps: select, confirm, processing, success, error
  
  const { handleTopUp, formatCurrency } = useOrderPayment();
  
  // Calculate shortfall
  const shortfall = requiredAmount - currentBalance;
  
  // Fetch credit packages on mount
  useEffect(() => {
    if (isOpen) {
      fetchCreditPackages();
    }
  }, [isOpen]);
  
  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setStep('select');
      setError(null);
      setSelectedPackage(null);
    }
  }, [isOpen]);
  
  // Fetch available credit packages
  const fetchCreditPackages = async () => {
    setIsLoading(true);
    
    try {
      const response = await walletAPI.getCreditChannels();
      
      if (response.data && response.data.length > 0) {
        // Get packages for the first channel
        const channelResponse = await walletAPI.getCreditPackages(response.data[0].id);
        
        if (channelResponse.data) {
          // Sort packages by price
          const sortedPackages = channelResponse.data.sort((a, b) => a.price - b.price);
          
          // Find the first package that covers the shortfall
          const recommendedPackage = sortedPackages.find(pkg => pkg.credits >= shortfall);
          
          setCreditPackages(sortedPackages);
          setSelectedPackage(recommendedPackage || sortedPackages[0]);
        }
      }
    } catch (err) {
      console.error('Error fetching credit packages:', err);
      setError('Failed to load credit packages. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle package selection
  const handleSelectPackage = (pkg) => {
    setSelectedPackage(pkg);
  };
  
  // Handle confirm button click
  const handleConfirm = () => {
    if (!selectedPackage) return;
    setStep('confirm');
  };
  
  // Handle payment confirmation
  const handleConfirmPayment = async () => {
    if (!selectedPackage) return;
    
    setIsLoading(true);
    setError(null);
    setStep('processing');
    
    try {
      // Use the order payment context to handle top-up
      await handleTopUp({
        amount: selectedPackage.price,
        onSuccess: (data) => {
          setStep('success');
          setTimeout(() => {
            if (onTopUpSuccess) onTopUpSuccess(data);
            onClose();
          }, 2000);
        },
        onError: (err) => {
          console.error('Error creating payment:', err);
          setError('Failed to process payment. Please try again later.');
          setStep('error');
          setIsLoading(false);
        }
      });
    } catch (err) {
      console.error('Error creating payment:', err);
      setError('Failed to process payment. Please try again later.');
      setStep('error');
      setIsLoading(false);
    }
  };
  
  // Handle close modal
  const handleCloseModal = () => {
    if (!isLoading) {
      onClose();
    }
  };
  
  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };
  
  // Render different content based on current step
  const renderContent = () => {
    switch (step) {
      case 'confirm':
        return renderConfirmStep();
      case 'processing':
        return renderProcessingStep();
      case 'success':
        return renderSuccessStep();
      case 'error':
        return renderErrorStep();
      default:
        return renderSelectStep();
    }
  };
  
  // Select step content
  const renderSelectStep = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Insufficient Balance</h2>
        <button 
          onClick={handleCloseModal}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div className="mb-6">
        <div className="bg-yellow-50 p-4 rounded-lg mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Insufficient Balance</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>You need {formatCurrency(requiredAmount)} to complete this order, but your current balance is {formatCurrency(currentBalance)}.</p>
                <p className="mt-1">Please top up your wallet with at least {formatCurrency(shortfall)} to proceed.</p>
              </div>
            </div>
          </div>
        </div>
        
        <h3 className="text-lg font-medium mb-3">Select a Credit Package</h3>
        
        {isLoading && creditPackages.length === 0 ? (
          <div className="flex justify-center py-8">
            <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-red-600">{typeof error === 'object' ? JSON.stringify(error) : String(error)}</p>
            <button
              onClick={fetchCreditPackages}
              className="mt-2 text-sm text-indigo-600 hover:text-indigo-800"
            >
              Try Again
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4 max-h-60 overflow-y-auto pr-1">
            {creditPackages.map(pkg => (
              <CreditPackageCard
                key={pkg.id}
                package={pkg}
                isSelected={selectedPackage && selectedPackage.id === pkg.id}
                onClick={() => handleSelectPackage(pkg)}
                isRecommended={pkg.credits >= shortfall && (!selectedPackage || pkg.id === selectedPackage.id)}
              />
            ))}
          </div>
        )}
      </div>
      
      <div className="flex justify-end">
        <button
          type="button"
          className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={handleCloseModal}
        >
          Cancel
        </button>
        <button
          type="button"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={handleConfirm}
          disabled={!selectedPackage || isLoading}
        >
          Continue
        </button>
      </div>
    </>
  );
  
  // Confirm step content
  const renderConfirmStep = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Confirm Top-Up</h2>
        <button 
          onClick={handleCloseModal}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div className="mb-6">
        <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
          <h3 className="text-lg font-medium mb-2">{selectedPackage.name}</h3>
          <div className="flex justify-between items-center">
            <div>
              <p className="text-gray-600">{selectedPackage.description}</p>
              <p className="text-sm text-gray-500 mt-1">Credits: {selectedPackage.credits}</p>
            </div>
            <div className="text-xl font-bold text-indigo-600">
              {formatCurrency(selectedPackage.price, 'MYR')}
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Current Balance:</span>
            <span className="font-medium">{formatCurrency(currentBalance)}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Credits to Add:</span>
            <span className="font-medium">{formatCurrency(selectedPackage.credits)}</span>
          </div>
          <div className="flex justify-between pt-2 border-t border-gray-200 mt-2">
            <span className="text-gray-800 font-medium">New Balance:</span>
            <span className="font-bold text-green-600">{formatCurrency(currentBalance + selectedPackage.credits)}</span>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button
          type="button"
          className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={() => setStep('select')}
        >
          Back
        </button>
        <button
          type="button"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={handleConfirmPayment}
        >
          Confirm Payment
        </button>
      </div>
    </>
  );
  
  // Processing step content
  const renderProcessingStep = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 border-t-4 border-blue-600 border-solid rounded-full animate-spin mx-auto mb-4"></div>
      <h2 className="text-xl font-bold mb-2">Processing Payment</h2>
      <p className="text-gray-600">Please wait while we process your payment...</p>
    </div>
  );
  
  // Success step content
  const renderSuccessStep = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <h2 className="text-xl font-bold mb-2">Top-Up Successful</h2>
      <p className="text-gray-600">Your wallet has been topped up successfully.</p>
    </div>
  );
  
  // Error step content
  const renderErrorStep = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Payment Error</h2>
        <button 
          onClick={handleCloseModal}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div className="mb-6">
        <div className="bg-red-50 p-4 rounded-lg mb-4">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{typeof error === 'object' ? JSON.stringify(error) : String(error)}</p>
            </div>
          </div>
        </div>
        
        <p className="text-gray-700">
          We encountered an error while processing your payment. Please try again or contact support if the issue persists.
        </p>
      </div>
      
      <div className="flex justify-end">
        <button
          type="button"
          className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={handleCloseModal}
        >
          Cancel
        </button>
        <button
          type="button"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={() => setStep('select')}
        >
          Try Again
        </button>
      </div>
    </>
  );
  
  // If modal is not open, don't render anything
  if (!isOpen) return null;
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={handleCloseModal}
        >
          <motion.div
            className="bg-white rounded-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              {renderContent()}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default InsufficientBalanceModal;
