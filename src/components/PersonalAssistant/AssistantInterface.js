import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../features/wallet/contexts/WalletContext';
import { useProfile } from '../../contexts/ProfileContext';
import AssistantHeader from './components/AssistantHeader';
import MessageList from './components/MessageList';
import SmartInput from './components/SmartInput';
import QuickActions from './components/QuickActions';
import ContextualSuggestions from './components/ContextualSuggestions';
import VoiceInterface from './components/VoiceInterface';

const AssistantInterface = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const { balance, transactions } = useWallet();
  const { profile, services, availability } = useProfile();
  
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [currentContext, setCurrentContext] = useState('general');
  const [suggestions, setSuggestions] = useState([]);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  
  const messagesEndRef = useRef(null);

  // Initialize with personalized welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage = generateWelcomeMessage();
      setMessages([welcomeMessage]);
      generateContextualSuggestions();
    }
  }, [isOpen, user, profile]);

  const generateWelcomeMessage = () => {
    const timeOfDay = getTimeOfDay();
    const userName = user?.name || profile?.name || 'there';
    
    return {
      id: Date.now(),
      type: 'assistant',
      content: `Good ${timeOfDay}, ${userName}! 👋 I'm your Mission X assistant. I can help you with:
      
• 💰 Wallet & transactions
• 🎯 Mission management  
• 📊 Profile optimization
• 📅 Scheduling & availability
• 🔔 Smart notifications

What would you like to do today?`,
      timestamp: new Date(),
      suggestions: [
        'Check my balance',
        'Show active missions',
        'Update availability',
        'Find new missions'
      ]
    };
  };

  const generateContextualSuggestions = () => {
    const contextSuggestions = [];
    
    // Wallet-based suggestions
    if (balance < 100) {
      contextSuggestions.push({
        type: 'wallet',
        text: 'Your balance is low. Would you like to top up?',
        action: 'topup',
        priority: 'high'
      });
    }

    // Mission-based suggestions
    if (profile?.services?.length > 0) {
      contextSuggestions.push({
        type: 'mission',
        text: 'Find missions matching your services',
        action: 'find_missions',
        priority: 'medium'
      });
    }

    // Availability suggestions
    if (!availability?.is_available) {
      contextSuggestions.push({
        type: 'profile',
        text: 'Set your availability to receive more orders',
        action: 'set_availability',
        priority: 'medium'
      });
    }

    setSuggestions(contextSuggestions);
  };

  const handleSendMessage = async (message, context = {}) => {
    // Add user message
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // Process message with enhanced AI
      const response = await processAssistantMessage(message, {
        user,
        profile,
        balance,
        transactions,
        services,
        availability,
        ...context
      });

      // Add assistant response
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: response.content,
        timestamp: new Date(),
        actions: response.actions,
        suggestions: response.suggestions,
        data: response.data
      };

      setMessages(prev => [...prev, assistantMessage]);
      
      // Update context and suggestions
      if (response.context) {
        setCurrentContext(response.context);
      }
      
      if (response.newSuggestions) {
        setSuggestions(response.newSuggestions);
      }

    } catch (error) {
      console.error('Assistant error:', error);
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: "I'm sorry, I encountered an error. Please try again or contact support if the issue persists.",
        timestamp: new Date(),
        error: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const processAssistantMessage = async (message, context) => {
    // This would integrate with your enhanced AI system
    // For now, returning a structured response format
    return {
      content: "I understand you want help with that. Let me assist you.",
      actions: [],
      suggestions: ['What else can I help with?'],
      context: 'general',
      data: null
    };
  };

  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: 20 }}
      transition={{ duration: 0.2 }}
      className="fixed bottom-24 left-6 w-[420px] h-[700px] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 flex flex-col overflow-hidden z-50"
    >
      {/* Enhanced Header */}
      <AssistantHeader 
        onClose={onClose}
        voiceEnabled={voiceEnabled}
        onToggleVoice={() => setVoiceEnabled(!voiceEnabled)}
        currentContext={currentContext}
      />

      {/* Messages Area */}
      <div className="flex-1 overflow-hidden">
        <MessageList 
          messages={messages}
          isTyping={isTyping}
          onActionClick={handleSendMessage}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* Contextual Suggestions */}
      {suggestions.length > 0 && (
        <ContextualSuggestions 
          suggestions={suggestions}
          onSuggestionClick={handleSendMessage}
        />
      )}

      {/* Quick Actions Bar */}
      <QuickActions 
        onActionClick={handleSendMessage}
        context={currentContext}
      />

      {/* Smart Input */}
      <SmartInput 
        onSendMessage={handleSendMessage}
        context={currentContext}
        suggestions={suggestions}
      />

      {/* Voice Interface */}
      {voiceEnabled && (
        <VoiceInterface 
          onVoiceMessage={handleSendMessage}
          isListening={voiceEnabled}
        />
      )}
    </motion.div>
  );
};

export default AssistantInterface;
