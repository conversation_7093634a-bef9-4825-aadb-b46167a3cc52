import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const SmartInput = ({ onSendMessage, context, suggestions = [] }) => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  const inputRef = useRef(null);

  // Smart suggestions based on context and input
  const smartSuggestions = {
    general: [
      'Check my balance',
      'Show active missions',
      'Update my availability',
      'Find new missions',
      'View recent orders',
      'Help me optimize my profile'
    ],
    wallet: [
      'Show my balance',
      'Recent transactions',
      'Top up credits',
      'Withdrawal options',
      'Spending analytics',
      'Set budget alerts'
    ],
    missions: [
      'Find missions for me',
      'Show my active missions',
      'Create a new mission',
      'Mission deadlines',
      'Application status',
      'Mission recommendations'
    ],
    profile: [
      'Update my services',
      'Set availability',
      'Profile optimization tips',
      'Upload new media',
      'Update bio',
      'Pricing suggestions'
    ],
    orders: [
      'Show pending orders',
      'Order history',
      'Accept all orders',
      'Schedule management',
      'Customer feedback',
      'Revenue analytics'
    ]
  };

  useEffect(() => {
    if (inputValue.length > 0) {
      const contextSuggestions = smartSuggestions[context] || smartSuggestions.general;
      const filtered = contextSuggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(inputValue.toLowerCase())
      );
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      setShowSuggestions(false);
      setFilteredSuggestions([]);
    }
    setSelectedSuggestionIndex(-1);
  }, [inputValue, context]);

  const handleSubmit = (e) => {
    e.preventDefault();
    const trimmed = inputValue.trim();
    if (trimmed) {
      onSendMessage(trimmed);
      setInputValue('');
      setShowSuggestions(false);
    }
  };

  const handleKeyDown = (e) => {
    if (showSuggestions && filteredSuggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        );
      } else if (e.key === 'Tab' && selectedSuggestionIndex >= 0) {
        e.preventDefault();
        setInputValue(filteredSuggestions[selectedSuggestionIndex]);
        setShowSuggestions(false);
      } else if (e.key === 'Enter' && selectedSuggestionIndex >= 0) {
        e.preventDefault();
        onSendMessage(filteredSuggestions[selectedSuggestionIndex]);
        setInputValue('');
        setShowSuggestions(false);
      }
    }
  };

  const handleSuggestionClick = (suggestion) => {
    onSendMessage(suggestion);
    setInputValue('');
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const hasContent = inputValue.trim().length > 0;

  return (
    <div className="relative p-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-t border-gray-200/50 dark:border-gray-700/50">
      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && filteredSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute bottom-full left-4 right-4 mb-2 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-xl overflow-hidden z-10"
          >
            <div className="max-h-48 overflow-y-auto">
              {filteredSuggestions.map((suggestion, index) => (
                <motion.button
                  key={suggestion}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className={`w-full text-left px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${
                    index === selectedSuggestionIndex 
                      ? 'bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400' 
                      : 'text-gray-700 dark:text-gray-300'
                  }`}
                  whileHover={{ x: 4 }}
                  transition={{ duration: 0.1 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-indigo-400 rounded-full opacity-60"></div>
                    <span className="text-sm font-medium">{suggestion}</span>
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Input Form */}
      <form
        onSubmit={handleSubmit}
        className={`flex items-center gap-3 p-3 rounded-2xl bg-gray-50/80 dark:bg-gray-800/80 backdrop-blur-sm border transition-all duration-300 ${
          isFocused
            ? 'border-indigo-300 dark:border-indigo-600 shadow-lg shadow-indigo-500/10'
            : 'border-gray-200/50 dark:border-gray-700/50'
        }`}
      >
        {/* Context Indicator */}
        <div className={`w-3 h-3 rounded-full ${getContextColor(context)} flex-shrink-0`} />

        {/* Input Field */}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => {
            setIsFocused(false);
            // Delay hiding suggestions to allow clicks
            setTimeout(() => setShowSuggestions(false), 150);
          }}
          onKeyDown={handleKeyDown}
          placeholder={getContextPlaceholder(context)}
          autoComplete="off"
          className="flex-1 bg-transparent border-0 text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 outline-none text-sm font-medium"
        />

        {/* Quick Action Buttons */}
        <div className="flex items-center space-x-1">
          {/* Voice Input Button */}
          <motion.button
            type="button"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-lg text-gray-400 hover:text-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/30 transition-all duration-200"
            title="Voice input"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
          </motion.button>

          {/* Send Button */}
          <motion.button
            type="submit"
            disabled={!hasContent}
            whileHover={hasContent ? { scale: 1.05 } : {}}
            whileTap={hasContent ? { scale: 0.95 } : {}}
            className={`p-2.5 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:ring-offset-1 ${
              hasContent
                ? 'bg-gradient-to-br from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/25 hover:shadow-xl hover:shadow-indigo-500/30'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2.5"
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              />
            </svg>
          </motion.button>
        </div>
      </form>
    </div>
  );
};

const getContextColor = (context) => {
  switch (context) {
    case 'wallet': return 'bg-green-400';
    case 'missions': return 'bg-blue-400';
    case 'profile': return 'bg-purple-400';
    case 'orders': return 'bg-orange-400';
    case 'scheduling': return 'bg-teal-400';
    default: return 'bg-indigo-400';
  }
};

const getContextPlaceholder = (context) => {
  switch (context) {
    case 'wallet': return 'Ask about your wallet, balance, or transactions...';
    case 'missions': return 'Ask about missions, applications, or opportunities...';
    case 'profile': return 'Ask about your profile, services, or optimization...';
    case 'orders': return 'Ask about orders, scheduling, or customer management...';
    case 'scheduling': return 'Ask about availability, calendar, or appointments...';
    default: return 'Ask Iona anything about Mission X...';
  }
};

export default SmartInput;
