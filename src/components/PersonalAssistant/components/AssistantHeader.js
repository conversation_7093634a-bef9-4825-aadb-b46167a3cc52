import React from 'react';
import { motion } from 'framer-motion';

const AssistantHeader = ({ onClose, voiceEnabled, onToggleVoice, currentContext }) => {
  const getContextIcon = (context) => {
    switch (context) {
      case 'wallet':
        return '💰';
      case 'missions':
        return '🎯';
      case 'profile':
        return '👤';
      case 'orders':
        return '📦';
      case 'scheduling':
        return '📅';
      default:
        return '🤖';
    }
  };

  const getContextColor = (context) => {
    switch (context) {
      case 'wallet':
        return 'from-green-500 to-emerald-600';
      case 'missions':
        return 'from-blue-500 to-indigo-600';
      case 'profile':
        return 'from-purple-500 to-pink-600';
      case 'orders':
        return 'from-orange-500 to-red-600';
      case 'scheduling':
        return 'from-teal-500 to-cyan-600';
      default:
        return 'from-indigo-500 to-purple-600';
    }
  };

  return (
    <div className={`relative p-4 bg-gradient-to-r ${getContextColor(currentContext)} text-white overflow-hidden`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full transform translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full transform -translate-x-12 translate-y-12"></div>
      </div>

      <div className="relative z-10 flex items-center justify-between">
        {/* Assistant Info */}
        <div className="flex items-center space-x-3">
          <motion.div 
            className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-2xl"
            animate={{ 
              scale: voiceEnabled ? [1, 1.1, 1] : 1,
              rotate: voiceEnabled ? [0, 5, -5, 0] : 0
            }}
            transition={{ 
              duration: 2, 
              repeat: voiceEnabled ? Infinity : 0,
              ease: "easeInOut"
            }}
          >
            <img 
              src="/IonaMascot.png" 
              alt="Iona" 
              className="w-10 h-10 rounded-full"
            />
          </motion.div>
          
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-bold text-lg">Iona</h3>
              <span className="text-xl">{getContextIcon(currentContext)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-white/90 capitalize">
                {currentContext === 'general' ? 'Ready to help' : `${currentContext} assistant`}
              </span>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-2">
          {/* Voice Toggle */}
          <motion.button
            onClick={onToggleVoice}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`p-2 rounded-full transition-all duration-200 ${
              voiceEnabled 
                ? 'bg-white/30 text-white' 
                : 'bg-white/10 text-white/70 hover:bg-white/20'
            }`}
            title={voiceEnabled ? 'Disable voice' : 'Enable voice'}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {voiceEnabled ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
              )}
            </svg>
          </motion.button>

          {/* Minimize/Close */}
          <motion.button
            onClick={onClose}
            whileHover={{ scale: 1.05, rotate: 90 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-full bg-white/10 hover:bg-white/20 text-white/70 hover:text-white transition-all duration-200"
            title="Close assistant"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </motion.button>
        </div>
      </div>

      {/* Context Indicator */}
      <motion.div 
        className="absolute bottom-0 left-0 h-1 bg-white/30"
        initial={{ width: 0 }}
        animate={{ width: '100%' }}
        transition={{ duration: 0.5, delay: 0.2 }}
      />
    </div>
  );
};

export default AssistantHeader;
