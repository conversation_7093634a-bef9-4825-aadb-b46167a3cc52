import React from 'react';

const PopularTalentFrame = ({ children, className = "" }) => {
  return (
    <div className={`relative w-full aspect-[3/4] ${className}`}>
      {/* Background with gaming theme gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-800 via-slate-900 to-indigo-900 rounded-xl overflow-hidden">
        {/* Animated background particles */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-[20%] left-[15%] w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
          <div className="absolute top-[35%] right-[20%] w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-[30%] left-[25%] w-1 h-1 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-[60%] right-[15%] w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Gaming character silhouettes */}
        <div className="absolute inset-0 opacity-30">
          {/* Left character silhouette */}
          <div className="absolute left-2 top-1/4 w-16 h-20 bg-gradient-to-b from-purple-600/40 to-transparent rounded-lg transform -rotate-12"></div>
          {/* Right character silhouette */}
          <div className="absolute right-2 top-1/3 w-14 h-18 bg-gradient-to-b from-blue-600/40 to-transparent rounded-lg transform rotate-12"></div>
          {/* Bottom character silhouettes */}
          <div className="absolute left-4 bottom-20 w-12 h-16 bg-gradient-to-b from-red-600/30 to-transparent rounded-lg"></div>
          <div className="absolute right-4 bottom-20 w-10 h-14 bg-gradient-to-b from-green-600/30 to-transparent rounded-lg"></div>
        </div>
      </div>

      {/* Main circular frame */}
      <div className="absolute inset-0 flex items-center justify-center" style={{ paddingTop: '5%', paddingBottom: '15%' }}>
        {/* Outer golden ring */}
        <div className="relative w-[70%] aspect-square">
          {/* Golden gradient ring */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-amber-300 via-yellow-500 to-amber-600 p-2 shadow-2xl">
            {/* Inner shadow ring */}
            <div className="w-full h-full rounded-full bg-gradient-to-br from-amber-400 via-yellow-600 to-amber-700 p-1">
              {/* Profile picture area */}
              <div className="w-full h-full rounded-full bg-gradient-to-br from-slate-200 to-slate-300 overflow-hidden relative">
                {/* Content goes here */}
                {children}
                
                {/* Inner glow effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/20 via-transparent to-black/20 pointer-events-none"></div>
              </div>
            </div>
          </div>

          {/* Decorative laurel wreaths */}
          {/* Left laurel */}
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-3">
            <div className="flex flex-col space-y-0.5">
              {[...Array(8)].map((_, i) => (
                <div key={i}
                     className="w-2 h-4 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 rounded-full transform rotate-45 opacity-90 shadow-sm"
                     style={{
                       transform: `rotate(${45 + i * 5}deg) scale(${1 - i * 0.05})`,
                       marginLeft: `${i * 1}px`
                     }}>
                </div>
              ))}
            </div>
          </div>

          {/* Right laurel */}
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-3">
            <div className="flex flex-col space-y-0.5">
              {[...Array(8)].map((_, i) => (
                <div key={i}
                     className="w-2 h-4 bg-gradient-to-br from-yellow-500 via-amber-500 to-amber-600 rounded-full transform -rotate-45 opacity-90 shadow-sm"
                     style={{
                       transform: `rotate(${-45 - i * 5}deg) scale(${1 - i * 0.05})`,
                       marginRight: `${i * 1}px`
                     }}>
                </div>
              ))}
            </div>
          </div>

          {/* Top decorative elements */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
            <div className="w-4 h-4 bg-gradient-to-br from-amber-300 to-yellow-500 rounded-full shadow-lg"></div>
          </div>

          {/* Bottom decorative gem */}
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-2">
            <div className="w-6 h-4 bg-gradient-to-br from-red-500 via-orange-500 to-yellow-500 rounded-full shadow-lg"></div>
          </div>
        </div>
      </div>

      {/* POPULAR badge */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
        <div className="bg-gradient-to-r from-amber-600 via-yellow-500 to-amber-600 px-6 py-2 rounded-full border-2 border-amber-300 shadow-xl">
          <div className="bg-gradient-to-r from-slate-800 to-slate-900 px-4 py-1 rounded-full">
            <span className="text-white font-bold text-sm tracking-wider">POPULAR</span>
          </div>
        </div>
      </div>

      {/* Shine effects */}
      <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/10 via-transparent to-transparent"></div>
        <div className="absolute top-2 left-2 w-8 h-8 bg-white/20 rounded-full blur-sm"></div>
        <div className="absolute bottom-4 right-4 w-6 h-6 bg-amber-300/30 rounded-full blur-sm"></div>
      </div>
    </div>
  );
};

export default PopularTalentFrame;
