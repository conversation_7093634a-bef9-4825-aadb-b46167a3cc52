/******************************************************************
 * TOP TalentCard – PREMIUM EDITION (Clean & Attractive Layout)
 * Designed specifically for top talents with enhanced aesthetics
 *****************************************************************/
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { FaBolt, FaFire, FaCrown, FaPlay, FaPause, FaVolumeHigh, FaTriangleExclamation, FaStar } from 'react-icons/fa6';

// Premium TOP Talent Card with clean, attractive layout
// Removed services and follow button for cleaner design
// Enhanced with TOP Talent badge and premium styling
const TopTalentCard = ({ talent = {} }) => {
  const {
    id,
    name: displayName,
    level: levelNumber,
    image: profileImage,
    rating = 0,
    isHot,
    verified = false,
    vip = false,
    voiceNoteUrl
  } = talent;

  const isFeatured = Boolean(isHot);
  const voice_note = voiceNoteUrl;

  const navigate = useNavigate();
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [isPlayingVoice, setIsPlayingVoice] = useState(false);
  const [audioElement, setAudioElement] = useState(null);
  const [audioLoading, setAudioLoading] = useState(false);
  const [audioProgress, setAudioProgress] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [audioError, setAudioError] = useState(false);

  /* Enhanced 3-D hover tilt */
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useTransform(y, [-100, 100], [8, -8]);
  const rotateY = useTransform(x, [-100, 100], [-8, 8]);

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
    };
  }, [audioElement]);

  const handleCardClick = () => navigate(`/talents/${id}`);

  const handleVoiceNoteClick = async (e) => {
    e.stopPropagation();

    if (!voice_note) return;

    if (isPlayingVoice && audioElement) {
      // Stop current audio
      audioElement.pause();
      audioElement.currentTime = 0;
      setIsPlayingVoice(false);
      setAudioElement(null);
      setAudioProgress(0);
    } else {
      // Play new audio
      setAudioLoading(true);
      setAudioError(false);

      try {
        const audio = new Audio(getCdnUrl(voice_note));

        // Enhanced audio event handlers
        audio.onloadedmetadata = () => {
          setAudioDuration(audio.duration);
          setAudioLoading(false);
        };

        audio.ontimeupdate = () => {
          if (audio.duration) {
            setAudioProgress((audio.currentTime / audio.duration) * 100);
          }
        };

        audio.onended = () => {
          setIsPlayingVoice(false);
          setAudioElement(null);
          setAudioProgress(0);
        };

        audio.onerror = () => {
          setIsPlayingVoice(false);
          setAudioElement(null);
          setAudioLoading(false);
          setAudioError(true);
          setAudioProgress(0);
          console.error('Error playing voice note');
        };

        audio.oncanplaythrough = () => {
          setAudioLoading(false);
        };

        await audio.play();
        setAudioElement(audio);
        setIsPlayingVoice(true);
      } catch (error) {
        console.error('Error playing voice note:', error);
        setIsPlayingVoice(false);
        setAudioElement(null);
        setAudioLoading(false);
        setAudioError(true);
        setAudioProgress(0);
      }
    }
  };

  const defaultImageUrl = '/AuthLogo.png';

  return (
    <motion.div
      className="group relative w-full aspect-square rounded-3xl border-2 border-gradient-to-br from-amber-400/40 via-yellow-300/30 to-orange-400/40 bg-gradient-to-br from-slate-800 via-slate-900 to-black shadow-[0_8px_32px_0_rgba(251,191,36,0.15)] hover:shadow-[0_12px_48px_0_rgba(251,191,36,0.25)] backdrop-blur-2xl transition-all duration-500 cursor-pointer overflow-hidden"
      style={{ 
        perspective: 1200,
        boxShadow: '0 0 0 1px rgba(251,191,36,0.2), 0 8px 32px rgba(251,191,36,0.15), 0 20px 60px rgba(0,0,0,0.3)'
      }}
      onClick={handleCardClick}
      whileHover={{ scale: 1.03, y: -5 }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        x.set((e.clientX - centerX) * 0.5);
        y.set((e.clientY - centerY) * 0.5);
        setMousePos({
          x: ((e.clientX - rect.left) / rect.width) * 100,
          y: ((e.clientY - rect.top) / rect.height) * 100
        });
      }}
      onMouseLeave={() => {
        x.set(0);
        y.set(0);
        setMousePos({ x: 50, y: 50 });
      }}
    >
      {/* Premium golden glow effect */}
      <div 
        className="absolute inset-0 rounded-3xl pointer-events-none opacity-60 group-hover:opacity-80 transition-opacity duration-500"
        style={{
          background: 'radial-gradient(circle at 50% 40%, rgba(251,191,36,0.3) 0%, rgba(245,158,11,0.2) 30%, rgba(217,119,6,0.1) 60%, transparent 100%)',
          filter: 'blur(20px)'
        }}
      />

      {/* Animated border shimmer */}
      <div className="absolute inset-0 rounded-3xl overflow-hidden">
        <motion.div
          className="absolute inset-0 opacity-0 group-hover:opacity-100"
          style={{
            background: 'conic-gradient(from 0deg, transparent, rgba(251,191,36,0.4), transparent, rgba(245,158,11,0.4), transparent)'
          }}
          animate={{ rotate: 360 }}
          transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
        />
        <div className="absolute inset-[2px] bg-gradient-to-br from-slate-800 via-slate-900 to-black rounded-3xl" />
      </div>

      {/* Tilt container */}
      <motion.div
        style={{ rotateX, rotateY, transformStyle: 'preserve-3d' }}
        className="w-full h-full relative z-10"
      >
        {/* Profile image with premium styling */}
        <div className="relative w-full h-full rounded-3xl overflow-hidden">
          <img
            src={profileImage || defaultImageUrl}
            alt={displayName}
            className="w-full h-full object-cover object-center scale-105 group-hover:scale-110 transition-transform duration-700"
            onError={(e) => {
              e.target.src = defaultImageUrl;
            }}
          />
          
          {/* Premium overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent opacity-80 group-hover:opacity-85 transition-opacity duration-300" />
          
          {/* Golden accent at bottom */}
          <div className="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-amber-900/40 via-transparent to-transparent" />
        </div>

        {/* TOP TALENT Badge - Premium Design */}
        <motion.div
          className="absolute top-4 left-1/2 -translate-x-1/2 z-30"
          initial={{ opacity: 0, y: -20, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <div className="relative">
            {/* Glow effect */}
            <div 
              className="absolute inset-0 rounded-full blur-md"
              style={{
                background: 'linear-gradient(135deg, #fbbf24, #f59e0b, #d97706)',
                opacity: 0.8
              }}
            />
            
            {/* Main badge */}
            <div className="relative flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-amber-400 via-yellow-400 to-orange-400 text-black font-black text-sm tracking-wider shadow-2xl border-2 border-yellow-300/50">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
              >
                <FaCrown className="w-4 h-4 text-amber-900" />
              </motion.div>
              <span className="text-amber-900 drop-shadow-sm">TOP TALENT</span>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                ✨
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Level Badge - Premium Style */}
        <motion.div
          className="absolute top-4 right-4 z-30"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center gap-2 px-3 py-2 rounded-xl bg-gradient-to-r from-indigo-500/90 to-purple-600/90 text-white font-bold text-sm backdrop-blur-md border border-white/20">
            <FaBolt className="w-3 h-3 text-yellow-300" />
            <span>LVL {levelNumber}</span>
          </div>
        </motion.div>

        {/* Featured Badge */}
        {isFeatured && (
          <motion.div
            className="absolute top-4 left-4 z-30"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.4, type: "spring" }}
          >
            <div className="flex items-center gap-1 px-3 py-2 rounded-xl bg-gradient-to-r from-red-500/90 to-orange-500/90 text-white font-bold text-sm backdrop-blur-md border border-white/20">
              <FaFire className="w-3 h-3" />
              <span>HOT</span>
            </div>
          </motion.div>
        )}

        {/* Content overlay - Clean and premium */}
        <div className="absolute inset-0 p-6 text-white flex flex-col justify-end">
          {/* Name and badges section */}
          <motion.div
            className="flex items-center justify-between mb-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <div className="flex items-center gap-3">
              <h3 className="text-2xl font-black tracking-tight">
                <span className="bg-gradient-to-r from-white via-amber-100 to-yellow-200 bg-clip-text text-transparent drop-shadow-lg">
                  {displayName}
                </span>
              </h3>

              {/* Voice Note Button - Premium Design */}
              {voice_note && (
                <div className="relative">
                  <motion.button
                    whileHover={{ scale: 1.15 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleVoiceNoteClick}
                    disabled={audioLoading}
                    className={`relative w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 shadow-xl overflow-hidden ${
                      audioError
                        ? 'bg-gradient-to-r from-red-500 to-red-600 text-white'
                        : isPlayingVoice
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                        : audioLoading
                        ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white'
                        : 'bg-gradient-to-r from-amber-400 to-yellow-500 text-amber-900 hover:from-yellow-400 hover:to-amber-500'
                    }`}
                    style={{
                      boxShadow: isPlayingVoice
                        ? '0 0 20px rgba(168,85,247,0.6)'
                        : '0 0 20px rgba(251,191,36,0.4)'
                    }}
                  >
                    {/* Progress Ring */}
                    {isPlayingVoice && (
                      <svg className="absolute inset-0 w-full h-full -rotate-90" viewBox="0 0 40 40">
                        <circle
                          cx="20"
                          cy="20"
                          r="18"
                          fill="none"
                          stroke="rgba(255,255,255,0.3)"
                          strokeWidth="2"
                        />
                        <circle
                          cx="20"
                          cy="20"
                          r="18"
                          fill="none"
                          stroke="rgba(255,255,255,0.9)"
                          strokeWidth="2"
                          strokeDasharray={`${2 * Math.PI * 18}`}
                          strokeDashoffset={`${2 * Math.PI * 18 * (1 - audioProgress / 100)}`}
                          className="transition-all duration-100 ease-linear"
                        />
                      </svg>
                    )}

                    {/* Icon */}
                    <div className="relative z-10">
                      {audioLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                        />
                      ) : audioError ? (
                        <FaTriangleExclamation className="w-4 h-4" />
                      ) : isPlayingVoice ? (
                        <FaPause className="w-4 h-4" />
                      ) : (
                        <FaPlay className="w-4 h-4 ml-0.5" />
                      )}
                    </div>
                  </motion.button>

                  {/* Audio waveform when playing */}
                  {isPlayingVoice && (
                    <motion.div
                      className="absolute -right-12 top-1/2 -translate-y-1/2 flex items-center gap-1"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      {[...Array(4)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="w-1 bg-amber-400 rounded-full"
                          animate={{ height: [6, 16, 6, 12, 6] }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            delay: i * 0.1,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </motion.div>
                  )}
                </div>
              )}
            </div>

            {/* Verification badges */}
            <div className="flex items-center gap-2">
              {verified && (
                <motion.div 
                  className="w-8 h-8 rounded-full flex items-center justify-center shadow-xl"
                  style={{
                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                    boxShadow: '0 0 15px rgba(59,130,246,0.5)'
                  }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>
              )}
              
              {vip && (
                <motion.div 
                  className="w-8 h-8 rounded-full flex items-center justify-center shadow-xl"
                  style={{
                    background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #ec4899 100%)',
                    boxShadow: '0 0 15px rgba(251,191,36,0.5)'
                  }}
                  whileHover={{ scale: 1.1, rotate: 15 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FaStar className="w-4 h-4 text-white" />
                </motion.div>
              )}
            </div>
          </motion.div>

          {/* Rating - Premium Design */}
          <motion.div
            className="flex justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div className="flex items-center gap-2 px-6 py-3 rounded-2xl backdrop-blur-md border border-amber-400/30" 
                 style={{
                   background: 'linear-gradient(135deg, rgba(251,191,36,0.15) 0%, rgba(245,158,11,0.25) 100%)',
                 }}>
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <FaStar className="w-5 h-5 text-amber-400" />
              </motion.div>
              <span className="text-2xl font-black text-white drop-shadow-lg">
                {typeof rating === 'number' ? rating.toFixed(1) : '5.0'}
              </span>
              <span className="text-amber-200 font-medium">Rating</span>
            </div>
          </motion.div>
        </div>

        {/* Premium hover effect overlay */}
        <motion.div
          className="absolute inset-0 pointer-events-none rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-500"
          style={{
            background: `radial-gradient(circle at ${mousePos.x}% ${mousePos.y}%, rgba(251,191,36,0.3) 0%, transparent 50%)`
          }}
        />

        {/* Golden particles effect on hover */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-3xl">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-amber-400 rounded-full opacity-0 group-hover:opacity-100"
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + (i % 2) * 30}%`,
              }}
              animate={{
                y: [-20, -60, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 2,
                delay: i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PopularTalentFrame;