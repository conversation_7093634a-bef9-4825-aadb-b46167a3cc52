import React, { useRef, useEffect } from 'react';

// Video component that auto plays when visible and pauses when not
const AutoPlayVideo = ({ src, type = 'video/mp4', className = '', poster = '', style = {}, ...props }) => {
  const videoRef = useRef(null);

  useEffect(() => {
    const el = videoRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          el.play().catch(() => {});
        } else {
          el.pause();
        }
      },
      { threshold: 0.5 }
    );
    observer.observe(el);
    return () => observer.disconnect();
  }, []);

  return (
    <video ref={videoRef} className={className} poster={poster} muted loop playsInline style={style} {...props}>
      <source src={src} type={type} />
    </video>
  );
};

export default AutoPlayVideo;
