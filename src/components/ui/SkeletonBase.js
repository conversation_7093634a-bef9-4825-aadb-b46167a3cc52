import React from 'react';
import clsx from 'clsx';

const SkeletonBase = ({
  width,
  height,
  variant = 'rectangular',
  className = '',
  ...props
}) => {
  const shapeClasses = {
    rectangular: 'rounded-md',
    circular: 'rounded-full',
    text: 'rounded',
    avatar: 'rounded-full',
    button: 'rounded-lg',
    card: 'rounded-xl'
  };

  return (
    <div
      className={clsx(
        'bg-gray-200 dark:bg-gray-700',
        shapeClasses[variant] || shapeClasses.rectangular,
        className
      )}
      style={{ width, height }}
      {...props}
    />
  );
};

export default SkeletonBase;
