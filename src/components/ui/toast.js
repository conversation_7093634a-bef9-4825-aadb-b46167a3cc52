import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircledIcon, Cross2Icon, ExclamationTriangleIcon } from '@radix-ui/react-icons';

// Toast container to manage multiple toasts
export const ToastContainer = ({ children }) => {
  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
      <AnimatePresence>
        {children}
      </AnimatePresence>
    </div>
  );
};

// Individual toast component
export const Toast = ({ 
  message, 
  type = 'success', // 'success', 'error', 'warning'
  duration = 5000,
  onClose
}) => {
  const [visible, setVisible] = useState(true);

  // Auto-dismiss
  useEffect(() => {
    if (!duration) return;
    
    const timer = setTimeout(() => {
      setVisible(false);
      setTimeout(() => {
        if (onClose) onClose();
      }, 300); // Give time for exit animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  // Icon based on type
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircledIcon className="w-5 h-5 text-green-500" />;
      case 'error':
        return <Cross2Icon className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-amber-500" />;
      default:
        return null;
    }
  };

  // Background color based on type
  const getBgColor = () => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200';
      case 'error': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-amber-50 border-amber-200';
      default: return 'bg-white border-gray-200';
    }
  };

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => {
      if (onClose) onClose();
    }, 300);
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className={`${getBgColor()} border rounded-lg shadow-lg p-4 pr-10 max-w-sm relative`}
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
        >
          <button 
            onClick={handleClose}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
          >
            <Cross2Icon className="w-3 h-3" />
          </button>
          
          <div className="flex items-start">
            <div className="mr-3 mt-0.5">
              {getIcon()}
            </div>
            <div>
            <p className="text-sm text-gray-900">{typeof message === 'object' ? JSON.stringify(message) : String(message || '')}</p>
          </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Toast manager for creating and managing toasts
export const useToast = () => {
  const [toasts, setToasts] = useState([]);

  const addToast = ({ message, type = 'success', duration = 5000 }) => {
    const id = Date.now();
    setToasts(prev => [...prev, { id, message, type, duration }]);
    return id;
  };

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const success = (message, duration) => addToast({ message, type: 'success', duration });
  const error = (message, duration) => addToast({ message, type: 'error', duration });
  const warning = (message, duration) => addToast({ message, type: 'warning', duration });

  const ToastDisplay = () => (
    <ToastContainer>
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </ToastContainer>
  );

  return {
    success,
    error,
    warning,
    addToast,
    removeToast,
    ToastDisplay
  };
};

export default { Toast, ToastContainer, useToast };