import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import socialPostService from '../services/socialPostService';
import { useToast } from '../components/common/ToastProvider';

const CreatePostModal = ({ isOpen, onClose, onPostCreated }) => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [location, setLocation] = useState(null);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [previewUrls, setPreviewUrls] = useState([]);
    const [activeTab, setActiveTab] = useState('photos');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState(null);
    const [locationSearch, setLocationSearch] = useState('');
    const [locationResults, setLocationResults] = useState([]);
    const [isSearchingLocation, setIsSearchingLocation] = useState(false);
    const [showLocationSearch, setShowLocationSearch] = useState(false);
    const [isDragOver, setIsDragOver] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const submitInProgressRef = useRef(false);
    const [currentStep, setCurrentStep] = useState(1); // 1: Media, 2: Details, 3: Review

    const fileInputRef = useRef();
    const locationSearchDebounceRef = useRef(null);
    const dropZoneRef = useRef();

    const steps = [
        { id: 1, icon: '📷', label: 'Media' },
        { id: 2, icon: '📝', label: 'Details' },
        { id: 3, icon: '✅', label: 'Review' },
    ];

    // Animation variants for step transitions
    const stepVariants = {
        initial: (direction) => ({
            x: direction > 0 ? 100 : -100,
            opacity: 0,
            position: 'absolute',
            width: '100%'
        }),
        animate: {
            x: 0,
            opacity: 1,
            position: 'relative',
            width: '100%'
        },
        exit: (direction) => ({
            x: direction < 0 ? 100 : -100,
            opacity: 0,
            position: 'absolute',
            width: '100%'
        })
    };

    // Track direction for step transitions
    const [stepDirection, setStepDirection] = useState(0);
    const goToStep = (step) => {
        if (step > currentStep) {
            if (currentStep === 1 && !validateMediaStep()) return;
            if (currentStep === 2 && !validateDetailsStep()) return;
        }
        setStepDirection(step - currentStep);
        setCurrentStep(step);
        setStepErrors({});
    };

    // Validation state for contextual feedback
    const [stepErrors, setStepErrors] = useState({});

    // Step validation logic
    const validateMediaStep = () => {
        if (selectedFiles.length === 0) {
            setStepErrors({ media: 'Please select at least one image or video.' });
            return false;
        }
        setStepErrors({});
        return true;
    };
    const validateDetailsStep = () => {
        const errors = {};
        if (!title) errors.title = 'Title is required.';
        if (!description) errors.description = 'Caption is required.';
        setStepErrors(errors);
        return Object.keys(errors).length === 0;
    };
    // Refactored: Only return errors, do not set state
    const getReviewStepErrors = () => {
        const errors = {};
        if (selectedFiles.length === 0) errors.media = 'No media selected.';
        if (!title) errors.title = 'Title is missing.';
        if (!description) errors.description = 'Caption is missing.';
        return errors;
    };

    // Error icon SVG
    const ErrorIcon = () => (
        <svg className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
    );

    // Validate and process files
    const validateAndProcessFiles = (files) => {
        const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
        const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/jpg', 'image/heic', 'image/heif', 'image/webp'];
        const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/mov', 'video/avi'];
        const MAX_FILES = 5;

        // Check if we're adding too many files
        if (selectedFiles.length + files.length > MAX_FILES) {
            setError(`You can only upload up to ${MAX_FILES} files`);
                return false;
            }

        // Validate each file
        for (const file of files) {
            // Check file size
            if (file.size > MAX_FILE_SIZE) {
                setError(`File "${file.name}" is too large. Maximum size is 50MB`);
                return false;
            }

            // Check file type
            const isImage = ALLOWED_IMAGE_TYPES.includes(file.type);
            const isVideo = ALLOWED_VIDEO_TYPES.includes(file.type);

            if (!isImage && !isVideo) {
                setError(`File "${file.name}" has an unsupported format. Allowed formats: JPEG, PNG, HEIC, HEIF, WebP, MP4, MOV, AVI`);
                return false;
            }

            // Additional validation for images
            if (isImage) {
                // Check image dimensions
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => {
                        if (img.width < 100 || img.height < 100) {
                            setError(`Image "${file.name}" is too small. Minimum dimensions are 100x100 pixels`);
                            resolve(false);
                        } else {
                            resolve(true);
                        }
                    };
                    img.onerror = () => {
                        setError(`Failed to load image "${file.name}". The file may be corrupted.`);
                        resolve(false);
                    };
                    img.src = URL.createObjectURL(file);
                });
            }

            // Additional validation for videos
            if (isVideo) {
                // Check if video duration is within limits (e.g., 5 minutes)
                return new Promise((resolve) => {
                    const video = document.createElement('video');
                    video.onloadedmetadata = () => {
                        if (video.duration > 300) { // 5 minutes in seconds
                            setError(`Video "${file.name}" is too long. Maximum duration is 5 minutes`);
                            resolve(false);
                        } else {
                            resolve(true);
                        }
                    };
                    video.onerror = () => {
                        setError(`Failed to load video "${file.name}". The file may be corrupted.`);
                        resolve(false);
                    };
                    video.src = URL.createObjectURL(file);
                });
            }
        }

        return true;
    };

    // Handle file selection
    const handleFileSelect = async (e) => {
        const files = e.target.files;
        if (!files.length) return;

        const isValid = await validateAndProcessFiles(files);
        if (!isValid) return;

        const validFiles = Array.from(files);
        setSelectedFiles(prev => [...prev, ...validFiles]);

        // Generate preview URLs with proper cleanup
        const newPreviewUrls = await Promise.all(validFiles.map(async file => {
            const url = URL.createObjectURL(file);
            return {
                url,
                type: file.type,
                name: file.name
            };
        }));

        setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
        setError(null);
    };

    // Handle drag and drop
    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isSubmitting) {
            setIsDragOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragOver(false);
    };

    const handleDrop = async (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragOver(false);

        if (isSubmitting) return;

        const files = e.dataTransfer.files;
        if (!files.length) return;

        const isValid = await validateAndProcessFiles(files);
        if (!isValid) return;

        const validFiles = Array.from(files);
        setSelectedFiles(prev => [...prev, ...validFiles]);

        // Generate preview URLs with proper cleanup
        const newPreviewUrls = await Promise.all(validFiles.map(async file => {
            const url = URL.createObjectURL(file);
            return {
                url,
                type: file.type,
                name: file.name
            };
        }));

        setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
        setError(null);
    };

    // Remove file and its preview
    const removeFile = (index) => {
        if (isSubmitting) return;

        // Revoke the blob URL before removing
        if (previewUrls[index]?.url.startsWith('blob:')) {
            URL.revokeObjectURL(previewUrls[index].url);
        }

        setSelectedFiles(prev => prev.filter((_, i) => i !== index));
        setPreviewUrls(prev => prev.filter((_, i) => i !== index));
    };

    // Handle location search
    const handleLocationSearch = async (query) => {
        if (!query.trim() || query.trim().length < 2) {
            setLocationResults([]);
            return;
        }

        setIsSearchingLocation(true);

        try {
            if (locationSearchDebounceRef.current) {
                clearTimeout(locationSearchDebounceRef.current);
            }

            locationSearchDebounceRef.current = setTimeout(async () => {
                const results = await socialPostService.searchPlaces(query);
                setLocationResults(results.predictions || []);
                setIsSearchingLocation(false);
            }, 500);
        } catch (error) {
            console.error('Error searching places:', error);
            setIsSearchingLocation(false);
        }
    };

    // Select location from search results
    const selectLocation = async (placeId, placeName) => {
        try {
            const details = await socialPostService.getPlaceDetails(placeId);
                setLocation({
                    place_id: placeId,
                    name: placeName,
                coordinates: details?.result?.geometry?.location
            });
            setShowLocationSearch(false);
            setLocationSearch('');
            setLocationResults([]);
        } catch (error) {
            console.error('Error getting place details:', error);
            setLocation({
                place_id: placeId,
                name: placeName
            });
            setShowLocationSearch(false);
            setLocationSearch('');
            setLocationResults([]);
        }
    };

    // Remove location
    const removeLocation = () => {
        setLocation(null);
    };

    // Success and error state for post submission
    const [postSuccess, setPostSuccess] = useState(false);
    const [postError, setPostError] = useState(null);
    const [postedData, setPostedData] = useState(null);

    // Toast notifications
    const { success: showSuccessToast, error: showErrorToast, info: showInfoToast } = useToast();

    // Wrap handleCreatePost to set success/error state
    const handleCreatePostWithFeedback = async () => {
        const reviewStepErrors = getReviewStepErrors();
        if (Object.keys(reviewStepErrors).length > 0) {
            setStepErrors(reviewStepErrors);
            return;
        }

        // Close modal immediately and show uploading toast
        handleCloseModal();
        showInfoToast({
            title: 'Uploading Post',
            description: 'Your image/video is being posted. You will be notified once it\'s done.'
        }, 8000);

        try {
            const result = await handleCreatePost();
            // Only show success if we actually got a valid result
            if (result && result.id) {
                showSuccessToast({
                    title: 'Post Created!',
                    description: 'Your post has been published successfully.'
                }, 5000);
                // Call onPostCreated to update the parent component
                onPostCreated(result);
            } else {
                // If no valid result, show error toast
                showErrorToast({
                    title: 'Post Failed',
                    description: 'Failed to create post. Please try again.'
                }, 6000);
            }
        } catch (err) {
            showErrorToast({
                title: 'Post Failed',
                description: err?.message || 'Failed to create post. Please try again.'
            }, 6000);
        }
    };

    // Reset modal for "Create Another"
    const handleCreateAnother = () => {
        setPostSuccess(false);
        setPostedData(null);
        setCurrentStep(1);
        setStepErrors({});
        // Optionally reset form fields here if needed
    };

    // Close modal and reset to first step
    const handleCloseModal = () => {
        resetForm();
        setCurrentStep(1);
        setStepErrors({});
        setPostSuccess(false);
        setPostError(null);
        setPostedData(null);
        onClose();
    };

    // Handle post creation
    const handleCreatePost = async () => {
        if (!title) {
            throw new Error('Please enter a title');
        }

        if (!description) {
            throw new Error('Please enter a caption');
        }

        if (selectedFiles.length === 0) {
            throw new Error('Please select at least one image or video');
        }

        if (submitInProgressRef.current) {
            throw new Error('Post creation already in progress');
        }

        submitInProgressRef.current = true;
        setIsSubmitting(true);
        setError(null);
        setUploadProgress(0);

        try {
            const newPost = await socialPostService.createPost({
                title,
                description,
                location_data: location,
                media_files: selectedFiles
            }, (percent) => setUploadProgress(percent));

            // Check if the post creation was successful
            if (newPost && newPost.id) {
                return newPost; // Return the created post for success validation
            } else {
                throw new Error('Post creation failed - no valid post returned');
            }
        } catch (error) {
            setError(error.message || 'Failed to create post');
            throw error; // Re-throw to be caught by handleCreatePostWithFeedback
        } finally {
            setIsSubmitting(false);
            submitInProgressRef.current = false;
        }
    };

    // Reset form
    const resetForm = () => {
        setTitle('');
        setDescription('');
        setLocation(null);
        setSelectedFiles([]);
        setPreviewUrls([]);
        setError(null);
        setLocationSearch('');
        setLocationResults([]);
        setShowLocationSearch(false);
        setUploadProgress(0);
    };

    // Cleanup preview URLs when component unmounts or when previews change
    useEffect(() => {
        return () => {
            previewUrls.forEach(preview => {
                if (preview.url.startsWith('blob:')) {
                    URL.revokeObjectURL(preview.url);
                }
            });
        };
    }, [previewUrls]);

    // Utility: detect mobile
    const isMobile = () => typeof window !== 'undefined' && window.innerWidth < 640;

    // Swipe gesture for step navigation (mobile only)
    const handleStepDragEnd = (event, info) => {
        if (!isMobile()) return;
        if (info.offset.x < -60 && currentStep < 3) goToStep(currentStep + 1);
        if (info.offset.x > 60 && currentStep > 1) goToStep(currentStep - 1);
    };

    // Step 1: Media Selection
    const renderMediaStep = () => (
            <motion.div
            key="media"
            custom={stepDirection}
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            drag={isMobile() ? "x" : false}
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.2}
            onDragEnd={handleStepDragEnd}
            className="w-full h-full"
        >
            <div className="max-w-xl mx-auto px-4 py-8 w-full">
                {/* Contextual error message for media step */}
                <AnimatePresence>
                    {stepErrors.media && (
                <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="flex items-center p-3 mb-4 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm shadow-sm"
                                    >
                            <ErrorIcon />
                            {stepErrors.media}
                        </motion.div>
                    )}
                </AnimatePresence>
                            <div
                                ref={dropZoneRef}
                                onClick={() => !isSubmitting && fileInputRef.current.click()}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                    className={`relative border-2 border-dashed rounded-2xl transition-all duration-200 dark:bg-gray-800 shadow-md bg-gradient-to-br from-indigo-50 via-white to-blue-50 flex flex-col items-center justify-center min-h-[220px] ${
                                    isDragOver 
                                        ? 'border-indigo-500 bg-indigo-50/50 dark:bg-gray-900' 
                                        : 'border-gray-300 hover:border-indigo-400 dark:bg-gray-800'
                    } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} mb-6`}
                            >
                                {previewUrls.length > 0 ? (
                                    <div className="p-4 w-full">
                                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                            <AnimatePresence initial={false}>
                                        {previewUrls.map((preview, index) => (
                                                <motion.div
                                    key={preview.url}
                                                    initial={{ opacity: 0, scale: 0.8 }}
                                                    animate={{ opacity: 1, scale: 1 }}
                                                    exit={{ opacity: 0, scale: 0.8 }}
                                    transition={{ duration: 0.2 }}
                                    className="relative aspect-square rounded-xl overflow-hidden group shadow-sm bg-white/90 border border-gray-200"
                                                >
                                                {preview.type.startsWith('image/') ? (
                                                    <img
                                                        src={preview.url}
                                                            alt={`Preview ${index + 1}`}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <video
                                                        src={preview.url}
                                                        className="w-full h-full object-cover"
                                                        controls
                                                    />
                                                )}
                                                    <motion.button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            removeFile(index);
                                                        }}
                                        className="absolute top-2 right-2 p-1.5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md z-10"
                                                        whileHover={{ scale: 1.1 }}
                                                        whileTap={{ scale: 0.9 }}
                                                    disabled={isSubmitting}
                                                >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                    </motion.button>
                                                </motion.div>
                                            ))}
                            </AnimatePresence>
                                    </div>
                                </div>
                            ) : (
                                        <motion.div
                                            animate={{
                                                scale: isDragOver ? 1.1 : 1,
                                                rotate: isDragOver ? 5 : 0,
                                            }}
                            className="flex flex-col items-center justify-center w-full py-12"
                                        >
                            <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-indigo-500 to-blue-500 dark:bg-gray-800 flex items-center justify-center shadow-lg">
                                            {/* Playful camera/gallery icon */}
                                            <svg
                                                className="w-12 h-12 text-white"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <rect x="3" y="7" width="18" height="13" rx="4" strokeWidth="2" />
                                                <circle cx="12" cy="13" r="4" strokeWidth="2" />
                                                <path d="M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" strokeWidth="2" />
                                            </svg>
                            </div>
                                        <p className="text-lg font-semibold text-gray-700 mb-2 text-center">
                                            {isDragOver ? 'Drop your files here!' : 'Click or drag files to upload'}
                                        </p>
                                        <p className="text-sm text-gray-500 text-center">
                                            {activeTab === 'photos' ? 'Up to 5 images or 1 video (max 50MB)' : 'One video (max 50MB)'}
                                        </p>
                        </motion.div>
                                )}
                                        </div>
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileSelect}
                                className="hidden"
                                accept={activeTab === 'photos' ? 'image/*' : 'video/*'}
                                multiple={activeTab === 'photos'}
                                disabled={isSubmitting}
                            />
                {/* Navigation */}
                <div className="flex flex-col gap-3 mt-8">
                    {currentStep === 1 && (
                        <motion.button
                            type="button"
                            className="w-full py-3 bg-gradient-to-r from-indigo-600 via-blue-600 to-indigo-700 text-white rounded-2xl font-bold text-lg shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400 disabled:opacity-60 disabled:cursor-not-allowed"
                            onClick={() => goToStep(2)}
                            disabled={selectedFiles.length === 0}
                            whileHover={{ scale: selectedFiles.length > 0 ? 1.03 : 1 }}
                            whileTap={{ scale: selectedFiles.length > 0 ? 0.98 : 1 }}
                        >
                            Next
                        </motion.button>
                    )}
                    {currentStep === 2 && (
                        <div className="flex gap-3">
                            <motion.button
                                type="button"
                                className="w-1/2 py-3 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 rounded-2xl font-bold text-lg shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
                                onClick={() => goToStep(1)}
                                whileHover={{ scale: 1.03 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Back
                            </motion.button>
                            <motion.button
                                type="button"
                                className="w-1/2 py-3 bg-gradient-to-r from-indigo-600 via-blue-600 to-indigo-700 text-white rounded-2xl font-bold text-lg shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400 disabled:opacity-60 disabled:cursor-not-allowed"
                                onClick={() => goToStep(3)}
                                disabled={!title}
                                whileHover={{ scale: title ? 1.03 : 1 }}
                                whileTap={{ scale: title ? 0.98 : 1 }}
                            >
                                Next
                            </motion.button>
                        </div>
                    )}
                    {currentStep === 3 && (
                        <div className="flex gap-3">
                            <motion.button
                                type="button"
                                className="w-1/2 py-3 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 rounded-2xl font-bold text-lg shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
                                onClick={() => goToStep(2)}
                                whileHover={{ scale: 1.03 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Back
                            </motion.button>
                            <motion.button
                                type="button"
                                className="w-1/2 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-2xl font-bold text-lg shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400 disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center"
                                onClick={handleCreatePostWithFeedback}
                                disabled={isSubmitting || Object.keys(getReviewStepErrors()).length > 0}
                                whileHover={{ scale: (!isSubmitting && Object.keys(getReviewStepErrors()).length === 0) ? 1.03 : 1 }}
                                whileTap={{ scale: (!isSubmitting && Object.keys(getReviewStepErrors()).length === 0) ? 0.98 : 1 }}
                            >
                                {isSubmitting ? (
                                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                    </svg>
                                ) : null}
                                {isSubmitting ? 'Posting...' : 'Post'}
                            </motion.button>
                        </div>
                    )}
                </div>
            </div>
        </motion.div>
    );

    // Step 2: Post Details
    const renderDetailsStep = () => (
        <motion.div
            key="details"
            custom={stepDirection}
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            drag={isMobile() ? "x" : false}
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.2}
            onDragEnd={handleStepDragEnd}
            className="w-full h-full"
        >
            <div className="max-w-xl mx-auto px-4 py-8 w-full">
                {/* Contextual error message for details step */}
                <AnimatePresence>
                    {stepErrors.title && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="flex items-center p-3 mb-4 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm shadow-sm"
                        >
                            <ErrorIcon />
                            {stepErrors.title}
                        </motion.div>
                    )}
                    {stepErrors.description && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="flex items-center p-3 mb-4 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm shadow-sm"
                        >
                            <ErrorIcon />
                            {stepErrors.description}
                        </motion.div>
                    )}
                </AnimatePresence>
                        <div className="space-y-6">
                            {/* Title */}
                    <div className="bg-gradient-to-br from-white via-indigo-50 to-blue-50 rounded-2xl shadow-sm p-5 border border-gray-100">
                                <label htmlFor="post-title" className="block text-base font-semibold text-gray-700 mb-2">
                                        Title <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="post-title"
                                    type="text"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    maxLength={45}
                                    placeholder="Give your post a title"
                            className="w-full px-5 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-colors bg-white/80 shadow-sm text-base placeholder-gray-400 dark:placeholder-gray-50 dark:bg-gray-800/70 dark:border-gray-700 dark:text-white dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
                                    disabled={isSubmitting}
                                />
                                <p className="text-xs text-gray-500 mt-1">{title.length}/45 characters</p>
                            </div>
                            {/* Description */}
                    <div className="bg-gradient-to-br from-white via-indigo-50 to-blue-50 rounded-2xl shadow-sm p-5 border border-gray-100">
                                <label htmlFor="post-description" className="block text-base font-semibold text-gray-700 mb-2">
                                    Caption <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    id="post-description"
                                    value={description}
                                    onChange={(e) => setDescription(e.target.value)}
                                    rows={4}
                                    placeholder="Write a caption..."
                            className="w-full px-5 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-colors bg-white/80 shadow-sm text-base placeholder-gray-400 dark:placeholder-gray-50 dark:bg-gray-800/70 dark:border-gray-700 dark:text-white dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
                                    disabled={isSubmitting}
                                    />
                                <p className="text-xs text-gray-500 mt-1">
                                    {description.split(/\s+/).filter(Boolean).length}/100 words
                                </p>
                            </div>
                            {/* Location */}
                    {/* <div className="bg-gradient-to-br from-white via-indigo-50 to-blue-50 dark:bg-gray-800 rounded-2xl shadow-sm p-5 border border-gray-100">
                                <label className="block text-base font-semibold text-gray-700 mb-2">
                                    Location
                                </label>
                                {location ? (
                            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-xl bg-white/90 dark:bg-gray-800 shadow-sm">
                                        <div className="flex items-center">
                                                <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                                <span className="text-gray-700">{location.name}</span>
                                        </div>
                                        <button
                                            onClick={removeLocation}
                                                className="text-gray-400 hover:text-gray-600"
                                            disabled={isSubmitting}
                                        >
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                    ) : (
                                        <div className="relative">
                                            <input
                                                type="text"
                                                value={locationSearch}
                                                onChange={(e) => {
                                                    setLocationSearch(e.target.value);
                                                    handleLocationSearch(e.target.value);
                                                }}
                                                onFocus={() => setShowLocationSearch(true)}
                                                placeholder="Add location"
                                    className="w-full px-5 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-colors bg-white/80 shadow-sm text-base placeholder-gray-400 dark:placeholder-white dark:bg-gray-800/70 dark:border-gray-700 dark:text-white dark:placeholder-gray-500 dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
                                                disabled={isSubmitting}
                                            />
                                            {showLocationSearch && locationResults.length > 0 && (
                                                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg">
                                                    {locationResults.map((result) => (
                                                    <button
                                                            key={result.place_id}
                                                            onClick={() => selectLocation(result.place_id, result.description)}
                                                            className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none rounded-xl"
                                                        >
                                                            {result.description}
                                                    </button>
                                                    ))}
                                                </div>
                                                )}
                                        </div>
                                                                )}
                                                            </div> */}
                </div>
                {/* Navigation */}
                <div className="flex justify-between mt-6 border-t border-gray-100 pt-6">
                    <motion.button
                        type="button"
                        className="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-xl min-h-[48px] min-w-[48px] text-base sm:text-base shadow-md"
                        onClick={() => goToStep(2)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                    >
                        Back
                    </motion.button>
                    <motion.button
                        type="button"
                        className="px-6 py-2.5 bg-indigo-600 text-white rounded-xl min-h-[48px] min-w-[48px] text-base sm:text-base shadow-md"
                        onClick={() => goToStep(3)}
                        disabled={!title}
                        whileHover={{ scale: title ? 1.05 : 1 }}
                        whileTap={{ scale: title ? 0.97 : 1 }}
                    >
                        Next
                    </motion.button>
                </div>
            </div>
        </motion.div>
    );

    // Step 3: Review & Submit
    const renderReviewStep = () => {
        const reviewStepErrors = getReviewStepErrors();
        const hasReviewErrors = Object.keys(reviewStepErrors).length > 0;
        return (
            <motion.div
                key="review"
                custom={stepDirection}
                variants={stepVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                drag={isMobile() ? "x" : false}
                dragConstraints={{ left: 0, right: 0 }}
                dragElastic={0.2}
                onDragEnd={handleStepDragEnd}
                className="w-full h-full"
            >
                <div className="max-w-xl mx-auto px-4 py-8 w-full">
                    {/* Error summary for review step */}
                    <AnimatePresence>
                        {hasReviewErrors && Object.keys(reviewStepErrors).length > 0 && (
                                    <motion.div
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className="flex items-start p-3 mb-4 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm shadow-sm"
                                    >
                                <ErrorIcon />
                                <div>
                                    <div className="font-semibold mb-1">Please fix the following issues before posting:</div>
                                    <ul className="list-disc ml-6">
                                        {Object.entries(reviewStepErrors).map(([field, msg]) => (
                                            <li key={field}>{msg}</li>
                                        ))}
                                    </ul>
                                </div>
                                    </motion.div>
                        )}
                    </AnimatePresence>
                    {/* Card layout for review summary */}
                    <div className="mb-8 bg-gradient-to-br from-white via-indigo-50 to-blue-50 dark:bg-gray-900 rounded-3xl shadow-xl p-8 border border-indigo-100 flex flex-col items-center">
                        {/* Media Preview Section */}
                        <div className="mb-6 flex flex-col items-center w-full">
                            <div className="flex items-center justify-center w-full">
                                <span className="font-bold text-gray-700 text-center text-base mr-4">Media:</span>
                                <button
                                    className="ml-auto text-indigo-600 hover:underline text-xs font-semibold px-3 py-1 rounded-lg bg-indigo-50 hover:bg-indigo-100 transition-colors"
                                    onClick={() => goToStep(1)}
                                >
                                    Edit
                                </button>
                            </div>
                            <div className="mt-3 grid grid-cols-2 sm:grid-cols-3 gap-4 w-full">
                                {previewUrls.slice(0, 3).map((preview, idx) => (
                                    <div key={preview.url} className="w-full aspect-square rounded-2xl overflow-hidden border-2 border-indigo-100 bg-white shadow-md flex items-center justify-center">
                                        {preview.type.startsWith('image/') ? (
                                            <img src={preview.url} alt={`Preview ${idx + 1}`} className="w-full h-full object-cover" />
                                        ) : (
                                            <video src={preview.url} className="w-full h-full object-cover" />
                                        )}
                                    </div>
                                ))}
                                {previewUrls.length > 3 && (
                                    <span className="ml-2 text-xs text-gray-500 self-center">+{previewUrls.length - 3} more</span>
                                )}
                            </div>
                        </div>
                        {/* Title/Description Section */}
                        <div className="mb-4 flex items-center w-full">
                            <span className="font-bold text-gray-700 text-left text-base w-32">Title:</span>
                            <span className="text-gray-900 text-left flex-1">{title}</span>
                            <button
                                className="ml-4 text-indigo-600 hover:underline text-xs font-semibold px-3 py-1 rounded-lg bg-indigo-50 hover:bg-indigo-100 transition-colors"
                                onClick={() => goToStep(2)}
                            >
                                Edit
                            </button>
                        </div>
                        <div className="mb-4 flex items-center w-full">
                            <span className="font-bold text-gray-700 text-left text-base w-32">Caption:</span>
                            <span className="text-gray-900 text-left flex-1">{description}</span>
                            <button
                                className="ml-4 text-indigo-600 hover:underline text-xs font-semibold px-3 py-1 rounded-lg bg-indigo-50 hover:bg-indigo-100 transition-colors"
                                onClick={() => goToStep(2)}
                            >
                                Edit
                            </button>
                        </div>
                        {/* Location Section */}
                        {/* <div className="mb-2 flex items-center w-full">
                            <span className="font-bold text-gray-700 text-base w-32">Location:</span>
                            <span className="text-gray-900 flex-1">{location ? location.name : 'None'}</span>
                            <button
                                className="ml-4 text-indigo-600 hover:underline text-xs font-semibold px-3 py-1 rounded-lg bg-indigo-50 hover:bg-indigo-100 transition-colors"
                                onClick={() => goToStep(2)}
                            >
                                Edit
                            </button>
                        </div> */}
                    </div>
                    {/* Navigation */}
                    <div className="flex justify-between mt-6 border-t border-gray-100 pt-6">
                            <motion.button
                            type="button"
                            className="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-xl min-h-[48px] min-w-[48px] text-base sm:text-base shadow-md"
                            onClick={() => goToStep(2)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.97 }}
                        >
                            Back
                            </motion.button>
                            <motion.button
                            type="button"
                            className="px-6 py-2.5 bg-green-600 text-white rounded-xl min-h-[48px] min-w-[48px] text-base sm:text-base shadow-md flex items-center justify-center"
                            onClick={handleCreatePostWithFeedback}
                            disabled={isSubmitting || hasReviewErrors}
                            whileHover={{ scale: (!isSubmitting && !hasReviewErrors) ? 1.05 : 1 }}
                            whileTap={{ scale: (!isSubmitting && !hasReviewErrors) ? 0.97 : 1 }}
                        >
                                    {isSubmitting ? (
                                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                            </svg>
                            ) : null}
                            {isSubmitting ? 'Posting...' : 'Post'}
                        </motion.button>
                                </div>
                </div>
            </motion.div>
        );
    };

    // Animated error state
    const renderErrorState = () => (
        <motion.div
            key="error"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            className="flex flex-col items-center justify-center py-12 w-full h-full overflow-y-auto"
        >
            <div className="w-full max-w-md mx-auto flex flex-col items-center justify-center">
                <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1.1, rotate: 0 }}
                    transition={{ type: 'spring', stiffness: 400, damping: 15 }}
                    className="mb-6"
                >
                    <svg className="w-20 h-20 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <motion.path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="3"
                            d="M6 18L18 6M6 6l12 12"
                            initial={{ pathLength: 0 }}
                            animate={{ pathLength: 1 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                        />
                    </svg>
                </motion.div>
                <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    className="text-2xl font-bold text-red-700 mb-2 text-center"
                >
                    Post Failed!
                </motion.h2>
                <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.4 }}
                    className="text-lg text-gray-700 mb-6 text-center px-4"
                >
                    {postError}
                </motion.p>

                {/* Action buttons */}
                <div className="flex flex-col sm:flex-row gap-3 w-full px-4">
                    <motion.button
                        onClick={() => {
                            setPostError(null);
                            setCurrentStep(3); // Go back to review step
                        }}
                        className="flex-1 py-3 bg-gradient-to-r from-indigo-600 via-blue-600 to-indigo-700 text-white rounded-2xl font-bold text-lg shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        Try Again
                    </motion.button>
                    <motion.button
                        onClick={handleCloseModal}
                        className="flex-1 py-3 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 rounded-2xl font-bold text-lg shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        Cancel
                    </motion.button>
                </div>
            </div>
        </motion.div>
    );

    // Animated success state
    const renderSuccessState = () => (
        <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            className="flex flex-col items-center justify-center py-12 w-full h-full overflow-y-auto"
        >
            <div className="w-full max-w-md mx-auto flex flex-col items-center justify-center">
                <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1.1, rotate: 0 }}
                    transition={{ type: 'spring', stiffness: 400, damping: 15 }}
                    className="mb-6"
                >
                    <svg className="w-20 h-20 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <motion.path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="3"
                            d="M5 13l4 4L19 7"
                            initial={{ pathLength: 0 }}
                            animate={{ pathLength: 1 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                        />
                    </svg>
                </motion.div>
                <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    className="text-2xl font-bold text-green-700 mb-2 text-center"
                >
                    Post Created!
                </motion.h2>
                <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.4 }}
                    className="text-lg text-gray-700 mb-6 text-center"
                >
                    Your post has been published successfully.
                </motion.p>
                {/* Summary card */}
                <div className="mb-6 bg-gradient-to-br from-white via-indigo-50 to-blue-50 rounded-2xl shadow-md p-6 border border-gray-100 w-full flex flex-col items-center">
                    <div className="mb-4 flex items-center w-full">
                        <span className="font-semibold text-left text-gray-700 w-24">Title:</span>
                        <span className="text-gray-900 text-left flex-1">{postedData?.title}</span>
                    </div>
                    <div className="mb-4 flex items-center w-full">
                        <span className="font-semibold text-left text-gray-700 w-24">Description:</span>
                        <span className="text-gray-900 text-left flex-1">{postedData?.description}</span>
                    </div>
                    {/* <div className="mb-4 flex items-center w-full">
                        <span className="font-semibold text-gray-700 w-24">Location:</span>
                        <span className="text-gray-900 flex-1">{postedData?.location ? postedData.location.name : 'None'}</span>
                    </div> */}
                    <div className="flex items-center w-full">
                        <span className="font-semibold text-gray-700 w-24">Media:</span>
                        <div className="flex gap-2">
                            {postedData?.previewUrls?.slice(0, 3).map((preview, idx) => (
                                <div key={preview.url} className="w-10 h-10 rounded-lg overflow-hidden border border-gray-200 bg-white shadow-sm flex items-center justify-center">
                                    {preview.type.startsWith('image/') ? (
                                        <img src={preview.url} alt={`Preview ${idx + 1}`} className="w-full h-full object-cover" />
                                    ) : (
                                        <video src={preview.url} className="w-full h-full object-cover" />
                                    )}
                                </div>
                            ))}
                            {postedData?.previewUrls?.length > 3 && (
                                <span className="ml-2 text-xs text-gray-500">+{postedData.previewUrls.length - 3} more</span>
                            )}
                        </div>
                    </div>
                </div>
                {/* Next steps */}
                <div className="flex flex-col sm:flex-row gap-4 mt-4 justify-center w-full max-w-md">
                    <motion.button
                        type="button"
                        className="flex-1 py-3 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 rounded-2xl font-bold text-lg shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 min-h-[44px] min-w-[44px]"
                        onClick={handleCreateAnother}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                    >
                        Create Another
                            </motion.button>
                </div>
                    </div>
                </motion.div>
    );

    // Animated error banner for post errors
    const renderPostErrorBanner = () => (
        <AnimatePresence>
            {postError && (
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="flex items-center p-4 mb-4 bg-red-50 border border-red-200 rounded-lg text-red-700 text-base shadow-md"
                >
                    <ErrorIcon />
                    <span className="flex-1">{postError}</span>
                    <button
                        className="ml-4 text-red-600 hover:underline text-xs font-medium"
                        onClick={() => setPostError(null)}
                    >
                        Dismiss
                    </button>
                </motion.div>
            )}
        </AnimatePresence>
    );

    // Responsive modal classes
    const modalClass =
        "w-full max-w-lg mx-4 sm:mx-0 bg-gradient-to-br from-white/95 via-white/90 to-white/95 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-gray-900/95 backdrop-blur-2xl " +
        "shadow-2xl border border-indigo-200/60 dark:border-indigo-900/60 ring-1 ring-indigo-100/60 dark:ring-indigo-900/60 " +
        "rounded-3xl " +
        "fixed sm:static top-0 left-0 sm:top-auto sm:left-auto " +
        "h-full sm:h-auto max-h-none sm:max-h-[90vh] " +
        "flex flex-col ";

    // Sticky footer for mobile
    const renderStickyFooter = (children) => (
        <div className="block sm:hidden sticky bottom-0 left-0 w-full bg-white/95 dark:bg-gray-900/95 border-t border-gray-200 dark:border-gray-800 z-10 px-4 py-3 flex justify-between gap-2 mt-auto">
            {children}
        </div>
    );

    if (!isOpen) return null;

    return (
        <AnimatePresence initial={false} mode="wait">
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="fixed inset-0 z-50 flex items-center justify-center p-0 sm:p-4 bg-black/60 backdrop-blur-md"
                onClick={handleCloseModal}
            >
                <motion.div
                    initial={{ scale: 0.96, opacity: 0, y: 32 }}
                    animate={{ scale: 1, opacity: 1, y: 0 }}
                    exit={{ scale: 0.96, opacity: 0, y: 32 }}
                    transition={{ type: 'spring', stiffness: 260, damping: 24 }}
                    onClick={(e) => e.stopPropagation()}
                    className={modalClass + (isMobile() ? 'rounded-none w-full h-full max-w-none' : '')}
                >
                    {/* Error banner for post errors */}
                    {renderPostErrorBanner()}
                    {/* Stepper/Progress Indicator */}
                    <div className="flex items-center justify-center gap-2 py-2 border-b border-gray-100 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80">
                        {steps.map((step, idx) => {
                            const isActive = currentStep === step.id;
                            const isCompleted = currentStep > step.id;
                            return (
                                <motion.div
                                    key={step.id}
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    transition={{ duration: 0.3, delay: idx * 0.05 }}
                                    className="flex items-center"
                                >
                                    <motion.div
                                        className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 text-lg font-bold
                                            ${isActive ? 'bg-gradient-to-br from-indigo-500 to-blue-500 border-indigo-500 text-white shadow-lg scale-110' : isCompleted ? 'bg-green-500 border-green-500 text-white' : 'bg-white border-gray-300 text-gray-400'}
                                            `}
                                        layoutId={isActive ? 'active-step' : undefined}
                                        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                                    >
                                        {step.icon}
                                    </motion.div>
                                    <span className={`ml-1 text-xs font-medium transition-colors duration-200 ${isActive ? 'text-indigo-700 font-bold' : isCompleted ? 'text-green-700' : 'text-gray-400'}`}>{step.label}</span>
                                    {idx < steps.length - 1 && (
                                        <motion.div
                                            className={`w-6 h-0.5 mx-1 rounded-full transition-all duration-300
                                                ${currentStep > step.id ? 'bg-green-500' : isActive ? 'bg-indigo-400' : 'bg-gray-200'}`}
                                            layoutId={isCompleted ? 'completed-bar' : undefined}
                                            transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                                        />
                                    )}
                                </motion.div>
                            );
                        })}
                    </div>
                    {/* Header */}
                    <div className="flex items-center justify-between px-8 py-6 border-b border-indigo-100/60 dark:border-indigo-900/60 bg-white/80 dark:bg-gray-900/80">
                        <div className="flex items-center space-x-4">
                            <motion.div
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ type: 'spring', stiffness: 300, damping: 20, delay: 0.1 }}
                                className="p-3 bg-gradient-to-br from-blue-400 to-indigo-400 dark:from-blue-900 dark:to-indigo-900 rounded-2xl shadow-md"
                            >
                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                                </svg>
                            </motion.div>
                            <motion.h2
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.15, duration: 0.4 }}
                                className="text-2xl font-extrabold bg-gradient-to-r from-blue-700 via-indigo-700 to-violet-700 dark:from-blue-300 dark:via-indigo-300 dark:to-violet-300 bg-clip-text text-transparent tracking-tight"
                            >
                                Create Post
                            </motion.h2>
                        </div>
                        <motion.button
                            onClick={handleCloseModal}
                            className="p-3 hover:bg-red-100 dark:hover:bg-red-900 rounded-full transition-all duration-200 shadow-md"
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            aria-label="Close modal"
                        >
                            <svg className="w-7 h-7 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </motion.button>
                    </div>
                    {/* Main Content: Render step based on currentStep or success state */}
                    <div className="flex-1 overflow-y-auto overflow-x-hidden relative pb-24 sm:pb-0 dark:bg-gray-900/80">
                        <AnimatePresence initial={false} custom={stepDirection} mode="wait">
                            {currentStep === 1
                                ? renderMediaStep()
                                : currentStep === 2
                                ? renderDetailsStep()
                                : renderReviewStep()}
                        </AnimatePresence>
                    </div>
                    {/* Sticky footer for mobile navigation (hide on success or error) */}
                    {!postSuccess && !postError && isMobile() && (
                        renderStickyFooter(
                            currentStep === 1 ? [
                                <motion.button
                                    key="next"
                                    type="button"
                                    className="flex-1 px-6 py-3 bg-indigo-600 text-white rounded-xl min-h-[48px] min-w-[48px] text-base font-semibold"
                                    onClick={() => goToStep(2)}
                                    disabled={selectedFiles.length === 0}
                                    whileHover={{ scale: selectedFiles.length > 0 ? 1.05 : 1 }}
                                    whileTap={{ scale: selectedFiles.length > 0 ? 0.97 : 1 }}
                                >
                                    Next
                                </motion.button>
                            ] : currentStep === 2 ? [
                                <motion.button
                                    key="back"
                                    type="button"
                                    className="flex-1 px-6 py-3 bg-gray-200 text-gray-700 rounded-xl min-h-[48px] min-w-[48px] text-base font-semibold"
                                    onClick={() => goToStep(1)}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.97 }}
                                >
                                    Back
                                </motion.button>,
                                <motion.button
                                    key="next"
                                    type="button"
                                    className="flex-1 px-6 py-3 bg-indigo-600 text-white rounded-xl min-h-[48px] min-w-[48px] text-base font-semibold"
                                    onClick={() => goToStep(3)}
                                    disabled={!title}
                                    whileHover={{ scale: title ? 1.05 : 1 }}
                                    whileTap={{ scale: title ? 0.97 : 1 }}
                                >
                                    Next
                                </motion.button>
                            ] : [
                                <motion.button
                                    key="back"
                                    type="button"
                                    className="flex-1 px-6 py-3 bg-gray-200 text-gray-700 rounded-xl min-h-[48px] min-w-[48px] text-base font-semibold"
                                    onClick={() => goToStep(2)}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.97 }}
                                >
                                    Back
                                </motion.button>,
                                <motion.button
                                    key="post"
                                    type="button"
                                    className="flex-1 px-6 py-3 bg-green-600 text-white rounded-xl min-h-[48px] min-w-[48px] text-base font-semibold"
                                    onClick={handleCreatePostWithFeedback}
                                    disabled={isSubmitting || Object.keys(getReviewStepErrors()).length > 0}
                                    whileHover={{ scale: (!isSubmitting && Object.keys(getReviewStepErrors()).length === 0) ? 1.05 : 1 }}
                                    whileTap={{ scale: (!isSubmitting && Object.keys(getReviewStepErrors()).length === 0) ? 0.97 : 1 }}
                                >
                                    {isSubmitting ? 'Posting...' : 'Post'}
                                </motion.button>
                            ]
                        )
                    )}
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default CreatePostModal;