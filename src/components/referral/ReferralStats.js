/**
 * ReferralStats Component
 * Displays user's referral statistics with animated cards and loading states
 */

import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { InlineLoader } from '../ui/LoadingIndicator';
import { UserGroupIcon, ChartBarIcon, GiftIcon, ArrowTrendingUpIcon } from '@heroicons/react/24/outline';

const StatCard = ({ title, value, icon: Icon, loading, className = '' }) => {
    const { t } = useTranslation('common');

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-6 bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-900/80 dark:to-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/50 dark:border-gray-800 hover:border-red-200 dark:hover:border-red-400 transition-all duration-300 hover:shadow-lg ${className}`}
        >
            <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-br from-red-500 to-orange-600 dark:from-red-700 dark:to-orange-700 rounded-xl shadow-lg">
                    <Icon className="w-6 h-6 text-white dark:text-gray-100" />
                </div>
                <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-600 dark:text-gray-300">{title}</h3>
                    {loading ? (
                        <div className="flex items-center space-x-2 mt-1">
                            <InlineLoader size="small" color="red" />
                            <span className="text-gray-500 dark:text-gray-400 text-sm">{t('loading', 'Loading...')}</span>
                        </div>
                    ) : (
                        <p className="text-2xl font-bold bg-gradient-to-r from-red-700 to-orange-700 dark:from-red-400 dark:to-orange-400 bg-clip-text text-transparent">
                            {value}
                        </p>
                    )}
                </div>
            </div>
        </motion.div>
    );
};

const ReferralStats = ({ loading = false, error = null, stats = {} }) => {
    const { t } = useTranslation(['referral', 'common']);

    // Progress bar calculation
    const points = stats.pointsEarned || 0;
    const tierSize = 1000;
    const currentTier = Math.floor(points / tierSize) + 1;
    const progressToNext = points % tierSize;
    const progressPercent = Math.min(100, (progressToNext / tierSize) * 100);

    // Milestone badges
    const milestones = [
        {
            label: t('statistics.milestones.firstReferral', 'First Referral'),
            achieved: (stats.totalReferrals || 0) > 0,
            icon: UserGroupIcon,
            color: 'emerald',
        },
        {
            label: t('statistics.milestones.100Points', '100 Points'),
            achieved: points >= 100,
            icon: GiftIcon,
            color: 'gold',
        },
        {
            label: t('statistics.milestones.500Points', '500 Points'),
            achieved: points >= 500,
            icon: GiftIcon,
            color: 'amethyst',
        },
        {
            label: t('statistics.milestones.1000Points', '1000 Points'),
            achieved: points >= 1000,
            icon: ArrowTrendingUpIcon,
            color: 'sapphire',
        },
    ];

    if (error) {
        return (
            <div className="text-center py-12">
                <div className="p-6 bg-red-50 dark:bg-red-900 rounded-2xl border border-red-100 dark:border-red-700 max-w-md mx-auto">
                    <svg className="w-12 h-12 text-red-400 dark:text-red-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-red-600 dark:text-red-300 font-medium mb-3">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6 dark:bg-gray-950">
            {/* Progress Bar to Next Tier */}
            <div className="mb-2">
                <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-200">{t('statistics.progressToNext', 'Progress to Next Tier')}</span>
                    <span className="text-xs font-semibold text-gold-600 dark:text-gold-400">{progressToNext} / {tierSize} {t('statistics.points', 'points')}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-3">
                    <div
                        className="h-3 rounded-full bg-gradient-to-r from-gold-400 via-emerald-400 to-amethyst-400 dark:from-gold-600 dark:via-emerald-600 dark:to-amethyst-600 transition-all duration-500"
                        style={{ width: `${progressPercent}%` }}
                    />
                </div>
                <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">{t('statistics.currentTier', 'Current Tier')}: <span className="font-bold text-emerald-600 dark:text-emerald-400">{currentTier}</span></div>
            </div>

            {/* Milestone Badges */}
            <div className="flex flex-wrap gap-3 mb-4">
                {milestones.map((ms, idx) => {
                    const Icon = ms.icon;
                    return (
                        <span
                            key={ms.label}
                            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow border
                                ${ms.achieved
                                    ? ms.color === 'gold' ? 'bg-gold-100 dark:bg-gold-900 text-gold-700 dark:text-gold-300 border-gold-300 dark:border-gold-700' :
                                      ms.color === 'emerald' ? 'bg-emerald-100 dark:bg-emerald-900 text-emerald-700 dark:text-emerald-300 border-emerald-300 dark:border-emerald-700' :
                                      ms.color === 'amethyst' ? 'bg-amethyst-100 dark:bg-amethyst-900 text-amethyst-700 dark:text-amethyst-300 border-amethyst-300 dark:border-amethyst-700' :
                                      ms.color === 'sapphire' ? 'bg-sapphire-100 dark:bg-sapphire-900 text-sapphire-700 dark:text-sapphire-300 border-sapphire-300 dark:border-sapphire-700' :
                                      'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200 border-gray-200 dark:border-gray-700'
                                    : 'bg-gray-100 dark:bg-gray-800 text-gray-400 border-gray-200 dark:border-gray-700 opacity-60'}
                            `}
                        >
                            <Icon className="w-4 h-4 mr-1" />
                            {ms.label}
                        </span>
                    );
                })}
            </div>

            {/* Recent Activity */}
            <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
                    {t('statistics.recentActivity', 'Recent Activity')}
                </h3>
                {loading ? (
                    <div className="flex items-center justify-center py-8">
                        <InlineLoader size="large" color="gold" />
                    </div>
                ) : stats.recentActivity?.length > 0 ? (
                    <div className="space-y-4">
                        {stats.recentActivity.map((activity, index) => (
                            <motion.div
                                key={activity.id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="p-4 bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm rounded-xl border border-white/50 dark:border-gray-800 hover:border-emerald-200 dark:hover:border-emerald-400 transition-all duration-300"
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="p-2 bg-gold-100 dark:bg-gold-900 rounded-lg">
                                            <GiftIcon className="w-5 h-5 text-gold-600 dark:text-gold-300" />
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-800 dark:text-gray-200">{activity.description}</p>
                                        </div>
                                    </div>
                                    <span className="text-sm font-medium text-gold-600 dark:text-gold-300">{activity.points} {t('statistics.points', 'points')}</span>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-xl">
                        <p className="text-gray-500 dark:text-gray-400">{t('statistics.noActivity', 'No recent activity')}</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ReferralStats; 