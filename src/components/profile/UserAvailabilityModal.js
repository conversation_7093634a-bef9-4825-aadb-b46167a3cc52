import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

const UserAvailabilityModal = ({ isOpen, onClose, availabilityData, availabilityRemarks }) => {
  const { t } = useTranslation('profile');
  
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/40 dark:bg-black/80 backdrop-blur-sm px-4 pb-4 sm:pb-0"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-t-3xl sm:rounded-3xl shadow-2xl dark:shadow-emerald-900/20 w-full max-w-lg mx-auto overflow-hidden border border-gray-200/50 dark:border-gray-700/50"
          initial={{ scale: 0.95, y: 100, opacity: 0 }}
          animate={{ scale: 1, y: 0, opacity: 1 }}
          exit={{ scale: 0.95, y: 100, opacity: 0 }}
          onClick={e => e.stopPropagation()}
          transition={{ type: "spring", duration: 0.5, bounce: 0.1 }}
        >
          {/* Header with gradient */}
          <div className="relative bg-gradient-to-br from-emerald-500 to-teal-600 dark:from-emerald-600 dark:to-teal-700 px-6 py-8 sm:px-8 sm:py-10">
            <div className="absolute inset-0 bg-black/10 dark:bg-black/20"></div>
            
            {/* Close button */}
            <motion.button
              onClick={onClose}
              className="absolute top-4 right-4 p-2.5 bg-white/20 dark:bg-black/20 backdrop-blur-md rounded-xl border border-white/30 dark:border-gray-600/50 hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-300 shadow-lg hover:shadow-xl z-20 group"
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: 0.1 }}
              aria-label="Close availability modal"
            >
              <svg className="w-5 h-5 text-white group-hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </motion.button>

            {/* Title and icon */}
            <div className="relative flex items-center justify-center gap-3">
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="p-3 bg-white/20 dark:bg-black/20 rounded-2xl backdrop-blur-sm"
              >
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </motion.div>
              <motion.h2 
                className="text-2xl sm:text-3xl font-bold text-white tracking-tight text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                {t('modals.availability.title')}
              </motion.h2>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-6 sm:px-8 sm:py-8 max-h-[60vh] sm:max-h-[70vh] overflow-y-auto">
            {availabilityData && Array.isArray(availabilityData) && availabilityData.length > 0 ? (
              <motion.div 
                className="space-y-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                {availabilityData.map((day, index) => {
                  const isAvailable = day.isAvailable ?? day.is_available;
                  const periods = Array.isArray(day.periods) ? day.periods : [];
                  
                  return (
                    <motion.div
                      key={day.day}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 * index }}
                      className={`p-4 rounded-2xl border transition-all duration-300 hover:shadow-md ${
                        isAvailable 
                          ? 'bg-emerald-50/80 dark:bg-emerald-950/30 border-emerald-200/60 dark:border-emerald-800/50 hover:bg-emerald-100/80 dark:hover:bg-emerald-950/50' 
                          : 'bg-gray-50/80 dark:bg-gray-800/30 border-gray-200/60 dark:border-gray-700/50 hover:bg-gray-100/80 dark:hover:bg-gray-800/50'
                      }`}
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                        {/* Day name */}
                        <div className="flex items-center gap-3 sm:w-1/3">
                          <div className={`w-3 h-3 rounded-full flex-shrink-0 ${
                            isAvailable 
                              ? 'bg-emerald-500 dark:bg-emerald-400 shadow-emerald-500/50 shadow-lg animate-pulse' 
                              : 'bg-gray-400 dark:bg-gray-500'
                          }`}></div>
                          <span className="font-semibold text-gray-900 dark:text-gray-100 text-base">
                            {day.day}
                          </span>
                        </div>

                        {/* Status badge */}
                        <div className="sm:w-1/3 flex justify-start sm:justify-center">
                          <span className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-semibold transition-all duration-200 ${
                            isAvailable 
                              ? 'bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-700/50' 
                              : 'bg-gray-100 dark:bg-gray-700/50 text-gray-700 dark:text-gray-400 border border-gray-200 dark:border-gray-600/50'
                          }`}>
                            {isAvailable ? (
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            )}
                            {isAvailable ? t('modals.availability.available') : t('modals.availability.notAvailable')}
                          </span>
                        </div>

                        {/* Time periods */}
                        <div className="sm:w-1/3 text-right">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-300 break-words">
                            {isAvailable && periods.length > 0
                              ? periods.map(p => `${p.start_time || p.startTime}–${p.end_time || p.endTime}`).join(', ')
                              : '—'}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>
            ) : (
              <motion.div 
                className="flex flex-col items-center justify-center py-16 px-6 text-center"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <div className="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-10 h-10 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <p className="text-lg font-semibold text-gray-600 dark:text-gray-300 mb-2">
                  {t('modals.availability.noData')}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  No availability schedule has been set up yet.
                </p>
              </motion.div>
            )}

            {/* Remarks section */}
            {availabilityRemarks && (
              <motion.div 
                className="mt-6 p-5 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-2xl border border-blue-200/60 dark:border-blue-800/50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex-shrink-0">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">
                      {t('modals.availability.remarks')}
                    </h4>
                    <p className="text-sm text-blue-800 dark:text-blue-300 leading-relaxed">
                      {availabilityRemarks}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Bottom handle for mobile */}
          <div className="flex justify-center pb-4 sm:hidden">
            <div className="w-12 h-1.5 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default UserAvailabilityModal;