import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import walletAPI from '../../services/walletService';
import orderAPI from '../../services/orderService';
import { useToast } from '../common/ToastProvider';
import ScheduleDateTimePicker from './ScheduleDateTimePicker';
import Toast from '../common/Toast';
import { useNavigate } from 'react-router-dom';
import useTranslation from '../../hooks/useTranslation';

/**
 * Enhanced Unified Order Flow Modal
 * 
 * This modal combines the entire order flow into one seamless experience:
 * 1. Service Selection
 * 2. Scheduling (Optional)
 * 3. Order Configuration
 * 4. Confirmation & Submission
 * 
 * Features:
 * - Progressive disclosure
 * - Real-time validation
 * - Contextual actions
 * - Visual progress indicators
 * - Mobile-optimized design
 * 
 * Animation Optimizations:
 * - Uses Framer Motion for hardware-accelerated animations
 * - Implements spring physics for natural motion
 * - Optimized transition timing for smooth UX
 * - Reduced motion support for accessibility
 * - Performance-optimized variants for consistent animations
 * - Efficient re-rendering with proper key props
 * - GPU-accelerated transforms (transform3d)
 * 
 * Responsive Design (Mobile-First):
 * - Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
 * - Mobile-optimized touch targets (minimum 44px)
 * - Responsive typography and spacing
 * - Adaptive layouts for different screen sizes
 * - Touch-friendly interactions with touch-manipulation
 * - Optimized modal sizing for mobile devices
 * - Responsive grid layouts and content organization
 */
const ServiceSelectionModal = ({
  isOpen,
  onClose,
  skill,
  onOrderPlaced,
  onViewOrders,
  availabilityData = [],
  specialDates = []
}) => {
  const { user } = useAuth();
  const { error: showErrorToast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation(['profile', 'common']);
  
  // Order flow state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTier, setSelectedTier] = useState(null);
  const [orderType, setOrderType] = useState('now'); // 'now' or 'later'
  const [scheduledDateTime, setScheduledDateTime] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [remarks, setRemarks] = useState('');
  
  // Validation and processing state
  const [walletBalance, setWalletBalance] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [createdOrder, setCreatedOrder] = useState(null);

  // Enhanced validation state
  const [validationErrors, setValidationErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate total price
  const totalPrice = selectedTier ? selectedTier.price * quantity : 0;
  const hasSufficientBalance = walletBalance >= totalPrice;

  // Enhanced validation helpers
  const validateField = (field, value) => {
    const errors = {};
    
    switch (field) {
      case 'selectedTier':
        if (!value) {
          errors.selectedTier = t('modals.serviceSelection.errors.selectTier', 'Please select a service tier');
        }
        break;
      case 'orderType':
        if (!value) {
          errors.orderType = t('modals.serviceSelection.errors.chooseOrderType', 'Please choose an order type');
        }
        break;
      case 'scheduledDateTime':
        if (orderType === 'later' && !value) {
          errors.scheduledDateTime = t('modals.serviceSelection.errors.selectSchedule', 'Please select a date and time for scheduling');
        } else if (orderType === 'later' && value) {
          const now = new Date();
          if (value <= now) {
            errors.scheduledDateTime = t('modals.serviceSelection.errors.futureDate', 'Please select a future date and time');
          }
        }
        break;
      case 'quantity':
        if (!value || value < 1) {
          errors.quantity = t('modals.serviceSelection.errors.quantityMin', 'Quantity must be at least 1');
        } else if (value > 99) {
          errors.quantity = t('modals.serviceSelection.errors.quantityMax', 'Quantity cannot exceed 99');
        }
        break;
      case 'remarks':
        if (value && value.length > 500) {
          errors.remarks = t('modals.serviceSelection.errors.remarksMax', 'Remarks cannot exceed 500 characters');
        }
        break;
      case 'walletBalance':
        if (!hasSufficientBalance) {
          errors.walletBalance = t('modals.serviceSelection.messages.insufficientBalanceNeed', 'Insufficient balance. You need {{amount}} more credits.', { amount: totalPrice - walletBalance });
        }
        break;
      default:
        break;
    }
    
    return errors;
  };

  // Comprehensive validation function
  const validateAllFields = () => {
    setIsValidating(true);
    const errors = {};
    
    // Validate all fields
    Object.assign(errors, validateField('selectedTier', selectedTier));
    Object.assign(errors, validateField('orderType', orderType));
    Object.assign(errors, validateField('scheduledDateTime', scheduledDateTime));
    Object.assign(errors, validateField('quantity', quantity));
    Object.assign(errors, validateField('remarks', remarks));
    Object.assign(errors, validateField('walletBalance', walletBalance));
    
    setValidationErrors(errors);
    setIsValidating(false);
    
    return Object.keys(errors).length === 0;
  };

  // Clear validation errors for a specific field
  const clearFieldError = (field) => {
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  // Enhanced error handling
  const handleError = (error, context = 'general') => {
    console.error(`Error in ${context}:`, error);
    
    let errorMessage = t('modals.serviceSelection.errors.unexpected', 'An unexpected error occurred. Please try again.');
    
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    
    setError({
      message: errorMessage,
      context,
      timestamp: new Date().toISOString()
    });
    if (isMobile()) showMobileError(errorMessage);
  };

  // Clear all errors
  const clearAllErrors = () => {
    setError(null);
    setValidationErrors({});
  };

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen && skill) {
      setCurrentStep(1);
      setSelectedTier(skill.tiers?.[0] || null);
      setOrderType('now');
      setScheduledDateTime(null);
      setQuantity(1);
      setRemarks('');
      setError(null);
      setOrderSuccess(false);
      setCreatedOrder(null);
      fetchWalletBalance();
    }
  }, [isOpen, skill]);

  // Fetch wallet balance
  const fetchWalletBalance = async () => {
    try {
      const response = await walletAPI.getBalance();
      setWalletBalance(response.data.credits_balance);
    } catch (err) {
      handleError(err, 'wallet_balance_fetch');
    }
  };

  // Real-time validation effects
  useEffect(() => {
    if (selectedTier) {
      const errors = validateField('selectedTier', selectedTier);
      if (errors.selectedTier) {
        setValidationErrors(prev => ({ ...prev, ...errors }));
    } else {
        clearFieldError('selectedTier');
      }
    }
  }, [selectedTier]);

  useEffect(() => {
    if (orderType) {
      const errors = validateField('orderType', orderType);
      if (errors.orderType) {
        setValidationErrors(prev => ({ ...prev, ...errors }));
      } else {
        clearFieldError('orderType');
      }
    }
  }, [orderType]);

  useEffect(() => {
    if (orderType === 'later') {
      const errors = validateField('scheduledDateTime', scheduledDateTime);
      if (errors.scheduledDateTime) {
        setValidationErrors(prev => ({ ...prev, ...errors }));
      } else {
        clearFieldError('scheduledDateTime');
      }
    }
  }, [scheduledDateTime, orderType]);

  useEffect(() => {
    const errors = validateField('quantity', quantity);
    if (errors.quantity) {
      setValidationErrors(prev => ({ ...prev, ...errors }));
    } else {
      clearFieldError('quantity');
    }
  }, [quantity]);

  useEffect(() => {
    const errors = validateField('remarks', remarks);
    if (errors.remarks) {
      setValidationErrors(prev => ({ ...prev, ...errors }));
    } else {
      clearFieldError('remarks');
    }
  }, [remarks]);

  useEffect(() => {
    const errors = validateField('walletBalance', walletBalance);
    if (errors.walletBalance) {
      setValidationErrors(prev => ({ ...prev, ...errors }));
    } else {
      clearFieldError('walletBalance');
    }
  }, [walletBalance, totalPrice]);

  // Handle tier selection
  const handleTierSelect = (tier) => {
    console.log('Selected tier:', tier);
    setSelectedTier(tier);
    clearFieldError('selectedTier');
  };

  // Handle order type selection
  const handleOrderTypeSelect = (type) => {
    console.log('Selected order type:', type);
    setOrderType(type);
    if (type === 'now') {
      setScheduledDateTime(null);
    }
    clearFieldError('orderType');
  };

  // Handle date/time selection
  const handleDateTimeSelect = (dateTime) => {
    console.log('Selected date/time:', dateTime);
    setScheduledDateTime(dateTime);
    clearFieldError('scheduledDateTime');
  };

  // Handle quantity change
  const handleQuantityChange = (newQuantity) => {
    const validQuantity = Math.max(1, Math.min(99, newQuantity));
    console.log('Selected quantity:', validQuantity);
    setQuantity(validQuantity);
    clearFieldError('quantity');
  };

  // Handle remarks change
  const handleRemarksChange = (e) => {
    const value = e.target.value.slice(0, 500); // Limit to 500 characters
    setRemarks(value);
    clearFieldError('remarks');
  };

  // Handle step navigation
  const handleNextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
      clearAllErrors();
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      clearAllErrors();
    }
  };

  // Handle order submission
  const handleSubmitOrder = async () => {
    // Clear previous errors but allow backend to handle validation
    clearAllErrors();

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      if (!skill.talent_id) {
        setError({ message: t('modals.serviceSelection.errors.talentIdMissing', 'Talent ID is missing. Please contact support.'), context: 'order_submission' });
        return;
      }
      if (
        skill.service_category?.slug?.toLowerCase() !== 'others' &&
        !selectedTier?.service_style_id
      ) {
        setError({
          message: t('modals.serviceSelection.errors.serviceStyleMissing', 'Service style is missing. Please select a service tier.'),
          context: 'order_submission'
        });
        return;
      }
      // Ensure pricing_option_type_id is present and valid
      const pricingOptionTypeId = selectedTier?.pricing_option_type_id || selectedTier?.pricing_option_id || selectedTier?.id;
      if (!pricingOptionTypeId || isNaN(Number(pricingOptionTypeId))) {
        setError({ message: t('modals.serviceSelection.errors.pricingOptionMissing', 'Pricing option is missing or invalid. Please select a valid service tier.'), context: 'order_submission' });
        setIsSubmitting(false);
        setIsLoading(false);
        return;
      }

      const orderData = {
        talent_id: skill.talent_id,
        user_service_id: skill.id,
        pricing_option_type_id: parseInt(pricingOptionTypeId, 10),
        service_category_id: skill.service_category_id || 1,
        quantity: quantity,
        remarks: remarks.trim() || null,
        scheduled_start_time:
          orderType === 'later' && scheduledDateTime
            ? new Date(
                scheduledDateTime.getTime() -
                  scheduledDateTime.getTimezoneOffset() * 60000
              ).toISOString()
            : null
      };
      if (skill.service_category?.slug?.toLowerCase() !== 'others') {
        orderData.service_style_id = selectedTier.service_style_id;
        orderData.service_type_id =
          skill.service_type_id ||
          skill.service_type?.id ||
          null;
      }
      let response;
      try {
        if (orderType === 'now') {
          response = await orderAPI.createOrderNow(orderData);
        } else {
          response = await orderAPI.scheduleOrder(orderData);
        }
      } catch (apiErr) {
        // Show backend error message in Toast if available
        const backendMsg = apiErr?.response?.data?.message;
        if (backendMsg) {
          setToastMessage(backendMsg);
          setShowToast(true);
        }
        setError({ message: apiErr?.response?.data?.message || apiErr.message || 'API error', context: 'order_submission' });
        setIsSubmitting(false);
        setIsLoading(false);
        return;
      }

      setCreatedOrder(response.data);
      setOrderSuccess(true);
      setCurrentStep(4);
    } catch (err) {
      const msg = err?.response?.data?.message;
      if (msg) {
        setToastMessage(msg);
        setShowToast(true);
      }
      handleError(err, 'order_submission');
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  // Validation helpers
  const canProceedToStep2 = selectedTier !== null && !validationErrors.selectedTier;
  const canProceedToStep3 = (orderType === 'now' || (orderType === 'later' && scheduledDateTime)) && 
                           !validationErrors.orderType && 
                           !validationErrors.scheduledDateTime;
  const canSubmitOrder = selectedTier &&
                        hasSufficientBalance &&
                        !isLoading &&
                        !isSubmitting;

  // Step configuration
  const steps = [
    { id: 1, title: t('modals.serviceSelection.steps.selectService', 'Select Service'), icon: '🎯' },
    { id: 2, title: t('modals.serviceSelection.steps.schedule', 'Schedule'), icon: '📅' },
    { id: 3, title: t('modals.serviceSelection.steps.configure', 'Configure'), icon: '⚙️' },
    { id: 4, title: t('modals.serviceSelection.steps.confirm', 'Confirm'), icon: '✅' }
  ];

  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, transition: { duration: 0.2, ease: "easeIn" } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1, 
      transition: { 
        duration: 0.4, 
        ease: "easeOut",
        type: "spring",
        stiffness: 300,
        damping: 30
      } 
    },
    exit: { 
      opacity: 0, 
      y: 50, 
      scale: 0.95, 
      transition: { 
        duration: 0.3, 
        ease: "easeIn" 
      } 
    }
  };

  // Step transition variants
  const stepVariants = {
    hidden: { opacity: 0, x: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      x: 0, 
      scale: 1, 
      transition: { 
        duration: 0.4, 
        ease: "easeOut",
        type: "spring",
        stiffness: 400,
        damping: 35
      } 
    },
    exit: { 
      opacity: 0, 
      x: -50, 
      scale: 0.95, 
      transition: { 
        duration: 0.3, 
        ease: "easeIn" 
      } 
    }
  };

  // Progress indicator variants
  const progressVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1, 
      transition: { 
        duration: 0.3, 
        ease: "easeOut" 
      } 
    }
  };

  // Success animation variants
  const successVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: { 
      scale: 1, 
      rotate: 0, 
      transition: { 
        duration: 0.6, 
        type: "spring",
        stiffness: 200,
        damping: 20
      } 
    }
  };

  // Loading animation variants
  const loadingVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0, 
      transition: { 
        duration: 0.3, 
        ease: "easeOut" 
      } 
    },
    exit: { 
      opacity: 0, 
      y: -10, 
      transition: { 
        duration: 0.2, 
        ease: "easeIn" 
      } 
    }
  };

  // Micro-interaction variants
  const buttonVariants = {
    hover: { 
      scale: 1.05, 
      transition: { 
        duration: 0.2, 
        ease: "easeOut" 
      } 
    },
    tap: { 
      scale: 0.95, 
      transition: { 
        duration: 0.1, 
        ease: "easeIn" 
      } 
    }
  };

  const cardVariants = {
    hover: { 
      scale: 1.02, 
      y: -2, 
      transition: { 
        duration: 0.2, 
        ease: "easeOut" 
      } 
    },
    tap: { 
      scale: 0.98, 
      transition: { 
        duration: 0.1, 
        ease: "easeIn" 
      } 
    }
  };

  // Utility: detect mobile
  const isMobile = () => typeof window !== 'undefined' && window.innerWidth < 640;

  // Long-press for quantity controls
  const quantityTimeout = useRef();
  const quantityInterval = useRef();
  const handleQuantityLongPress = (delta) => {
    clearTimeout(quantityTimeout.current);
    clearInterval(quantityInterval.current);
    quantityTimeout.current = setTimeout(() => {
      quantityInterval.current = setInterval(() => {
        handleQuantityChange(quantity + delta);
      }, 80);
    }, 400);
  };
  const clearQuantityLongPress = () => {
    clearTimeout(quantityTimeout.current);
    clearInterval(quantityInterval.current);
  };

  // Pull-to-refresh for wallet balance (step 3, mobile only)
  const [isRefreshing, setIsRefreshing] = useState(false);
  const pullStartY = useRef(null);
  const pullDeltaY = useRef(0);
  const [pullDistance, setPullDistance] = useState(0);
  const handleTouchStart = (e) => {
    if (!isMobile() || currentStep !== 3) return;
    pullStartY.current = e.touches[0].clientY;
    pullDeltaY.current = 0;
  };
  const handleTouchMove = (e) => {
    if (!isMobile() || currentStep !== 3 || pullStartY.current === null) return;
    pullDeltaY.current = e.touches[0].clientY - pullStartY.current;
    setPullDistance(Math.max(0, pullDeltaY.current));
  };
  const handleTouchEnd = async () => {
    if (!isMobile() || currentStep !== 3) return;
    if (pullDeltaY.current > 60) {
      setIsRefreshing(true);
      await fetchWalletBalance();
      setTimeout(() => setIsRefreshing(false), 600);
    }
    setPullDistance(0);
    pullStartY.current = null;
    pullDeltaY.current = 0;
  };

  // Mobile step swipe gesture
  const handleStepDragEnd = (event, info) => {
    if (!isMobile()) return;
    if (info.offset.x < -60 && currentStep < 4) handleNextStep();
    if (info.offset.x > 60 && currentStep > 1) handlePrevStep();
  };

  // For quick add quantity
  const quickAddOptions = [5, 10];
  const handleQuickAdd = (val) => {
    console.log('Quick add quantity:', val);
    handleQuantityChange(quantity + val);
  };

  // For clipboard paste (remarks)
  const handlePasteFromClipboard = async () => {
    if (navigator.clipboard) {
      const text = await navigator.clipboard.readText();
      if (text) setRemarks((prev) => (prev + ' ' + text).slice(0, 500));
    }
  };

  // For mobile error feedback
  const showMobileError = (msg) => {
    if (window.navigator && window.navigator.vibrate) window.navigator.vibrate(80);
    setMobileError(msg);
    setTimeout(() => setMobileError(null), 2200);
  };
  const [mobileError, setMobileError] = useState(null);

  // Scroll input into view on focus (mobile)
  const remarksRef = useRef();
  const handleRemarksFocus = () => {
    if (isMobile() && remarksRef.current) {
      setTimeout(() => remarksRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' }), 100);
    }
  };

  // Helper for consistent date/time formatting
  const formatScheduledDate = (date) => date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
  const formatScheduledTime = (date) => date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });

  // Always use the latest prop, do not store in local state
  useEffect(() => {
  }, [availabilityData]);

  // Toast state for backend error messages
  const [toastMessage, setToastMessage] = useState('');
  const [showToast, setShowToast] = useState(false);

  // Handler for "Message Talent" button after order placed
  const handleMessageTalent = () => {
    if (!createdOrder) return;
    // Save order details to localStorage for ChatWindow to pick up
    const orderMessage = {
      orderId: createdOrder.id,
      service: skill.name,
      quantity,
      talentId: skill.talent_id,
      initialMessage: `Hi! 🌟 I just booked your ${skill.name} service (x${quantity}) and I'm really looking forward to collaborating with you! Can't wait to see what we create together.`
    };
    localStorage.setItem('pendingOrderMessage', JSON.stringify(orderMessage));
    const params = new URLSearchParams({
      talent_id: skill.talent_id,
      order_id: createdOrder.id,
      action: 'new_conversation'
    });
    navigate(`/chat?${params.toString()}`);
  };

  if (!isOpen || !skill) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-0 sm:p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={onClose}
        >
          <motion.div
            className="bg-white/80 dark:bg-gradient-to-br from-indigo-800 dark:to-black backdrop-blur-2xl border border-indigo-100 dark:border-gray-800 rounded-3xl w-full max-w-full sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-screen max-h-screen sm:max-h-[90vh] overflow-hidden shadow-2xl mx-0 sm:mx-0 flex flex-col"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Animated gradient blobs for vibrancy */}
            <div className="absolute -top-12 -right-16 w-40 h-40 bg-gradient-to-br from-blue-400/30 to-indigo-400/30 rounded-full blur-2xl animate-pulse z-0 pointer-events-none" />
            <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-pink-400/30 to-blue-400/30 rounded-full blur-xl animate-pulse delay-1000 z-0 pointer-events-none" />

            {/* Header with Progress Indicator */}
            <div className="relative px-6 py-6 sm:px-8 sm:py-8 dark:text-white bg-gradient-to-br from-indigo-50 to-blue-50 dark:dark:from-yellow-400/10 dark:to-pink-400/10 border-b border-gray-100 gap-6">
              {/* Close Button */}
              <button
                onClick={onClose}
                className="absolute top-2 sm:top-4 right-2 sm:right-4 p-3 sm:p-3 text-white bg-red-500 hover:text-gray-600 hover:bg-white/50 rounded-full transition-all duration-200 touch-manipulation select-none min-w-[44px] min-h-[44px] flex items-center justify-center"
                aria-label="Close order modal"
                tabIndex={0}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* Service Header */}
              <div className="flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-2xl bg-gradient-to-br from-indigo-500 to-blue-600 dark:from-yellow-400/10 dark:to-pink-400/10 flex items-center justify-center shadow-lg flex-shrink-0">
                  {skill.image ? (
                    <img
                      src={skill.image}
                      alt={skill.name}
                      className="w-10 h-10 sm:w-12 sm:h-12 object-contain dark:text-white"
                      onError={e => { e.target.style.display = 'none'; }}
                    />
                  ) : skill.icon ? (
                    <img
                      src={skill.icon}
                      alt={skill.name}
                      className="w-12 h-12 sm:w-12 sm:h-12 object-cover dark:text-white"
                      onError={e => { e.target.style.display = 'none'; }}
                    />
                  ) : (
                    <svg
                      className="w-10 h-10 sm:w-12 sm:h-12 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h1 className="text-lg sm:text-xl lg:text-2xl text-left font-bold text-gray-900 dark:text-white mb-1 truncate">{skill.name}</h1>
                    {skill.ordersCount > 0 && (
                      <p className="text-xs sm:text-sm text-gray-600 dark:text-amber-200 font-medium">
                        {t('modals.serviceSelection.messages.ordersCount', { count: skill.ordersCount })}
                      </p>
                    )}
                  </div>
                </div>

              {/* Enhanced Progress Steps - Visually Connected */}
              <div className="flex items-center justify-between gap-2 sm:gap-4 px-2 sm:px-4 py-4">
                <div className="flex-1 flex items-center justify-between relative">
                  {steps.map((step, index) => {
                    const isCompleted = currentStep > step.id;
                    const isActive = currentStep === step.id;
                    const isLast = index === steps.length - 1;
                    return (
                      <React.Fragment key={step.id}>
                        <div className="flex flex-col items-center z-10 min-w-[60px]">
                          <div className={`flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full transition-all duration-300
                            ${isActive ? 'bg-gradient-to-r from-yellow-400 to-pink-400 text-white ring-4 ring-yellow-200/40 shadow-xl scale-110' :
                              isCompleted ? 'bg-gradient-to-r from-indigo-500 to-blue-500 dark:bg-amber-200 text-white shadow-lg' :
                              'bg-gray-200 dark:bg-gray-500 text-gray-400 dark:text-amber-500'}>
                          `}>
                            {isCompleted ? (
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                              </svg>
                            ) : (
                              <span className="text-lg font-extrabold">{step.icon}</span>
                            )}
                          </div>
                          <span className={`mt-2 text-xs sm:text-sm font-bold transition-colors duration-200
                            ${isActive ? 'text-yellow-500 dark:text-amber-200' : isCompleted ? 'text-indigo-600 dark:text-amber-200' : 'text-gray-400 dark:text-gray-500'}`}
                          >
                            {step.title}
                          </span>
                        </div>
                        {!isLast && (
                          <div className={`flex-1 h-1 mx-1 sm:mx-2 rounded-full transition-all duration-300
                            ${currentStep > step.id ? 'bg-gradient-to-r from-yellow-400 to-pink-400' : 'bg-gray-300 dark:bg-gray-700'}`}
                          />
                        )}
                      </React.Fragment>
                    );
                  })}
                </div>
              </div>
              </div>

            {/* Content Area */}
            <div className="flex-1 px-6 py-6 sm:px-8 sm:py-8 overflow-y-auto h-full gap-6 flex flex-col">
              <AnimatePresence mode="wait">
                {/* Step 1: Service Selection */}
                {currentStep === 1 && (
                  <motion.div
                    key="step1"
                    variants={stepVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ duration: 0.4 }}
                    drag={isMobile() ? "x" : false}
                    dragConstraints={{ left: 0, right: 0 }}
                    dragElastic={0.2}
                    onDragEnd={handleStepDragEnd}
                  >
                    <motion.h2 
                      className="text-2xl font-extrabold text-gray-900 dark:text-indigo-50 mb-6 flex items-center"
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      <span className="w-1 h-4 sm:h-6 bg-gradient-to-b from-indigo-500 to-blue-500 dark:bg-amber-200 rounded-full mr-2 sm:mr-3"></span>
                      {t('modals.serviceSelection.titles.step1', 'Select Service Tier')}
                    </motion.h2>

                    {/* Service Description Card */}
                    {skill.description && (
                      <motion.div
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ delay: 0.2, duration: 0.4 }}
                        className="bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-amber-200 border-2 rounded-2xl p-8 mb-8"
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div className="ml-2 sm:ml-3">
                            <h3 className="text-xs sm:text-sm text-left font-medium text-blue-900">About this service</h3>
                            <p className="text-xs sm:text-sm text-left text-blue-700 mt-1 leading-relaxed">{skill.description}</p>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Service Tiers Grid - Responsive */}
                    <div className="space-y-6">
                      {skill.tiers && skill.tiers.map((tier, index) => (
                        <motion.div
                          key={tier.id ?? index}
                          initial={{ opacity: 0, y: 30, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          transition={{
                            delay: 0.3 + index * 0.1, 
                            duration: 0.4,
                            type: "spring",
                            stiffness: 300,
                            damping: 30
                          }}
                          className={`relative group cursor-pointer transition-all duration-300 ${
                        selectedTier && selectedTier.id === tier.id
                              ? 'transform scale-[1.02] dark:text-white'
                              : 'hover:scale-[1.01] dark:text-white'
                          }`}
                        >
                          {/* Selection Indicator */}
                          {selectedTier && selectedTier.id === tier.id && (
                            <motion.div
                              initial={{ scale: 0, opacity: 0, rotate: -180 }}
                              animate={{ scale: 1, opacity: 1, rotate: 0 }}
                              transition={{ 
                                duration: 0.4, 
                                type: "spring",
                                stiffness: 200,
                                damping: 20
                              }}
                              className="absolute -top-2 sm:-top-3 -right-2 sm:-right-3 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg z-10"
                            >
                              <svg className="w-3 h-3 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                              </svg>
                            </motion.div>
                          )}

                          {/* Tier Card */}
                          <motion.div
                            variants={cardVariants}
                            whileHover="hover"
                            whileTap="tap"
                            className={`relative border-2 border-indigo-100 dark:border-gray-800 rounded-2xl bg-white/40 dark:bg-gray-900/60 backdrop-blur-md p-8 mb-8 transition-all duration-300 ${
                              selectedTier && selectedTier.id === tier.id
                                ? 'ring-2 ring-indigo-500 dark:ring-amber-200 shadow-lg'
                                : 'hover:ring-2 hover:ring-indigo-300 dark:ring-amber-400 hover:shadow-md'
                      }`}
                      onClick={() => handleTierSelect(tier)}
                    >
                            {/* Popular Badge */}
                            {tier.isPopular && (
                              <motion.div 
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.5 + index * 0.1 }}
                                className="absolute -top-2 sm:-top-3 left-2 sm:left-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 sm:px-3 py-1 rounded-full shadow-md"
                              >
                                Most Popular
                              </motion.div>
                            )}

                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-3 sm:space-y-0">
                              <div className="flex-1">
                                {/* Tier Header */}
                                <div className="flex items-center mb-2">
                                  <h3 className="font-bold text-gray-900 dark:text-orange-200 text-base sm:text-lg mr-2 sm:mr-3">{tier.name}</h3>
                                  {tier.isRecommended && (
                                    <motion.span 
                                      initial={{ opacity: 0, scale: 0.8 }}
                                      animate={{ opacity: 1, scale: 1 }}
                                      transition={{ delay: 0.6 + index * 0.1 }}
                                      className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium px-2 py-1 rounded-full"
                                    >
                                      Recommended
                                    </motion.span>
                                  )}
                                </div>

                                {/* Tier Description */}
                          {tier.description && (
                                  <p className="text-xs sm:text-sm text-gray-600 dark:text-amber-200 leading-relaxed mb-2 sm:mb-3">{tier.description}</p>
                                )}

                                {/* Tier Features */}
                                {tier.features && tier.features.length > 0 && (
                                  <div className="space-y-1 mb-2 sm:mb-3">
                                    {tier.features.map((feature, featureIndex) => (
                                      <motion.div 
                                        key={featureIndex} 
                                        className="flex items-center text-xs sm:text-sm text-gray-600"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.7 + index * 0.1 + featureIndex * 0.05 }}
                                      >
                                        <svg className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-1 sm:mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        {feature}
                                      </motion.div>
                                    ))}
                        </div>
                                )}

                                {/* Order Count */}
                                {tier.orderCount > 0 && (
                                  <motion.div 
                                    className="flex items-center text-xs text-gray-500"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.8 + index * 0.1 }}
                                  >
                                    <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    {tier.orderCount} {tier.orderCount === 1 ? 'person' : 'people'} ordered this tier
                                  </motion.div>
                                )}
                        </div>

                              {/* Price Section */}
                              <div className="text-center sm:text-right sm:ml-4 lg:ml-6 flex-shrink-0">
                                <motion.div 
                                  className="mb-1"
                                  initial={{ opacity: 0, scale: 0.8 }}
                                  animate={{ opacity: 1, scale: 1 }}
                                  transition={{ delay: 0.9 + index * 0.1, type: "spring" }}
                                >
                                  <div className="text-2xl sm:text-3xl font-bold text-indigo-600 dark:text-orange-200">
                                    {tier.price}
                      </div>
                                  <div className="text-xs sm:text-sm text-gray-500 dark:text-amber-200 font-medium">
                                    per {tier.unit || 'round'}
                    </div>
                                </motion.div>

                                {/* Estimated Time */}
                                {tier.estimatedTime && (
                                  <motion.div 
                                    className="text-xs font-semibold text-gray-500 text-left bg-gray-100 rounded-full px-2 py-1 mt-2"
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 1.0 + index * 0.1 }}
                                  >
                                    ~{tier.estimatedTime}
                                  </motion.div>
                                )}

                                {/* Select Button */}
                                <motion.button
                                  variants={buttonVariants}
                                  whileHover="hover"
                                  whileTap="tap"
                                  className={`mt-2 sm:mt-3 px-3 sm:px-4 py-2 sm:py-2 rounded-lg text-xs sm:text-sm font-semibold transition-all duration-200 w-full sm:w-auto ${
                                    selectedTier && selectedTier.id === tier.id
                                      ? 'bg-indigo-600 dark:bg-amber-200 text-white dark:text-black shadow-md'
                                      : 'bg-gray-100 dark:bg-indigo-600 text-gray-700 dark:text-white hover:bg-indigo-100 hover:text-indigo-700'
                                  }`}
                                >
                                  {selectedTier && selectedTier.id === tier.id
                                    ? t('modals.serviceSelection.buttons.selected', 'Selected')
                                    : t('modals.serviceSelection.buttons.select', 'Select')}
                                </motion.button>
                              </div>
                            </div>

                            {/* Hover Effect Overlay */}
                            <div className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${
                              selectedTier && selectedTier.id === tier.id
                                ? 'bg-gradient-to-r from-indigo-500/5 to-blue-500/5 opacity-100'
                                : 'bg-gradient-to-r from-indigo-500/0 to-blue-500/0 opacity-0 group-hover:opacity-100'
                            }`} />
                          </motion.div>
                        </motion.div>
                      ))}

                      {/* Empty State */}
                  {(!skill.tiers || skill.tiers.length === 0) && (
                        <motion.div
                          initial={{ opacity: 0, y: 30, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          transition={{ duration: 0.4, type: "spring" }}
                          className="text-center py-8 sm:py-12 bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl border-dashed"
                        >
                          <svg className="w-12 h-12 sm:w-16 sm:h-16 text-gray-300 mx-auto mb-3 sm:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">{t('modals.serviceSelection.messages.noTiers', 'No Service Tiers Available')}</h3>
                          <p className="text-sm text-gray-500">{t('modals.serviceSelection.messages.noTiersDesc', "This talent hasn't set up any service tiers yet.")}</p>
                        </motion.div>
                      )}
                    </div>

                    {/* Validation Feedback */}
                    {!selectedTier && (
                      <motion.div
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ delay: 0.5, duration: 0.4 }}
                        className="mt-6 bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl p-6"
                      >
                        <div className="flex">
                          <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <div className="ml-2 sm:ml-3">
                            <h3 className="text-xs sm:text-sm font-medium text-yellow-800 dark:text-amber-200">{t('modals.serviceSelection.messages.selectTier', 'Select a service tier')}</h3>
                            <p className="text-xs sm:text-sm text-yellow-700 dark:text-amber-200 mt-1">
                              {t('modals.serviceSelection.messages.selectTierDesc', 'Please choose a service tier to continue with your order.')}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Selected Tier Summary */}
                    {selectedTier && (
                      <motion.div
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ delay: 0.6, duration: 0.4, type: "spring" }}
                        className="mt-6 bg-green-50 dark:bg-gray-900 border border-green-200 dark:border-amber-200 rounded-lg p-6"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <div className="ml-2 sm:ml-3">
                              <h3 className="text-xs sm:text-sm text-left font-medium text-green-800 dark:text-amber-200">{t('modals.serviceSelection.messages.tierSelected', 'Service tier selected')}</h3>
                              <p className="text-xs sm:text-sm text-left text-green-700 dark:text-amber-200">
                                {selectedTier.name} - {selectedTier.price} credits per {selectedTier.unit || 'round'}
                              </p>
                </div>
              </div>
                          <motion.button
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                            onClick={() => setSelectedTier(null)}
                            className="text-xs sm:text-sm text-green-600 dark:text-green-50 hover:rounded-2xl hover:bg-gray-600 bg-transparent hover:bg-green-50 hover:text-green-800 font-medium touch-manipulation"
                          >
                            {t('modals.serviceSelection.buttons.change', 'Change')}
                          </motion.button>
            </div>
                      </motion.div>
                    )}
                  </motion.div>
                )}

                {/* Step 2: Scheduling */}
                {currentStep === 2 && (
                  <motion.div
                    key="step2"
                    variants={stepVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ duration: 0.4 }}
                    drag={isMobile() ? "x" : false}
                    dragConstraints={{ left: 0, right: 0 }}
                    dragElastic={0.2}
                    onDragEnd={handleStepDragEnd}
                  >
                    <h2 className="text-2xl font-extrabold text-gray-900 dark:text-white mb-6 flex items-center">
                      <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-full mr-3"></span>
                      {t('modals.serviceSelection.titles.step2', 'Choose Order Type')}
                    </h2>

                    {/* Order Type Selection */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      {/* Order Now Card */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                        className={`relative group cursor-pointer transition-all duration-300 ${
                          orderType === 'now' ? 'transform scale-[1.02]' : 'hover:scale-[1.01]'
                        }`}
                      >
                        {/* Selection Indicator */}
                        {orderType === 'now' && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 dark:from-yellow-400 dark:to-pink-400 rounded-full flex items-center justify-center shadow-lg z-10"
                          >
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                            </svg>
                          </motion.div>
                        )}

                        <div
                          className={`relative border-2 rounded-xl p-6 transition-all duration-300 ${
                            orderType === 'now'
                              ? 'border-green-500 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-yellow-400 dark:to-pink-400 shadow-lg'
                              : 'border-gray-200 hover:border-green-300 hover:shadow-md bg-white'
                          }`}
                          onClick={() => handleOrderTypeSelect('now')}
                        >
                          {/* Icon and Header */}
                          <div className="flex items-center mb-4">
                            <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 transition-all duration-300 ${
                              orderType === 'now' 
                                ? 'bg-gradient-to-r from-green-500 to-emerald-500 dark:from-yellow-400 dark:to-pink-400 shadow-lg' 
                                : 'bg-green-100 dark:bg-gray-900'
                            }`}>
                              <svg className={`w-6 h-6 transition-all duration-300 ${
                                orderType === 'now' ? 'text-white' : 'text-green-600'
                              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-xl text-left font-bold text-gray-900 dark:text-black">Order Now</h3>
                              <p className="text-sm text-left text-gray-500 dark:text-black">Immediate service</p>
                            </div>
                          </div>

                          {/* Description */}
                          <p className="text-gray-600 dark:text-black text-left mb-4 pb-4 pt-4 leading-relaxed">
                            Get immediate service. The talent will respond within 60 seconds and start working right away.
                          </p>

                          {/* Features */}
                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm text-gray-600 dark:text-black">
                              <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              Instant response (60 seconds)
                            </div>
                            <div className="flex items-center text-sm text-gray-600 dark:text-black">
                              <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              Immediate service start
                            </div>
                            <div className="flex items-center text-sm text-gray-600 dark:text-black">
                              <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              No scheduling needed
                            </div>
                          </div>

                          {/* Select Button */}
                          <motion.button
                            className={`w-full px-4 py-3 rounded-lg text-sm font-semibold transition-all duration-200 ${
                              orderType === 'now'
                                ? 'bg-green-600 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-green-100 hover:text-green-700'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {orderType === 'now'
                              ? t('modals.serviceSelection.buttons.selected', 'Selected')
                              : t('modals.serviceSelection.buttons.chooseOrderNow', 'Choose Order Now')}
                          </motion.button>

                          {/* Hover Effect Overlay */}
                          <div className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${
                            orderType === 'now'
                              ? 'bg-gradient-to-r from-green-500/5 to-emerald-500/5 opacity-100'
                              : 'bg-gradient-to-r from-green-500/0 to-emerald-500/0 opacity-0 group-hover:opacity-100'
                          }`} />
                        </div>
                      </motion.div>

                      {/* Schedule Later Card */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className={`relative group cursor-pointer transition-all duration-300 ${
                          orderType === 'later' ? 'transform scale-[1.02]' : 'hover:scale-[1.01]'
                        }`}
                      >
                        {/* Selection Indicator */}
                        {orderType === 'later' && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg z-10"
                          >
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                            </svg>
                          </motion.div>
                        )}

                        <div
                          className={`relative border-2 rounded-xl p-6 transition-all duration-300 ${
                            orderType === 'later'
                              ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg'
                              : 'border-gray-200 hover:border-blue-300 hover:shadow-md bg-white'
                          }`}
                          onClick={() => handleOrderTypeSelect('later')}
                        >
                          {/* Icon and Header */}
                          <div className="flex items-center mb-4">
                            <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 transition-all duration-300 ${
                              orderType === 'later' 
                                ? 'bg-gradient-to-r from-blue-500 to-indigo-500 shadow-lg' 
                                : 'bg-blue-100'
                            }`}>
                              <svg className={`w-6 h-6 transition-all duration-300 ${
                                orderType === 'later' ? 'text-white' : 'text-blue-600'
                              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-xl text-left font-bold text-gray-900">Schedule Later</h3>
                              <p className="text-sm text-left text-gray-500">Book for later</p>
                            </div>
                          </div>

                          {/* Description */}
                          <p className="text-gray-600 text-left mb-4 pb-4 pt-4 leading-relaxed">
                            Book for a specific date and time that works best for you. Perfect for planning ahead.
                          </p>

                          {/* Features */}
                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm text-gray-600">
                              <svg className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              Choose your preferred time
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <svg className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              No rush, plan ahead
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <svg className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              Guaranteed availability
                            </div>
                          </div>

                          {/* Select Button */}
                          <motion.button
                            className={`w-full px-4 py-3 rounded-lg text-sm font-semibold transition-all duration-200 ${
                              orderType === 'later'
                                ? 'bg-blue-600 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {orderType === 'later'
                              ? t('modals.serviceSelection.buttons.selected', 'Selected')
                              : t('modals.serviceSelection.buttons.chooseScheduleLater', 'Choose Schedule Later')}
                          </motion.button>

                          {/* Hover Effect Overlay */}
                          <div className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${
                            orderType === 'later'
                              ? 'bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-100'
                              : 'bg-gradient-to-r from-blue-500/0 to-indigo-500/0 opacity-0 group-hover:opacity-100'
                          }`} />
                        </div>
                      </motion.div>
                    </div>

                    {/* Date/Time Picker for Scheduled Orders */}
                    <AnimatePresence>
                      {orderType === 'later' && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="border-t border-gray-200 pt-6"
                        >
                          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <span className="w-1 h-6 bg-gradient-to-b from-blue-500 to-indigo-500 rounded-full mr-3"></span>
                            {t('modals.serviceSelection.titles.selectDateTime', 'Select Date & Time')}
                          </h3>

                          {/* Calendar + Slot Picker */}
                          <ScheduleDateTimePicker
                            availabilityData={availabilityData}
                            specialDates={specialDates}
                            minDate={new Date()}
                            onSelect={handleDateTimeSelect}
                          />

                          {/* Selected DateTime Display */}
                          {scheduledDateTime && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="bg-white border border-blue-200 rounded-lg p-4"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <svg className="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">
                                      Scheduled for {formatScheduledDate(scheduledDateTime)}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      at {formatScheduledTime(scheduledDateTime)}
                                    </p>
                                  </div>
                                </div>
              <button
                                  onClick={() => setScheduledDateTime(null)}
                                  className="text-sm text-blue-600 bg-transparent hover:text-blue-800 font-medium"
              >
                                  {t('modals.serviceSelection.buttons.change', 'Change')}
              </button>
                              </div>
                            </motion.div>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Validation Feedback */}
                    {orderType === 'later' && !scheduledDateTime && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4"
                      >
                        <div className="flex">
                          <svg className="w-5 h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-yellow-800">{t('modals.serviceSelection.messages.selectDateTime', 'Select a date and time')}</h3>
                            <p className="text-sm text-yellow-700 mt-1">
                              {t('modals.serviceSelection.messages.chooseWhen', "Please choose when you'd like your service to be scheduled.")}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Order Type Summary */}
                    {orderType && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-6 bg-green-50 dark:bg-black border border-green-200 dark:border-amber-200 border-2 rounded-lg p-6"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <div className="ml-3">
                              <h3 className="text-sm text-left font-medium text-green-800">{t('modals.serviceSelection.messages.orderTypeSelected', 'Order type selected')}</h3>
                              <p className="text-sm text-left text-green-700">
                                {orderType === 'now'
                                  ? t('modals.serviceSelection.messages.immediateService', 'Immediate service')
                                  : t('modals.serviceSelection.messages.scheduledService', 'Scheduled service')}
                                {orderType === 'later' && scheduledDateTime && (
                                  <span> for {formatScheduledDate(scheduledDateTime)} at {formatScheduledTime(scheduledDateTime)}</span>
                                )}
                              </p>
                            </div>
                          </div>
              <button
                            onClick={() => {
                              setOrderType('now');
                              setScheduledDateTime(null);
                            }}
                            className="text-sm text-green-600 dark:text-green-200 hover:rounded-2xl hover:bg-gray-600 bg-transparent hover:bg-transparent hover:text-green-800 font-medium"
                          >
                            {t('modals.serviceSelection.buttons.change', 'Change')}
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                )}

                {/* Step 3: Order Configuration */}
                {currentStep === 3 && (
                  <motion.div
                    key="step3"
                    variants={stepVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ duration: 0.4 }}
                    drag={isMobile() ? "x" : false}
                    dragConstraints={{ left: 0, right: 0 }}
                    dragElastic={0.2}
                    onDragEnd={handleStepDragEnd}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                    style={{ touchAction: 'pan-y' }}
                  >
                    {/* Pull-to-refresh indicator */}
                    {isMobile() && (
                      <motion.div
                        style={{ height: pullDistance, opacity: pullDistance > 0 ? 1 : 0, background: '#e0f2fe', borderRadius: 8, marginBottom: 8, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                        animate={{ height: isRefreshing ? 44 : pullDistance }}
                      >
                        {isRefreshing ? (
                          <svg className="animate-spin w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                        ) : (
                          <span className="text-xs text-blue-500">{t('modals.serviceSelection.messages.pullToRefresh', 'Pull to refresh wallet')}</span>
                        )}
                      </motion.div>
                    )}
                    <h2 className="text-2xl font-extrabold text-gray-900 dark:text-white mb-6 flex items-center">
                      <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-full mr-3"></span>
                      {t('modals.serviceSelection.titles.step3', 'Configure Your Order')}
                    </h2>

                    {/* Enhanced Order Summary */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl p-8 mb-8"
                    >
                      <div className="flex items-center mb-4 pb-4">
                        <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-extrabold text-gray-900 dark:text-white">{t('modals.serviceSelection.titles.orderSummary', 'Order Summary')}</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Service Details */}
                        <div className="space-y-3">
                          <div className="flex justify-between items-start">
                            <span className="text-sm font-medium text-gray-600 dark:text-indigo-100">Service:</span>
                            <span className="text-sm font-semibold text-gray-900 dark:text-indigo-100 text-right">
                              {skill.name}
                              <br />
                              <span className="text-indigo-600">{selectedTier?.name}</span>
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-600 dark:text-gray-100">Order Type:</span>
                            <span className="text-sm font-semibold text-gray-900 dark:text-indigo-100">
                              {orderType === 'now' ? (
                                <span className="flex items-center text-green-600 dark:text-green-100">
                                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                  </svg>
                                  Immediate
                                </span>
                              ) : (
                                <span className="flex items-center text-blue-600 dark:text-blue-100">
                                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                  Scheduled
                                </span>
                              )}
                            </span>
                          </div>
                          {orderType === 'later' && scheduledDateTime && (
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-gray-600 dark:text-indigo-100">Scheduled for:</span>
                              <span className="text-sm font-semibold text-gray-900 dark:text-indigo-100 text-right">
                                {formatScheduledDate(scheduledDateTime)}
                                <br />
                                {formatScheduledTime(scheduledDateTime)}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Pricing Details */}
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-600 dark:text-indigo-100">{t('modals.serviceSelection.messages.pricePerUnit', 'Price per unit:')}</span>
                            <span className="text-lg font-bold text-indigo-700 dark:text-indigo-200">
                              {selectedTier?.price} credits
                            </span>
                          </div>
                          <div className="flex justify-between pt-6 items-center">
                            <span className="text-sm font-medium text-gray-600 dark:text-indigo-100">{t('modals.serviceSelection.messages.quantityLabel', 'Quantity:')}</span>
                            <span className="text-sm font-semibold text-gray-900 dark:text-indigo-100">{quantity}</span>
                          </div>
                          <div className="border-t border-indigo-200 pt-3">
                            <div className="flex justify-between items-center">
                              <span className="text-base font-semibold text-gray-900 dark:text-indigo-100">{t('modals.serviceSelection.messages.totalAmount', 'Total Amount:')}</span>
                              <span className="text-xl font-bold text-indigo-600 dark:text-indigo-100">{totalPrice} credits</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    {/* Enhanced Quantity Selection */}
                    <div className="mb-6" data-field="quantity">
                      <label className="block text-base font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                        <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-full mr-3"></span>
                        {t('modals.serviceSelection.messages.quantity', 'Quantity')}
                      </label>
                      
                      <div className={`bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl p-4 transition-all duration-200 ${
                        validationErrors.quantity ? 'border-red-300 bg-red-50' : ''
                      }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            {/* Decrease Button */}
                            <motion.button
                type="button"
                              onClick={() => handleQuantityChange(quantity - 1)}
                              onPointerDown={() => handleQuantityLongPress(-1)}
                              onPointerUp={clearQuantityLongPress}
                              onPointerLeave={clearQuantityLongPress}
                              onTouchEnd={clearQuantityLongPress}
                              disabled={quantity <= 1}
                              className="w-12 h-12 min-w-[44px] min-h-[44px] border-2 border-gray-300 rounded-xl flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 touch-manipulation select-none"
                              whileHover={{ scale: quantity > 1 ? 1.08 : 1 }}
                              whileTap={{ scale: quantity > 1 ? 0.92 : 1 }}
                              aria-label="Decrease quantity"
                              tabIndex={0}
                            >
                              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4" />
                              </svg>
                            </motion.button>

                            {/* Quantity Display */}
                            <div className="text-center">
                              <div className="text-3xl font-bold text-gray-900 dark:text-indigo-100 mb-1">{quantity}</div>
                              <div className="text-sm text-gray-500 dark:text-indigo-100">
                                {selectedTier?.unit || 'round'}{quantity > 1 ? 's' : ''}
                              </div>
                            </div>

                            {/* Increase Button */}
                            <motion.button
                              type="button"
                              onClick={() => handleQuantityChange(quantity + 1)}
                              onPointerDown={() => handleQuantityLongPress(1)}
                              onPointerUp={clearQuantityLongPress}
                              onPointerLeave={clearQuantityLongPress}
                              onTouchEnd={clearQuantityLongPress}
                              disabled={quantity >= 99}
                              className="w-12 h-12 min-w-[44px] min-h-[44px] border-2 border-gray-300 rounded-xl flex items-center dark:text-black justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 touch-manipulation select-none"
                              whileHover={{ scale: quantity < 99 ? 1.08 : 1 }}
                              whileTap={{ scale: quantity < 99 ? 0.92 : 1 }}
                              aria-label="Increase quantity"
                              tabIndex={0}
                            >
                              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                            </motion.button>
                          </div>

                          {/* Manual Input */}
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500 dark:text-indigo-100">{t('modals.serviceSelection.messages.orEnter', 'Or enter:')}</span>
                            <input
                              type="number"
                              min="1"
                              max="99"
                              value={quantity}
                              onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                              className={`w-20 text-center border border-gray-300 dark:border-indigo-100 dark:bg-gray-900 rounded-lg px-3 py-3 sm:py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base sm:text-sm font-medium min-h-[44px] touch-manipulation select-none ${
                                validationErrors.quantity ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''
                              }`}
                              inputMode="numeric"
                              pattern="[0-9]*"
                              autoComplete="off"
                              aria-label="Quantity input"
                              tabIndex={0}
                            />
                          </div>
                        </div>

                        {/* Quantity Info */}
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500 dark:text-indigo-100">Price per {selectedTier?.unit || 'round'}:</span>
                            <span className="font-semibold text-indigo-600 dark:text-indigo-100">{selectedTier?.price} credits</span>
                          </div>
                          <div className="flex items-center justify-between text-sm mt-1">
                            <span className="text-gray-500 dark:text-indigo-100">Total:</span>
                            <span className="font-bold text-lg text-indigo-600 dark:text-indigo-100">{totalPrice} credits</span>
                          </div>
                        </div>

                        {/* Quantity Validation Error */}
                        {validationErrors.quantity && (
                          <motion.div
                            initial={{ opacity: 0, y: -5 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-3 flex items-center text-sm text-red-600"
                          >
                            <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {validationErrors.quantity}
                          </motion.div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Remarks Field */}
                    <div className="mb-6" data-field="remarks">
                      <label className="block text-base font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                        <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-full mr-3"></span>
                        {t('modals.serviceSelection.messages.remarks', 'Remarks (Optional)')}
                      </label>
                      
                      <div className="relative">
                        <textarea
                          ref={remarksRef}
                          onFocus={handleRemarksFocus}
                          enterKeyHint="done"
                          value={remarks}
                          onChange={handleRemarksChange}
                          placeholder={t('modals.serviceSelection.messages.remarksPlaceholder', 'Add any special instructions, requirements, or notes for the talent...')}
                          rows="4"
                          className={`w-full border-2 rounded-xl dark:bg-gray-900 px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none transition-all duration-200 text-base sm:text-sm min-h-[44px] touch-manipulation select-none0${
                            validationErrors.remarks ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                          }`}
                          maxLength={500}
                          inputMode="text"
                          autoComplete="on"
                          aria-label="Order remarks"
                          tabIndex={0}
                        />
                        
                        {/* Character Counter */}
                        <div className="absolute bottom-3 right-3">
                          <div className={`text-xs px-2 py-1 rounded-full ${
                            validationErrors.remarks
                              ? 'bg-red-100 text-red-600'
                              : remarks.length > 450 
                                ? 'bg-red-100 text-red-600' 
                                : remarks.length > 400 
                                  ? 'bg-yellow-100 text-yellow-600'
                                  : 'bg-gray-100 text-gray-500'
                          }`}>
                            {remarks.length}/500
                          </div>
                        </div>
                      </div>

                      {/* Remarks Help Text */}
                      <div className="mt-2 text-sm text-gray-500 dark:text-gray-100">
                        <p>{t('modals.serviceSelection.messages.remarksTip', '💡 Tip: Include specific details like game mode, rank requirements, or special requests to help the talent deliver exactly what you need.')}</p>
                      </div>

                      {/* Remarks Validation Error */}
                      {validationErrors.remarks && (
                        <motion.div
                          initial={{ opacity: 0, y: -5 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-3 flex items-center text-sm text-red-600"
                        >
                          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {validationErrors.remarks}
                        </motion.div>
                      )}
                    </div>

                    {/* Enhanced Wallet Balance & Total */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`rounded-2xl p-6 border-2 transition-all duration-300 bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border-indigo-100 dark:border-gray-800 ${
                        hasSufficientBalance
                          ? 'ring-1 ring-green-200'
                          : 'ring-1 ring-red-200'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                            hasSufficientBalance ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {hasSufficientBalance ? (
                              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                              </svg>
                            ) : (
                              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            )}
                          </div>
                          <div>
                            <h3 className="font-semibold text-left text-gray-900 dark:text-indigo-100">{t('modals.serviceSelection.titles.walletBalance', 'Wallet Balance')}</h3>
                            <p className={`text-sm ${hasSufficientBalance ? 'text-green-600' : 'text-red-600'}`}>
                              {t('modals.serviceSelection.messages.creditsAvailable', '{{balance}} credits available', { balance: walletBalance })}
                            </p>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {totalPrice} credits
                          </div>
                          <div className="text-sm text-gray-500 dark:text-indigo-100 font-bold">{t('modals.serviceSelection.titles.totalCost', 'Total Cost')}</div>
                        </div>
                      </div>

                      {/* Balance Status */}
                      {hasSufficientBalance ? (
                        <div className="flex items-center text-green-700 dark:text-indigo-100">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm font-medium">{t('modals.serviceSelection.messages.sufficientBalance', 'Sufficient balance available')}</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-red-700">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-sm font-medium">
                            {t('modals.serviceSelection.messages.insufficientBalanceNeed', 'Insufficient balance. You need {{amount}} more credits.', { amount: totalPrice - walletBalance })}
                          </span>
                        </div>
                      )}

                      {/* Remaining Balance */}
                      {hasSufficientBalance && (
                        <div className="mt-3 pt-3 border-t border-green-200">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-indigo-100">{t('modals.serviceSelection.messages.remainingAfterPurchase', 'Remaining after purchase:')}</span>
                            <span className="font-semibold text-green-600 dark:text-indigo-100">{walletBalance - totalPrice} credits</span>
                          </div>
                        </div>
                      )}
                    </motion.div>

                    {/* Validation Summary */}
                    {!hasSufficientBalance && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-6 bg-red-50 border border-red-200 rounded-lg p-6"
                      >
                        <div className="flex">
                          <svg className="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">{t('modals.serviceSelection.messages.insufficientBalance', 'Insufficient balance')}</h3>
                            <p className="text-sm text-red-700 mt-1">
                              {t('modals.serviceSelection.messages.addCredits', 'Please add more credits to your wallet to complete this order.')}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Quick Add Quantity (mobile/tablet) */}
                    {(isMobile() || (typeof window !== 'undefined' && window.innerWidth < 1024)) && (
                      <div className="flex gap-2 mb-4">
                        {quickAddOptions.map((opt) => (
                          <button
                            key={opt}
                            type="button"
                            onClick={() => handleQuickAdd(opt)}
                            className="px-3 py-2 rounded-lg bg-indigo-100 text-indigo-700 font-semibold text-xs shadow-sm active:bg-indigo-200 touch-manipulation min-w-[44px]"
                          >
                            +{opt}
              </button>
                        ))}
                      </div>
                    )}

                    {/* Paste from clipboard for remarks (mobile/tablet) */}
                    {(isMobile() || (typeof window !== 'undefined' && window.innerWidth < 1024)) && (
              <button
                type="button"
                        onClick={handlePasteFromClipboard}
                        className="mt-2 px-3 py-2 rounded-lg bg-blue-100 text-blue-700 font-semibold text-xs shadow-sm active:bg-blue-200 touch-manipulation min-w-[44px]"
              >
                        {t('modals.serviceSelection.buttons.pasteFromClipboard', 'Paste from clipboard')}
              </button>
                    )}
                  </motion.div>
                )}

                {/* Step 4: Confirmation */}
                {currentStep === 4 && (
                  <motion.div
                    key="step4"
                    variants={stepVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ duration: 0.4 }}
                    drag={isMobile() ? "x" : false}
                    dragConstraints={{ left: 0, right: 0 }}
                    dragElastic={0.2}
                    onDragEnd={handleStepDragEnd}
                  >
                    {orderSuccess ? (
                      <div className="text-center py-8">
                        {/* Success Animation */}
                        <motion.div
                          variants={successVariants}
                          initial="hidden"
                          animate="visible"
                          className="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
                        >
                          <motion.svg 
                            className="w-10 h-10 text-white" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                            initial={{ pathLength: 0 }}
                            animate={{ pathLength: 1 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                          >
                            <path 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth="3" 
                              d="M5 13l4 4L19 7" 
                            />
                          </motion.svg>
                        </motion.div>

                        {/* Success Message */}
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.3, duration: 0.5 }}
                        >
                          <motion.h2 
                            className="text-3xl font-bold text-gray-900 dark:text-indigo-100 mb-3"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.4, duration: 0.4, type: "spring" }}
                          >
                            {t('modals.serviceSelection.messages.orderPlaced', 'Order Placed Successfully!')}
                          </motion.h2>
                          <motion.p 
                            className="text-lg text-gray-600 dark:text-amber-200 mb-8 leading-relaxed"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5, duration: 0.4 }}
                          >
                            {t('modals.serviceSelection.messages.orderPlacedDesc', "Your order has been created and is waiting for the talent to respond.\nYou'll receive notifications about your order status.")}
                          </motion.p>
                        </motion.div>

                        {/* Enhanced Order Details */}
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.6, duration: 0.5 }}
                          className="bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-yello-500 border-2 rounded-2xl p-8 mb-8"
                        >
                          <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                            <h3 className="text-lg font-extrabold text-gray-900 dark:text-amber-200">{t('modals.serviceSelection.titles.orderDetails', 'Order Details')}</h3>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="space-y-3">
                              <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-amber-200 font-medium">Order ID:</span>
                                <span className="font-semibold text-gray-900 dark:text-amber-200">#{createdOrder?.id}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-amber-200 font-medium">{t('modals.serviceSelection.messages.serviceLabel', 'Service:')}</span>
                                <span className="font-semibold text-gray-900 dark:text-amber-200 text-right">
                                  {skill.name}
                                  <br />
                                  <span className="text-indigo-600 dark:text-amber-200">{selectedTier?.name}</span>
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-amber-200 font-medium">{t('modals.serviceSelection.messages.quantityLabel', 'Quantity:')}</span>
                                <span className="font-semibold text-gray-900 dark:text-amber-200">{quantity}</span>
                              </div>
                            </div>
                            <div className="space-y-3">
                              <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-yellow-500 font-medium">{t('modals.serviceSelection.messages.orderType', 'Order Type:')}</span>
                                <span className="font-semibold text-gray-900 dark:text-yellow-500">
                                  {orderType === 'now' ? (
                                    <span className="flex items-center text-green-600 dark:text-green-100">
                                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                      </svg>
                                      {t('modals.serviceSelection.messages.immediate', 'Immediate')}
                                    </span>
                                  ) : (
                                    <span className="flex items-center text-blue-600 dark:text-blue-100">
                                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                      {t('modals.serviceSelection.messages.scheduled', 'Scheduled')}
                                    </span>
                                  )}
                                </span>
                              </div>
                              {orderType === 'later' && scheduledDateTime && (
                                <div className="flex justify-between">
                                  <span className="text-gray-600 dark:text-amber-200 font-medium">{t('modals.serviceSelection.messages.scheduledFor', 'Scheduled for:')}</span>
                                  <span className="font-semibold text-gray-900 dark:text-amber-200 text-right">
                                    {formatScheduledDate(scheduledDateTime)}
                                    <br />
                                    {formatScheduledTime(scheduledDateTime)}
                                  </span>
                                </div>
                              )}
                              <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-amber-200 font-medium">{t('modals.serviceSelection.messages.totalAmount', 'Total Amount:')}</span>
                                <span className="font-bold text-lg text-indigo-600 dark:text-amber-200">{totalPrice} credits</span>
                              </div>
                            </div>
                          </div>

                          {/* Order Status */}
                          <div className="mt-4 pt-4 border-t border-green-200">
                            <div className="flex items-center justify-between">
                              <span className="text-gray-600 dark:text-amber-200 font-medium">{t('modals.serviceSelection.messages.status', 'Status:')}</span>
                              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {t('modals.serviceSelection.messages.pendingResponse', 'Pending Response')}
                              </span>
                            </div>
            </div>
          </motion.div>

                        {/* Next Steps */}
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7, duration: 0.5 }}
                          className="bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-yellow-500 border-2 rounded-2xl p-6 mb-6"
                        >
                          <div className="flex items-center mb-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-amber-400">{t('modals.serviceSelection.messages.nextSteps', 'What happens next?')}</h3>
                          </div>
                          <div className="space-y-2 text-sm text-gray-700 dark:text-amber-200">
                            <div className="flex items-start">
                              <span className="w-2 h-2 bg-amber-200 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                              <span>{t('modals.serviceSelection.messages.nextStepReview', 'The talent will review your order and respond within {{time}}', { time: orderType === 'now' ? '60 seconds' : '24 hours' })}</span>
                            </div>
                            <div className="flex items-start">
                              <span className="w-2 h-2 bg-amber-200 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                              <span>{t('modals.serviceSelection.messages.nextStepNotifications', "You'll receive notifications about order status updates")}</span>
                            </div>
                            <div className="flex items-start">
                              <span className="w-2 h-2 bg-amber-200 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                              <span>{t('modals.serviceSelection.messages.nextStepTrack', 'Track your order progress in your dashboard')}</span>
                            </div>
                          </div>
                        </motion.div>

                        {/* Action Buttons */}
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.8, duration: 0.5 }}
                          className="flex flex-col sm:flex-row gap-3 justify-center"
                        >
                          <motion.button
                            onClick={handleMessageTalent}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                            className="px-6 py-3 border border-gray-300 text-gray-700 dark:text-black bg-amber-200 rounded-xl text-sm font-semibold hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                          >
                            <span className="flex items-center">
                              <motion.svg 
                                className="w-4 h-4 mr-2" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 1.0, type: "spring" }}
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2h14a2 2 0 002-2V8z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 8l-9 6-9-6" />
                              </motion.svg>
                              {t('modals.serviceSelection.buttons.messageTalent', 'Message Talent')}
                            </span>
                          </motion.button>
                        </motion.div>
                      </div>
                    ) : (
                      <div>
                        <h2 className="text-2xl font-extrabold text-gray-900 dark:text-white mb-6 flex items-center">
                          <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-full mr-3"></span>
                          {t('modals.serviceSelection.titles.step4', 'Confirm Your Order')}
                        </h2>
                        
                        {/* Enhanced Final Review */}
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl p-8 mb-8"
                        >
                          <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                              <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                            <h3 className="text-lg font-extrabold text-gray-900 dark:text-white">Final Review</h3>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Service Details */}
                            <div className="space-y-3">
                              <div className="flex justify-between items-start">
                                <span className="text-sm font-medium text-gray-600">Service:</span>
                                <span className="text-sm font-semibold text-gray-900 text-right">
                                  {skill.name}
                                  <br />
                                  <span className="text-indigo-600">{selectedTier?.name}</span>
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-600">Quantity:</span>
                                <span className="text-sm font-semibold text-gray-900">{quantity}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-600">Order Type:</span>
                                <span className="text-sm font-semibold text-gray-900">
                                  {orderType === 'now' ? (
                                    <span className="flex items-center text-green-600 dark:text-green-100">
                                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                      </svg>
                                      Immediate
                                    </span>
                                  ) : (
                                    <span className="flex items-center text-blue-600 dark:text-blue-100">
                                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                      Scheduled
                                    </span>
                                  )}
                                </span>
                              </div>
                              {orderType === 'later' && scheduledDateTime && (
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium text-gray-600">Scheduled for:</span>
                                  <span className="text-sm font-semibold text-gray-900 text-right">
                                    {formatScheduledDate(scheduledDateTime)}
                                    <br />
                                    {formatScheduledTime(scheduledDateTime)}
                                  </span>
                                </div>
                              )}
                            </div>

                            {/* Pricing Details */}
                            <div className="space-y-3">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-600">Price per unit:</span>
                                <span className="text-lg font-bold text-indigo-700 dark:text-indigo-200">{selectedTier?.price} credits</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-600">Wallet Balance:</span>
                                <span className="text-sm font-semibold text-gray-900">{walletBalance} credits</span>
                              </div>
                              <div className="border-t border-indigo-200 pt-3">
                                <div className="flex justify-between items-center">
                                  <span className="text-base font-semibold text-gray-900">Total Amount:</span>
                                  <span className="text-xl font-bold text-indigo-600">{totalPrice} credits</span>
                                </div>
                                <div className="flex justify-between items-center mt-1">
                                  <span className="text-sm text-gray-500 dark:text-gray-300">{t('modals.serviceSelection.messages.remainingAfterPurchase', 'Remaining after purchase:')}</span>
                                  <span className="text-sm font-semibold text-green-600">{walletBalance - totalPrice} credits</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Remarks Display */}
                          {remarks && (
                            <div className="mt-4 pt-4 border-t border-indigo-200">
                              <div className="flex items-start">
                                <span className="text-sm font-medium text-gray-600 mr-3">Remarks:</span>
                                <span className="text-sm text-gray-700 flex-1">{remarks}</span>
                              </div>
                            </div>
                          )}
                        </motion.div>

                        {/* Terms and Conditions */}
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl p-6 mb-6"
                        >
                          <div className="flex items-start">
                            <svg className="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <div>
                              <h3 className="text-sm font-medium text-yellow-800">Important Information</h3>
                              <p className="text-sm text-yellow-700 mt-1">
                                By placing this order, you agree to our terms of service. Credits will be deducted from your wallet immediately. 
                                {orderType === 'now' ? ' The talent will respond within 60 seconds.' : ' The talent will respond within 24 hours.'}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-6 bg-red-50 border border-red-200 rounded-lg p-6"
                >
                  <div className="flex">
                    <svg className="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-red-800">
                        {error.context === 'order_submission'
                          ? t('modals.serviceSelection.errors.orderSubmission', 'Order Submission Error')
                          : error.context === 'wallet_balance_fetch'
                            ? t('modals.serviceSelection.errors.walletError', 'Wallet Error')
                            : t('modals.serviceSelection.errors.error', 'Error')}
                      </h3>
                      <p className="text-sm text-red-700 mt-1">{error.message}</p>
                      <button
                        onClick={clearAllErrors}
                        className="text-sm text-red-600 hover:text-red-800 font-medium mt-2"
                      >
                        {t('modals.serviceSelection.buttons.dismiss', 'Dismiss')}
              </button>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Validation Errors Summary */}
              {Object.keys(validationErrors).length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-6 bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl p-6"
                >
                  <div className="flex">
                    <svg className="w-5 h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-yellow-800">{t('modals.serviceSelection.errors.fixIssues', 'Please fix the following issues:')}</h3>
                      <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                        {Object.entries(validationErrors).map(([field, message]) => (
                          <li key={field} className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-yellow-400 rounded-full mr-2"></span>
                            {message}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Loading State */}
              {(isLoading || isValidating || isSubmitting) && (
                <motion.div
                  variants={loadingVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6"
                >
                  <div className="flex items-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full mr-3"
                    />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {isSubmitting
                          ? t('modals.serviceSelection.buttons.processing', 'Processing your order...')
                          : isValidating
                            ? t('modals.serviceSelection.errors.validatingSelections', 'Validating your selections...')
                            : t('modals.serviceSelection.errors.loading', 'Loading...')}
                      </p>
                      <p className="text-sm text-blue-600">
                        {isSubmitting
                          ? t('modals.serviceSelection.errors.processing', 'Please wait while we process your payment and create your order.')
                          : isValidating
                            ? t('modals.serviceSelection.errors.checkingSelections', 'Checking all your selections...')
                            : t('modals.serviceSelection.errors.pleaseWait', 'Please wait...')}
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Footer with Action Buttons */}
            <div className="px-2 sm:px-6 lg:px-8 py-3 sm:py-6 bg-gray-50 dark:bg-gray-900 border-t border-gray-100 sticky bottom-0 left-0 right-0 z-10">
              <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
                {/* Back Button */}
                {currentStep > 1 && currentStep < 4 && (
                  <motion.button
                    type="button"
                    onClick={handlePrevStep}
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                    className="w-full sm:w-auto px-4 sm:px-6 py-3 sm:py-3 border border-gray-300 rounded-xl text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 touch-manipulation"
                  >
                    <span className="flex items-center justify-center sm:justify-start">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                      {t('modals.serviceSelection.buttons.back', 'Back')}
                    </span>
                  </motion.button>
                )}

                <div className="flex-1 sm:hidden"></div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row w-full sm:w-auto space-y-2 sm:space-y-0 sm:space-x-3">
                  {currentStep === 1 && (
                    <motion.button
                      type="button"
                      onClick={handleNextStep}
                      disabled={!canProceedToStep2}
                      variants={buttonVariants}
                      whileHover={canProceedToStep2 ? "hover" : undefined}
                      whileTap={canProceedToStep2 ? "tap" : undefined}
                      className="w-full sm:w-auto px-4 sm:px-6 py-3 sm:py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-full font-bold shadow-xl text-sm hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-60 disabled:cursor-not-allowed touch-manipulation"
                    >
                      <span className="flex items-center justify-center sm:justify-start">
                        <span>{t('modals.serviceSelection.buttons.continue', 'Continue')}</span>
                        <motion.svg 
                          className="w-4 h-4 sm:w-5 sm:h-5 ml-2" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                          initial={{ x: 0 }}
                          animate={{ x: canProceedToStep2 ? 5 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </motion.svg>
                      </span>
                    </motion.button>
                  )}

                  {currentStep === 2 && (
                    <motion.button
                      type="button"
                      onClick={handleNextStep}
                      disabled={!canProceedToStep3}
                      variants={buttonVariants}
                      whileHover={canProceedToStep3 ? "hover" : undefined}
                      whileTap={canProceedToStep3 ? "tap" : undefined}
                      className="w-full sm:w-auto px-4 sm:px-6 py-3 sm:py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-full font-bold shadow-xl text-sm hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-60 disabled:cursor-not-allowed touch-manipulation"
                    >
                      <span className="flex items-center justify-center sm:justify-start">
                        <span>{t('modals.serviceSelection.buttons.continue', 'Continue')}</span>
                        <motion.svg 
                          className="w-4 h-4 sm:w-5 sm:h-5 ml-2" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                          initial={{ x: 0 }}
                          animate={{ x: canProceedToStep3 ? 5 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </motion.svg>
                      </span>
                    </motion.button>
                  )}

                  {currentStep === 3 && (
                    <motion.button
                      type="button"
                      onClick={handleSubmitOrder}
                      disabled={!canSubmitOrder}
                      variants={buttonVariants}
                      whileHover={canSubmitOrder ? "hover" : undefined}
                      whileTap={canSubmitOrder ? "tap" : undefined}
                      className="w-full sm:w-auto px-4 sm:px-6 py-3 sm:py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-full font-bold shadow-xl text-sm hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-60 disabled:cursor-not-allowed touch-manipulation"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center justify-center sm:justify-start">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full mr-2 sm:mr-3"
                          />
                          {t('modals.serviceSelection.buttons.processingOrder', 'Processing Order...')}
                        </span>
                      ) : isValidating ? (
                        <span className="flex items-center justify-center sm:justify-start">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full mr-2 sm:mr-3"
                          />
                          {t('modals.serviceSelection.buttons.validating', 'Validating...')}
                        </span>
                      ) : (
                        <span className="flex items-center justify-center sm:justify-start">
                          <motion.svg 
                            className="w-4 h-4 sm:w-5 sm:h-5 mr-2" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                            initial={{ scale: 1 }}
                            animate={{ scale: canSubmitOrder ? [1, 1.1, 1] : 1 }}
                            transition={{ duration: 0.5, repeat: canSubmitOrder ? Infinity : 0 }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </motion.svg>
                          {t('modals.serviceSelection.buttons.placeOrder', 'Place Order')}
                        </span>
                      )}
                    </motion.button>
                  )}

                  {currentStep === 4 && orderSuccess && (
                    <motion.button
                      type="button"
                      onClick={() => {
                        if (onOrderPlaced) {
                          onOrderPlaced(createdOrder);
                        }
                        onClose();
                      }}
                      variants={buttonVariants}
                      whileHover="hover"
                      whileTap="tap"
                      className="w-full sm:w-auto px-4 sm:px-6 py-3 sm:py-3 bg-indigo-600 text-white rounded-full font-bold shadow-xl text-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:scale-105 active:scale-95"
                    >
                      <span className="flex items-center justify-center sm:justify-start">
                        <motion.svg 
                          className="w-4 h-4 sm:w-5 sm:h-5 mr-2" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.2, type: "spring" }}
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </motion.svg>
                        {t('modals.serviceSelection.buttons.done', 'Done')}
                      </span>
                    </motion.button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
      {mobileError && isMobile() && (
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -30 }}
          className="fixed top-4 left-1/2 -translate-x-1/2 z-[9999] bg-red-500 text-white px-6 py-3 rounded-xl shadow-lg text-sm font-semibold"
          style={{ maxWidth: 320 }}
        >
          {mobileError}
        </motion.div>
      )}
      {/* Toast for backend error messages */}
      {showToast && (
        <div className="fixed top-8 left-1/2 transform -translate-x-1/2 z-[9999]">
          <Toast
            title={t('modals.serviceSelection.errors.orderErrorTitle', 'Order Error')}
            description={toastMessage}
            duration={4000}
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </AnimatePresence>
  );
};

export default ServiceSelectionModal;
