/******************************************************************
 * TalentCard – ENHANCED GAMING EDITION (UI/UX Enhanced)
 * Props unchanged – swap file and enjoy
 *****************************************************************/
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { FaUserPlus, FaUserCheck, FaBolt, FaFire, FaDiamond, FaPlay, FaPause, FaVolumeHigh, FaTriangleExclamation } from 'react-icons/fa6';
import { useToggleFollow } from '../hooks/talent/useTalents';
import { getCdnUrl } from '../utils/cdnUtils';

// A version of TalentCard optimized for grid layouts on the Home page
// This component is used in the Recommended, New and Online talent sections
// where a 4-column grid is displayed instead of the previous slider/carousel.
const NewTalentCard = ({ talent = {}, badgeType }) => {
  const {
    id,
    name: displayName,
    level: levelNumber,
    image: profileImage,
    rating = 0,
    services = [],
    isHot,
    verified = false,
    vip = false,
    voiceNoteUrl,
    is_following,
    isFollowing: isFollowingProp
  } = talent;

  const isFeatured = Boolean(isHot);
  const voice_note = voiceNoteUrl;

  const navigate = useNavigate();
  const toggleFollowMutation = useToggleFollow();
  const [isFollowing, setIsFollowing] = useState(
    is_following ?? isFollowingProp ?? false
  );
  const [followLoading, setFollowLoading] = useState(false);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [isPlayingVoice, setIsPlayingVoice] = useState(false);
  const [audioElement, setAudioElement] = useState(null);
  const [audioLoading, setAudioLoading] = useState(false);
  const [audioProgress, setAudioProgress] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [audioError, setAudioError] = useState(false);

  /* Enhanced 3-D hover tilt */
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useTransform(y, [-100, 100], [12, -12]);
  const rotateY = useTransform(x, [-100, 100], [-12, 12]);

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
    };
  }, [audioElement]);

  const handleCardClick = () => navigate(`/talents/${id}`);

  const handleFollowClick = async (e) => {
    e.stopPropagation();
    setFollowLoading(true);
    const newState = !isFollowing;
    setIsFollowing(newState);
    try {
      const res = await toggleFollowMutation.mutateAsync({
        talentId: id,
        isFollowing: newState,
      });
      if (typeof res?.is_following === 'boolean')
        setIsFollowing(res.is_following);
    } catch {
      setIsFollowing((prev) => !prev);
    }
    setFollowLoading(false);
  };

  const handleVoiceNoteClick = async (e) => {
    e.stopPropagation();

    if (!voice_note) return;

    if (isPlayingVoice && audioElement) {
      // Stop current audio
      audioElement.pause();
      audioElement.currentTime = 0;
      setIsPlayingVoice(false);
      setAudioElement(null);
      setAudioProgress(0);
    } else {
      // Play new audio
      setAudioLoading(true);
      setAudioError(false);

      try {
        const audio = new Audio(getCdnUrl(voice_note));

        // Enhanced audio event handlers
        audio.onloadedmetadata = () => {
          setAudioDuration(audio.duration);
          setAudioLoading(false);
        };

        audio.ontimeupdate = () => {
          if (audio.duration) {
            setAudioProgress((audio.currentTime / audio.duration) * 100);
          }
        };

        audio.onended = () => {
          setIsPlayingVoice(false);
          setAudioElement(null);
          setAudioProgress(0);
        };

        audio.onerror = () => {
          setIsPlayingVoice(false);
          setAudioElement(null);
          setAudioLoading(false);
          setAudioError(true);
          setAudioProgress(0);
          console.error('Error playing voice note');
        };

        audio.oncanplaythrough = () => {
          setAudioLoading(false);
        };

        await audio.play();
        setAudioElement(audio);
        setIsPlayingVoice(true);
      } catch (error) {
        console.error('Error playing voice note:', error);
        setIsPlayingVoice(false);
        setAudioElement(null);
        setAudioLoading(false);
        setAudioError(true);
        setAudioProgress(0);
      }
    }
  };

  const defaultImageUrl = '/AuthLogo.png';
  const maxServices = 2;
  const visibleServices = services.slice(0, maxServices);
  const extraServices = services.length - maxServices;

  const badgeConfig = {
    recommended: {
      label: 'Recommended',
      gradient: 'from-purple-500 to-indigo-500',
      glowColor: 'rgba(139,92,246,0.4)',
      borderColor: 'border-purple-400/30'
    },
    new: {
      label: 'New',
      gradient: 'from-emerald-500 to-teal-500',
      glowColor: 'rgba(16,185,129,0.4)',
      borderColor: 'border-emerald-400/30'
    },
    online: {
      label: 'Online',
      gradient: 'from-orange-500 to-red-500',
      glowColor: 'rgba(249,115,22,0.4)',
      borderColor: 'border-orange-400/30'
    }
  };
  const badge = badgeConfig[badgeType];

  /* Enhanced rarity color helper with more vibrant gradients */
  const rarityColor = (idx) => {
    const colors = [
      'from-cyan-400 via-blue-500 to-purple-600',
      'from-pink-400 via-purple-500 to-indigo-600',
      'from-emerald-400 via-teal-500 to-cyan-600',
      'from-orange-400 via-red-500 to-pink-600'
    ];
    return colors[idx % colors.length];
  };

  return (
    <motion.div
      className="group relative w-full aspect-square rounded-2xl sm:rounded-3xl border-2 border-white/20 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 shadow-[0_6px_24px_0_rgba(24,25,38,0.35)] hover:shadow-[0_8px_32px_0_rgba(24,25,38,0.55)] sm:shadow-[0_8px_32px_0_rgba(24,25,38,0.45)] sm:hover:shadow-[0_12px_48px_0_rgba(24,25,38,0.65)] backdrop-blur-2xl transition-all duration-300 cursor-pointer overflow-hidden"
      style={{ perspective: 1200 }}
      onClick={handleCardClick}
      whileHover={{ scale: 1.02 }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        x.set(e.clientX - centerX);
        y.set(e.clientY - centerY);
        setMousePos({
          x: ((e.clientX - rect.left) / rect.width) * 100,
          y: ((e.clientY - rect.top) / rect.height) * 100
        });
      }}
      onMouseLeave={() => {
        x.set(0);
        y.set(0);
        setMousePos({ x: 50, y: 50 });
      }}
    >
      {/* Glowing ring effect - matching Popular Games design */}
      <div className="absolute inset-0 z-0 rounded-2xl sm:rounded-3xl pointer-events-none"
        style={{
          background: 'radial-gradient(circle at 50% 60%, rgba(165,180,252,0.35) 0%, rgba(192,132,252,0.25) 40%, rgba(236,72,153,0.18) 80%, transparent 100%)',
          filter: 'blur(12px)',
          opacity: 0.85
        }}
      />

      {/* Tilt container */}
      <motion.div
        style={{ rotateX, rotateY, transformStyle: 'preserve-3d' }}
        className="w-full h-full relative"
      >
        {/* Profile image - matching Popular Games design */}
        <img
          src={profileImage || defaultImageUrl}
          alt={displayName}
          className="w-full h-full object-cover object-center scale-105 rounded-2xl sm:rounded-3xl group-hover:scale-110 transition-transform duration-500"
          style={{ boxShadow: '0 0 0 4px rgba(255,255,255,0.08)' }}
          onError={(e) => {
            e.target.src = defaultImageUrl;
          }}
        />

        {/* Overlay for readability - matching Popular Games design */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-80 group-hover:opacity-90 transition-opacity rounded-2xl sm:rounded-3xl"></div>

        {/* Bottom gradient overlay */}
        <div className="absolute bottom-0 left-0 w-full h-16 sm:h-20 z-10 pointer-events-none rounded-b-2xl sm:rounded-b-3xl"
          style={{
            background: 'linear-gradient(to top, rgba(24,25,38,0.85) 0%, rgba(24,25,38,0.0) 100%)'
          }}
        />





        {/* Top Status Bar - Enhanced badges matching Popular Games design */}
        <div className="absolute top-2 sm:top-4 left-2 sm:left-4 right-2 sm:right-4 flex justify-between items-start z-20">
          {/* Enhanced Section Badge */}
          {badge && (
            <motion.div
              className={`px-2 py-1 sm:px-3 sm:py-1.5 rounded-full bg-gradient-to-r ${badge.gradient} text-white text-[10px] sm:text-xs font-bold tracking-wider shadow backdrop-blur-md border ${badge.borderColor} z-30`}
              style={{
                boxShadow: `0 0 15px ${badge.glowColor}, 0 2px 8px rgba(0,0,0,0.3)`
              }}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.05 }}
            >
              {badge.label.toUpperCase()}
            </motion.div>
          )}

          {/* Enhanced Level Badge - matching Popular Games design */}
          <motion.div
            className="flex items-center gap-1 px-2 py-1 sm:px-3 sm:py-1.5 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-[10px] sm:text-xs font-bold shadow backdrop-blur-md border border-white/20 z-30"
            style={{
              boxShadow: '0 0 15px rgba(139,92,246,0.4), 0 2px 8px rgba(0,0,0,0.3)'
            }}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            whileHover={{ scale: 1.05 }}
          >
            <FaBolt className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-yellow-300" />
            <span className="tracking-wider">
              LVL {levelNumber}
            </span>
          </motion.div>
        </div>

        {/* Content overlay - Enhanced */}
        <div className="absolute inset-0 p-3 sm:p-4 md:p-5 text-white flex flex-col justify-end">
          {/* Enhanced Featured Badge - matching Popular Games design */}
          {isFeatured && (
            <motion.div
              className="absolute top-2 right-2 sm:top-4 sm:right-4 flex items-center gap-1 px-2 py-1 sm:px-3 sm:py-1.5 text-[10px] sm:text-xs font-bold uppercase tracking-wider rounded-full bg-gradient-to-r from-pink-500 to-orange-500 text-white shadow backdrop-blur-md border border-white/20 z-30"
              style={{
                boxShadow: '0 0 15px rgba(249,115,22,0.4), 0 2px 8px rgba(0,0,0,0.3)'
              }}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", delay: 0.5 }}
              whileHover={{ scale: 1.05 }}
            >
              <FaFire className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
              <span>FEATURED</span>
            </motion.div>
          )}

          {/* Enhanced Name & Badges */}
          <motion.div
            className="flex items-center gap-2 sm:gap-3 mb-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <h3 className="text-lg sm:text-lg md:text-xl font-extrabold truncate">
              <span className="bg-gradient-to-r from-indigo-200 via-white to-purple-300 bg-clip-text text-transparent drop-shadow">
                {displayName}
              </span>
            </h3>

            {/* Enhanced Voice Note Play Button with Progress */}
            {voice_note && (
              <div className="relative">
                <motion.button
                  whileHover={{ scale: 1.15 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleVoiceNoteClick}
                  disabled={audioLoading}
                  className={`relative w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg overflow-hidden ${
                    audioError
                      ? 'bg-gradient-to-r from-red-500 to-red-600 text-white'
                      : isPlayingVoice
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-purple-500/50'
                      : audioLoading
                      ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white'
                      : 'bg-gradient-to-r from-white/25 to-white/15 text-white backdrop-blur-sm hover:from-white/35 hover:to-white/25 border border-white/30'
                  }`}
                  style={{
                    boxShadow: isPlayingVoice
                      ? '0 0 15px rgba(168,85,247,0.5), 0 4px 12px rgba(0,0,0,0.3)'
                      : audioError
                      ? '0 0 15px rgba(239,68,68,0.5), 0 4px 12px rgba(0,0,0,0.3)'
                      : '0 4px 12px rgba(0,0,0,0.3), 0 0 8px rgba(255,255,255,0.1)'
                  }}
                  title={
                    audioError
                      ? 'Error loading voice note'
                      : audioLoading
                      ? 'Loading voice note...'
                      : isPlayingVoice
                      ? 'Stop voice note'
                      : 'Play voice note'
                  }
                >
                  {/* Progress Ring */}
                  {isPlayingVoice && (
                    <svg className="absolute inset-0 w-full h-full -rotate-90" viewBox="0 0 32 32">
                      <circle
                        cx="16"
                        cy="16"
                        r="14"
                        fill="none"
                        stroke="rgba(255,255,255,0.2)"
                        strokeWidth="2"
                      />
                      <circle
                        cx="16"
                        cy="16"
                        r="14"
                        fill="none"
                        stroke="rgba(255,255,255,0.8)"
                        strokeWidth="2"
                        strokeDasharray={`${2 * Math.PI * 14}`}
                        strokeDashoffset={`${2 * Math.PI * 14 * (1 - audioProgress / 100)}`}
                        className="transition-all duration-100 ease-linear"
                      />
                    </svg>
                  )}

                  {/* Icon with animations */}
                  <div className="relative z-10">
                    {audioLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-3 h-3 sm:w-3.5 sm:h-3.5 border-2 border-current border-t-transparent rounded-full"
                      />
                    ) : audioError ? (
                      <FaTriangleExclamation className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                    ) : isPlayingVoice ? (
                      <motion.div
                        initial={{ scale: 0.8 }}
                        animate={{ scale: 1 }}
                        className="flex items-center"
                      >
                        <FaPause className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                      </motion.div>
                    ) : (
                      <motion.div
                        whileHover={{ x: 0.5 }}
                        className="flex items-center"
                      >
                        <FaPlay className="w-2.5 h-2.5 sm:w-3 sm:h-3 ml-0.5" />
                      </motion.div>
                    )}
                  </div>
                </motion.button>

                {/* Waveform Animation when playing */}
                {isPlayingVoice && (
                  <motion.div
                    className="absolute -right-8 top-1/2 -translate-y-1/2 flex items-center gap-0.5"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                  >
                    {[...Array(3)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="w-0.5 bg-white/60 rounded-full"
                        animate={{
                          height: [4, 8, 4, 6, 4],
                        }}
                        transition={{
                          duration: 0.8,
                          repeat: Infinity,
                          delay: i * 0.1,
                          ease: "easeInOut"
                        }}
                      />
                    ))}
                  </motion.div>
                )}

                {/* Audio duration indicator */}
                {audioDuration > 0 && !isPlayingVoice && !audioLoading && (
                  <motion.div
                    className="absolute -bottom-5 left-1/2 -translate-x-1/2 text-[8px] text-white/60 font-medium bg-black/30 px-1.5 py-0.5 rounded-full backdrop-blur-sm"
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    {Math.round(audioDuration)}s
                  </motion.div>
                )}
              </div>
            )}

            {/* Enhanced Verified Badge */}
            {verified && (
              <motion.div 
                className="w-6 h-6 sm:w-6 md:w-7 sm:h-6 md:h-7 rounded-full flex items-center justify-center shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  boxShadow: '0 0 15px rgba(59,130,246,0.5)'
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-4 h-4 sm:w-4 sm:h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </motion.div>
            )}
            
            {/* Enhanced VIP Badge */}
            {vip && (
              <motion.div 
                className="w-5 h-5 sm:w-6 md:w-7 sm:h-6 md:h-7 rounded-full flex items-center justify-center shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #ec4899 100%)',
                  boxShadow: '0 0 15px rgba(251,191,36,0.5)'
                }}
                whileHover={{ scale: 1.1, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </motion.div>
            )}
          </motion.div>

          <div className="flex items-end justify-between">
            <div className="flex flex-col">
              {/* Enhanced Rating */}
              <motion.div
                className="flex items-center gap-1 sm:gap-2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
              >
                <div className="flex items-center gap-1 px-2 py-0.5 sm:px-3 sm:py-1 rounded-lg backdrop-blur-sm" style={{
                  background: 'linear-gradient(135deg, rgba(251,191,36,0.2) 0%, rgba(245,158,11,0.3) 100%)',
                  border: '1px solid rgba(251,191,36,0.3)'
                }}>
                  <svg className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span className="text-base text-white font-bold text-[10px] sm:text-sm drop-shadow">
                    {typeof rating === 'number' ? rating.toFixed(1) : '5.0'}
                  </span>
                </div>
              </motion.div>

              {/* Enhanced Services */}
              <motion.div
                className="flex flex-wrap gap-1 sm:gap-2 mt-1 sm:mt-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                {visibleServices.map((s, idx) => (
                  <motion.span
                    key={idx}
                    className={`px-1.5 py-0.5 sm:px-3 sm:py-1.5 text-[9px] sm:text-xs font-black rounded-md sm:rounded-lg bg-gradient-to-r ${rarityColor(idx)} text-white shadow-lg`}
                    style={{
                      boxShadow: '0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.1)'
                    }}
                    whileHover={{ scale: 1.05, y: -2 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.9 + idx * 0.1 }}
                  >
                    {typeof s === 'string'
                      ? s
                      : s.service_category?.slug?.toLowerCase() === 'others'
                      ? s.service_type_title || s.type || s.name
                      : s.type || s.name}
                  </motion.span>
                ))}
                {/* {extraServices > 0 && (
                  <motion.span
                    className="px-1.5 py-0.5 sm:px-3 sm:py-1.5 text-[9px] sm:text-xs font-black rounded-md sm:rounded-lg text-white shadow-lg"
                    style={{
                      background: 'linear-gradient(135deg, rgba(75,85,99,0.8) 0%, rgba(55,65,81,0.9) 100%)',
                      border: '1px solid rgba(156,163,175,0.3)'
                    }}
                    whileHover={{ scale: 1.05, y: -2 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1.1 }}
                  >
                    +{extraServices}
                  </motion.span>
                )} */}
              </motion.div>
            </div>

            {/* Follow Button */}
            <motion.button
              whileHover={{ scale: 1.15, rotate: 5 }}
              whileTap={{ scale: 0.85 }}
              onClick={handleFollowClick}
              disabled={followLoading}
              className={`w-8 h-8 sm:w-10 sm:h-10 rounded-lg sm:rounded-xl flex items-center justify-center transition-all duration-300 shadow-lg ${
                isFollowing
                  ? 'bg-gradient-to-r from-emerald-400 to-teal-500 text-white shadow-emerald-400/40'
                  : 'bg-gradient-to-r from-white/20 to-white/10 text-white backdrop-blur-sm hover:from-white/30 hover:to-white/20 border border-white/20'
              }`}
              style={{
                boxShadow: isFollowing
                  ? '0 0 20px rgba(52,211,153,0.4), 0 4px 15px rgba(0,0,0,0.3)'
                  : '0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.1)'
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              {followLoading ? (
                <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : isFollowing ? (
                <FaUserCheck className="w-3 h-3 sm:w-4 sm:h-4" />
              ) : (
                <FaUserPlus className="w-3 h-3 sm:w-4 sm:h-4" />
              )}
            </motion.button>
          </div>
        </div>

        {/* Subtle hover effect overlay */}
        <motion.div
          className="absolute inset-0 pointer-events-none rounded-2xl sm:rounded-3xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"
          style={{
            background: `radial-gradient(circle at ${mousePos.x}% ${mousePos.y}%, rgba(255,255,255,0.2) 0%, transparent 50%)`
          }}
        />
      </motion.div>


    </motion.div>
  );
};

export default NewTalentCard;
