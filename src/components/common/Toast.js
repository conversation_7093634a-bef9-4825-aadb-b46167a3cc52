import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const Toast = ({ title, description = '', duration = 3000, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [settled, setSettled] = useState(false);
  const audioRef = useRef(null);

  useEffect(() => {
    // Play sound when toast appears - with proper error handling for autoplay restrictions
    if (isVisible && audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(error => {
        // Silently handle autoplay restrictions - this is expected behavior
        // Only log if it's not an autoplay restriction error
        if (error.name !== 'NotAllowedError') {
          console.warn('Toast audio play failed:', error);
        }
      });
    }
  }, [isVisible]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => {
        onClose && onClose();
      }, 300); // Wait for exit animation to complete
    }, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);

  // After overshoot, settle to scale: 1
  useEffect(() => {
    if (!settled && isVisible) {
      const settleTimer = setTimeout(() => setSettled(true), 180);
      return () => clearTimeout(settleTimer);
    }
  }, [settled, isVisible]);

  // Use a consistent borderRadius for both wrapper and content
  const borderRadius = settled ? 24 : 8;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -40, scale: 0.95, borderRadius: 8 }}
          animate={settled ? { opacity: 1, y: 0, scale: 1, borderRadius: 24 } : { opacity: 1, y: 0, scale: 1.02, borderRadius: 20 }}
          exit={{ opacity: 0, y: -20, scale: 0.95, borderRadius: 8 }}
          transition={{
            type: 'spring',
            stiffness: 500,
            damping: 30,
            mass: 1,
            bounce: 0.25,
            duration: 0.5
          }}
          style={{ borderRadius, padding: 0, background: 'none', minWidth: 350, maxWidth: 800, width: '100%' }}
          className="relative"
        >
          {/* Gradient border wrapper */}
          <div
            className="p-1 w-full h-full"
            style={{
              borderRadius,
              background: 'linear-gradient(120deg, #6366f1 0%, #ec4899 50%, #fbbf24 100%)',
              boxShadow: '0 8px 40px 0 rgba(99,102,241,0.10), 0 1.5px 8px 0 rgba(139,92,246,0.08)'
            }}
          >
            {/* Audio element for toast sound */}
            <audio ref={audioRef} src="/sounds/ToastAudio.mp3" preload="auto" />
            {/* White content with matching border radius */}
            <div
              className="flex items-start bg-white/90 dark:bg-gray-900/90 p-6 w-full h-full relative overflow-hidden shadow-xl backdrop-blur-lg"
              style={{ borderRadius }}
            >
              {/* Logo */}
              <img src="/AuthLogo.png" alt="Logo" className="w-12 h-12 mr-4 mt-2 object-contain select-none rounded-2xl border-2 border-indigo-100 bg-white shadow" draggable="false" />
              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="font-bold text-lg mb-1 leading-tight bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-400 bg-clip-text text-transparent drop-shadow-lg">
                  MISSION ALERT 🔔
                </div>
                <div className="border-b-2 border-transparent bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-400 bg-clip-border w-1/2 mb-2" style={{borderImage: 'linear-gradient(90deg, #6366f1, #ec4899, #fbbf24) 1'}} />
                <div className="font-bold text-lg mb-1 leading-tight bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-400 bg-clip-text text-transparent drop-shadow-lg">
                  {typeof title === 'object' ? JSON.stringify(title) : String(title || '')}
                </div>
                <div className="text-gray-800 dark:text-gray-100 text-base break-words">
                  {typeof description === 'object' ? JSON.stringify(description) : String(description || '')}
                </div>
              </div>
              {/* Animated Progress Bar */}
          <motion.div
                className="absolute left-4 right-4 bottom-2 h-1 rounded-full bg-gradient-to-r from-indigo-600 to-blue-500"
            initial={{ width: '100%' }}
            animate={{ width: 0 }}
                transition={{ duration: duration / 1000, ease: 'linear' }}
          />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Toast;
