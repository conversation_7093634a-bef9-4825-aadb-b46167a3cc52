import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Move features outside component to prevent re-creation on every render
const features = [
  { title: "Connect Instantly", desc: "Chat with talents in real-time" },
  { title: "Secure Wallet", desc: "Manage your payments safely" },
  { title: "Discover Talents", desc: "Find amazing creators nearby" }
];

const MobileAppPromoModal = ({ open, onClose }) => {
  const [currentFeature, setCurrentFeature] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (open) {
      const interval = setInterval(() => {
        setCurrentFeature((prev) => (prev + 1) % features.length);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [open]);

  const handleMouseMove = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setMousePosition({
      x: ((e.clientX - rect.left) / rect.width) * 100,
      y: ((e.clientY - rect.top) / rect.height) * 100
    });
  };

  return (
    <AnimatePresence>
      {open && (
        <motion.div
        key="modal-overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="fixed inset-0 z-[1000] flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900"
        aria-modal="true"
        role="dialog"
      >
        {/* Floating particles background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-gradient-to-r from-indigo-400 to-pink-400 rounded-full opacity-20"
              initial={{
                x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),
                y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800)
              }}
              animate={{
                y: [null, -100, (typeof window !== 'undefined' ? window.innerHeight : 800) + 100],
                rotate: [0, 360]
              }}
              transition={{ 
                duration: Math.random() * 10 + 10,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <motion.div
          key="modal-card"
          initial={{ opacity: 0, scale: 0.85, y: 50, rotateX: 15 }}
          animate={{ opacity: 1, scale: 1, y: 0, rotateX: 0 }}
          exit={{ opacity: 0, scale: 0.85, y: 50, rotateX: 15 }}
          transition={{ 
            type: 'spring', 
            stiffness: 300, 
            damping: 25,
            mass: 0.8 
          }}
          className="relative w-full h-full md:h-auto md:max-w-6xl mx-0 md:mx-4 perspective-1000"
          onClick={(e) => e.stopPropagation()}
          onMouseMove={handleMouseMove}
          style={{
            transform: (typeof window !== 'undefined' && window.innerWidth >= 768) ? `rotateX(${(mousePosition.y - 50) * 0.1}deg) rotateY(${(mousePosition.x - 50) * 0.1}deg)` : 'none',
            transformStyle: 'preserve-3d'
          }}
        >
          {/* Holographic border */}
          <div className="absolute inset-0 rounded-none md:rounded-3xl bg-gradient-to-r from-indigo-500 via-purple-500 via-pink-500 to-yellow-400 p-0 md:p-1 animate-pulse">
            <div className="absolute inset-0 rounded-none md:rounded-3xl bg-gradient-to-r from-indigo-600 via-purple-600 via-pink-600 to-yellow-500 opacity-50 blur-xl"></div>
          </div>

          {/* Main content container */}
          <div className="relative bg-gradient-to-br from-white/95 via-white/90 to-white/85 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-gray-800/85 rounded-none md:rounded-3xl overflow-hidden backdrop-blur-xl shadow-2xl border-0 md:border border-white/20 h-full md:h-auto">
            
            {/* Animated gradient background */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 via-purple-50/30 to-pink-50/50 dark:from-indigo-900/20 dark:via-purple-900/10 dark:to-pink-900/20"></div>
            
            {/* Glowing orbs */}
            <div className="absolute top-0 left-0 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-indigo-400/20 to-purple-600/20 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2 animate-pulse"></div>
            <div className="absolute bottom-0 right-0 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-pink-400/20 to-yellow-500/20 rounded-full blur-3xl translate-x-1/2 translate-y-1/2 animate-pulse"></div>

            {/* Close Button */}
            <motion.button
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              className="absolute top-4 right-4 md:top-6 md:right-6 w-10 h-10 md:w-12 md:h-12 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/40 dark:border-gray-700/40 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:text-red-500 transition-all duration-300 z-50 shadow-lg"
            >
              <svg className="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </motion.button>

            <div className="relative flex flex-col items-center p-4 md:p-8 lg:p-12 gap-6 md:gap-8 lg:gap-12 min-h-screen md:min-h-[600px] md:flex-row lg:flex-row justify-center">
              
              {/* Left Content */}
              <div className="flex-1 flex flex-col items-center text-center md:items-start md:text-left lg:items-start lg:text-left z-10 space-y-4 md:space-y-6 pt-12 md:pt-0 max-w-none md:max-w-lg lg:max-w-none">
                
                {/* Title with typing animation */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.8 }}
                  className="space-y-3 md:space-y-4"
                >
                  <motion.div
                    className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/50 dark:to-purple-900/50 border border-indigo-200 dark:border-indigo-700"
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <span className="text-xs md:text-sm font-semibold text-indigo-700 dark:text-indigo-300">✨ Now Available</span>
                  </motion.div>
                  
                  <h1 className="text-3xl md:text-4xl lg:text-6xl font-black mb-2 md:mb-4">
                    <span className="bg-gradient-to-r from-indigo-600 via-purple-600 via-pink-500 to-yellow-500 bg-clip-text text-transparent drop-shadow-lg">
                      Mission X
                    </span>
                    <br />
                    <span className="text-gray-800 dark:text-white text-2xl md:text-3xl lg:text-4xl font-bold">
                      Goes Mobile! 🚀
                    </span>
                  </h1>
                </motion.div>

                {/* Feature carousel */}
                <motion.div 
                  className="h-12 md:h-16 flex items-center justify-center md:justify-start lg:justify-start"
                  key={currentFeature}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                >
                  <div className="text-center md:text-left lg:text-left">
                    <h3 className="text-lg md:text-xl font-bold text-gray-800 dark:text-white">
                      {features[currentFeature].title}
                    </h3>
                    <p className="text-sm md:text-base text-gray-600 dark:text-gray-300">
                      {features[currentFeature].desc}
                    </p>
                  </div>
                </motion.div>

                <p className="text-base md:text-lg text-gray-700 dark:text-gray-200 leading-relaxed max-w-sm md:max-w-lg px-4 md:px-0">
                  Experience the full power of Mission X in your pocket. Connect with talents, 
                  manage your wallet, and discover amazing opportunities - all optimized for mobile!
                </p>

                {/* Download buttons */}
                <motion.div 
                  className="flex flex-col gap-3 w-full max-w-sm md:max-w-lg px-4 md:px-0"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                >
                  <motion.a 
                    href="https://play.google.com/store/apps/details?id=com.missionx.user"
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                    className="group relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 hover:from-indigo-500 hover:via-purple-500 hover:to-pink-500 rounded-xl md:rounded-2xl p-3 md:p-4 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                    <div className="relative flex items-center justify-center space-x-3">
                      <svg className="w-6 h-6 md:w-8 md:h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                      </svg>
                      <div className="text-left">
                        <div className="text-xs text-indigo-100">Get it on</div>
                        <div className="text-sm font-bold text-white">Google Play</div>
                      </div>
                    </div>
                  </motion.a>

                  <motion.a 
                    href="https://apps.apple.com/my/app/mission-x-gigs-for-youth/id6743386705"
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                    className="group relative overflow-hidden bg-gradient-to-r from-gray-800 via-gray-900 to-black hover:from-gray-700 hover:via-gray-800 hover:to-gray-900 rounded-xl md:rounded-2xl p-3 md:p-4 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                    <div className="relative flex items-center justify-center space-x-3">
                      <svg className="w-6 h-6 md:w-8 md:h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.71,19.5C17.88,20.74 17,21.95 15.66,21.97C14.32,22 13.89,21.18 12.37,21.18C10.84,21.18 10.37,21.95 9.1,22C7.79,22.05 6.8,20.68 5.96,19.47C4.25,17 2.94,12.45 4.7,9.39C5.57,7.87 7.13,6.91 8.82,6.88C10.1,6.86 11.32,7.75 12.11,7.75C12.89,7.75 14.37,6.68 15.92,6.84C16.57,6.87 18.39,7.1 19.56,8.82C19.47,8.88 17.39,10.1 17.41,12.63C17.44,15.65 20.06,16.66 20.09,16.67C20.06,16.74 19.67,18.11 18.71,19.5M13,3.5C13.73,2.67 14.94,2.04 15.94,2C16.07,3.17 15.6,4.35 14.9,5.19C14.21,6.04 13.07,6.7 11.95,6.61C11.8,5.46 12.36,4.26 13,3.5Z"/>
                      </svg>
                      <div className="text-left">
                        <div className="text-xs text-gray-300">Download on</div>
                        <div className="text-sm font-bold text-white">App Store</div>
                      </div>
                    </div>
                  </motion.a>
                </motion.div>

                {/* Trust indicators */}
                <motion.div 
                  className="flex items-center justify-center md:justify-start lg:justify-start space-x-4 md:space-x-6 text-xs md:text-sm text-gray-500 dark:text-gray-400 pb-4 md:pb-0"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-400">★★★★★</span>
                    <span>4.8 Rating</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>📱</span>
                    <span>10K+ Downloads</span>
                  </div>
                </motion.div>
              </div>

              {/* Right: Phone Mockups - Hidden on mobile, visible on tablet+ */}
              <div className="hidden md:flex flex-1 relative h-80 lg:h-[500px] w-full max-w-md">
                <motion.div
                  className="absolute inset-0 flex items-center justify-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  {/* Background phone with wallet */}
                  <motion.div
                    className="absolute left-0 top-8"
                    animate={{ 
                      y: [0, -10, 0],
                      rotate: [-8, -6, -8]
                    }}
                    transition={{ 
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-3xl blur-2xl transform scale-110"></div>
                      <img
                        src="/Dompet.png"
                        alt="MissionX Wallet"
                        className="relative w-24 lg:w-40 rounded-2xl shadow-2xl opacity-70"
                        style={{ filter: 'drop-shadow(0 25px 50px rgba(139,92,246,0.3))' }}
                      />
                    </div>
                  </motion.div>

                  {/* Main phone - homepage */}
                  <motion.div
                    className="relative z-10"
                    animate={{ 
                      y: [0, -15, 0],
                      rotate: [2, 0, 2]
                    }}
                    transition={{ 
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <div className="relative">
                      {/* Glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/40 via-purple-400/40 to-pink-400/40 rounded-3xl blur-3xl transform scale-110"></div>
                      
                      {/* Phone frame */}
                      <div className="relative bg-gradient-to-br from-gray-800 to-gray-900 p-2 rounded-3xl shadow-2xl">
                        <img
                          src="/HomepageApp.png"
                          alt="MissionX App Home"
                          className="w-32 lg:w-52 rounded-2xl"
                          style={{ filter: 'drop-shadow(0 20px 40px rgba(0,0,0,0.3))' }}
                        />
                        
                        {/* Screen reflection */}
                        <div className="absolute inset-2 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl pointer-events-none"></div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Floating elements */}
                  <motion.div
                    className="absolute top-0 right-0 w-4 h-4 lg:w-6 lg:h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full"
                    animate={{ 
                      y: [0, -20, 0],
                      x: [0, 10, 0],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      delay: 0.5
                    }}
                  />
                  
                  <motion.div
                    className="absolute bottom-8 left-8 w-3 h-3 lg:w-4 lg:h-4 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full"
                    animate={{ 
                      y: [0, -15, 0],
                      x: [0, -8, 0],
                      scale: [1, 0.8, 1]
                    }}
                    transition={{ 
                      duration: 3,
                      repeat: Infinity,
                      delay: 1
                    }}
                  />
                </motion.div>
              </div>
            </div>

            {/* Bottom wave decoration */}
            <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-indigo-50/50 to-transparent dark:from-indigo-900/20 dark:to-transparent">
              <svg className="absolute bottom-0 w-full h-12" viewBox="0 0 1200 120" fill="none">
                <path d="M0,60 C300,120 600,0 900,60 C1050,90 1150,30 1200,60 L1200,120 L0,120 Z" fill="url(#wave-gradient)"/>
                <defs>
                  <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="rgba(99,102,241,0.1)"/>
                    <stop offset="50%" stopColor="rgba(168,85,247,0.1)"/>
                    <stop offset="100%" stopColor="rgba(236,72,153,0.1)"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </motion.div>
      </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MobileAppPromoModal;