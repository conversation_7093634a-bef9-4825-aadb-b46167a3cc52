import React, { useState, useMemo, useRef, useEffect } from 'react';
import { DateTime } from 'luxon';
import { Avatar, AvatarImage, AvatarFallback } from "../../components/ui/avatar";
import { Button } from "../../components/ui/button";
import { ScrollArea } from "../../components/ui/scroll-area";
import {
    HeartIcon,
    MagnifyingGlassIcon,
    ChatBubbleIcon
} from '@radix-ui/react-icons';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "../../lib/utils";
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from "../../components/ui/command";
import { Badge } from "../../components/ui/badge";
import { Skeleton } from "../../components/ui/skeleton";
import { getCdnUrl } from '../../utils/cdnUtils';
import { SectionLoader } from '../../components/ui/LoadingIndicator';
import { useNavigate } from 'react-router-dom';
import useTranslation from '../../hooks/useTranslation';

const listItemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 400, damping: 25 } },
    hover: { scale: 1.02, boxShadow: '0 8px 20px rgba(99, 102, 241, 0.15)', transition: { type: 'spring', stiffness: 500, damping: 20 } }
};

const MAX_IMAGE_RETRIES = 3;
const RETRY_DELAYS = [1000, 2000, 4000]; // 1s, 2s, 4s

// Helper to safely extract the last message text
const getLastMessageText = (chat) => {
    if (!chat || chat.last_message == null) return '';
    return typeof chat.last_message === 'object'
        ? chat.last_message.content
        : chat.last_message;
};

// Helper to get user info from chat object
const getUserInfo = (chat, currentUserId) => {
    // Handle the backend data structure: chat_partner (primary structure)
    if (chat.chat_partner && chat.chat_partner.id !== currentUserId) {
        return {
            id: chat.chat_partner.id,
            uid: chat.chat_partner.uid,
            nickname: chat.chat_partner.nickname,
            profile_image: chat.chat_partner.profile_picture,
            name: chat.chat_partner.name
        };
    }
    
    // Handle the backend data structure: participants.user (secondary structure)
    if (chat.participants && Array.isArray(chat.participants)) {
        // Find the participant that is not the current user
        const otherParticipant = chat.participants.find(participant => 
            participant.user && participant.user.id !== currentUserId
        );
        
        if (otherParticipant && otherParticipant.user) {
            return {
                id: otherParticipant.user.id,
                uid: otherParticipant.user.uid,
                nickname: otherParticipant.user.nickname,
                profile_image: otherParticipant.user.profile_picture,
                name: otherParticipant.user.name
            };
        }
    }
    
    // Fallback to old structure (user/talent) for backward compatibility
    if (chat.user && chat.user.id !== currentUserId) {
        return {
            id: chat.user.id,
            uid: chat.user.uid,
            nickname: chat.user.nickname,
            profile_image: chat.user.profile_picture,
            name: chat.user.name
        };
    }
    
    if (chat.talent && chat.talent.id !== currentUserId) {
        return {
            id: chat.talent.id,
            uid: chat.talent.uid,
            nickname: chat.talent.nickname,
            profile_image: chat.talent.profile_picture,
            name: chat.talent.name
        };
    }
    
    return null;
};

// Helper to determine if all attachments are images
function areAllImages(attachments = []) {
    return attachments.length > 0 && attachments.every(att => {
        if (att.mime_type && att.mime_type.startsWith('image/')) return true;
        if (att.filename && att.filename.match(/\.(jpg|jpeg|png|gif|webp|heic|heif|bmp|svg)$/i)) return true;
        if (att.optimized_path && att.optimized_path.includes('image')) return true;
        return false;
    });
}

const ChatListHeader = ({
    searchQuery,
    setSearchQuery,
    filteredChats,
    onChatSelect,
    onToggleFavorite,
    selectedChat,
    favorites,
    onSearchChange,
    currentUserId
}) => {
    const [isSearching, setIsSearching] = useState(false);
    const { t } = useTranslation(['chat', 'common']);

    return (
        <div className="sticky top-0 z-30 bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border-b border-gray-100 dark:border-gray-800 shadow-sm">
            <div className="px-4 pt-4 pb-3">
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                        <div className="relative">
                            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500 to-violet-600 flex items-center justify-center shadow-lg shadow-indigo-500/20">
                                <ChatBubbleIcon className="w-5 h-5 text-white" />
                            </div>
                            <div className="absolute -top-1 -right-1 w-3 h-3">
                                <span className="absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75 animate-ping"></span>
                                <span className="relative inline-flex rounded-full h-3 w-3 bg-indigo-500"></span>
                            </div>
                        </div>
                        <div>
                            <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-violet-500 to-indigo-600 bg-clip-text text-transparent dark:from-indigo-200 dark:via-violet-300 dark:to-indigo-200">
                                {t('list.title')}
                            </h1>
                            <p className="text-sm text-gray-500 dark:text-gray-300 mt-0.5">{t('list.subtitle')}</p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-1">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="rounded-full bg-pink-50 text-pink-600 hover:text-gray-600 transition-colors duration-200"
                            onClick={() => onToggleFavorite(selectedChat?.id)}
                            disabled={!selectedChat}
                        >
                            <HeartIcon className={cn(
                                "w-7 h-7",
                                favorites?.includes(selectedChat?.id) && "fill-current"
                            )} />
                        </Button>
                    </div>
                </div>

                {/* Unified Search with Command Menu */}
                <div className="flex items-center">
                    <Command className="rounded-xl border-0 shadow-none bg-gray-50/80 dark:bg-gray-800/80 flex-1">
                        <CommandInput
                            placeholder={t('list.searchPlaceholder')}
                            value={searchQuery}
                            onValueChange={(value) => {
                                setSearchQuery(value);
                                setIsSearching(value.length > 0);
                                onSearchChange?.(value);
                            }}
                            onBlur={() => setTimeout(() => setIsSearching(false), 200)}
                            onFocus={() => setIsSearching(true)}
                            className="h-11 rounded-lg border-0 bg-transparent focus:ring-2 focus:ring-indigo-500/20 transition-all duration-200 dark:text-gray-100"
                            leadingIcon={<MagnifyingGlassIcon className="w-4 h-4 text-gray-500" />}
                        />
                        {isSearching && (
                            <CommandList className="absolute mt-1 w-full bg-white dark:bg-gray-900 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-800 animate-in fade-in-0 zoom-in-95 z-50">
                                {(!searchQuery) ? (
                                    <CommandGroup heading={t("list.recentSearches")}>
                                        <div className="px-2 py-3 text-sm text-gray-500 bg-gray-50 rounded-lg text-center">
                                            <span className="inline-block"><MagnifyingGlassIcon className="inline w-4 h-4 mr-1 text-gray-400 align-text-bottom" />{t("list.startTyping")}</span>
                                        </div>
                                    </CommandGroup>
                                ) : filteredChats.length > 0 ? (
                                    <CommandGroup heading={t("list.chats")}>
                                        {filteredChats.map((chat) => {
                                            const userInfo = getUserInfo(chat, currentUserId);
                                            return (
                                                <CommandItem
                                                    key={chat.id}
                                                    className="flex items-center px-2 py-2 cursor-pointer hover:bg-indigo-50/80 hover:scale-[1.01] transition-all duration-150 rounded-lg group"
                                                    onSelect={() => onChatSelect(chat)}
                                                >
                                                    <Avatar className="h-8 w-8 mr-2">
                                                        <AvatarImage 
                                                            src={userInfo?.profile_image ? getCdnUrl(userInfo.profile_image) : null}
                                                            alt={userInfo?.nickname || userInfo?.name || 'User'}
                                                        />
                                                        <AvatarFallback className="bg-gradient-to-br from-indigo-100 to-violet-100 text-indigo-600 font-semibold">
                                                            {userInfo?.nickname?.[0] || userInfo?.name?.[0] || '?'}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium text-gray-900 truncate max-w-[200px]" title={userInfo?.nickname || userInfo?.name}>
                                                            {userInfo?.nickname || userInfo?.name}
                                                            {userInfo?.uid && (
                                                                <span className="ml-1 text-xs text-gray-500 truncate">#{userInfo.uid}</span>
                                                            )}
                                                        </p>
                                                        <p className="text-xs text-gray-500 truncate max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap" title={getLastMessageText(chat)}>
                                                            {getLastMessageText(chat)}
                                                        </p>
                                                    </div>
                                                    <span className="ml-2 hidden group-hover:inline-block text-indigo-400 transition-colors duration-150">
                                                        <MagnifyingGlassIcon className="w-4 h-4" />
                                                    </span>
                                                </CommandItem>
                                            );
                                        })}
                                    </CommandGroup>
                                ) : (
                                    <CommandEmpty className="p-4 text-sm text-gray-500 bg-gray-50 rounded-lg text-center">
                                        <span className="inline-block"><MagnifyingGlassIcon className="inline w-4 h-4 mr-1 text-gray-400 align-text-bottom" />{t("list.noResults", { query: searchQuery.length > 20 ? `${searchQuery.substring(0, 20)}...` : searchQuery })}</span>
                                    </CommandEmpty>
                                )}
                            </CommandList>
                        )}
                    </Command>
                </div>
            </div>
        </div>
    );
};

const ChatList = ({
    chats = [],
    selectedChat,
    onChatSelect,
    favorites = [],
    onToggleFavorite,
    loading = false,
    error = null,
    onLoadMore,
    onSearchChange,
    hasMore = false,
    isFetchingMore = false,
    optimisticUpdates = {},
    messageQueue = [],
    currentUserId = null
}) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedChatId, setSelectedChatId] = useState(null);
    const [imageLoadErrors, setImageLoadErrors] = useState({});
    const [showScrollTop, setShowScrollTop] = useState(false);
    const observerRef = useRef(null);
    const loadMoreRef = useRef(null);
    const scrollAreaRef = useRef(null);
    const CONVERSATIONS_PER_PAGE = 18;
    const navigate = useNavigate();
    const { t } = useTranslation(['chat', 'common']);

    const handleChatSelect = (chat) => {
        setSelectedChatId(chat.id);
        onChatSelect(chat);
        
        // Add a subtle scroll animation when selecting a chat
        if (scrollAreaRef.current) {
            const chatElement = document.querySelector(`[data-chat-id="${chat.id}"]`);
            if (chatElement) {
                chatElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }
    };

    const handleImageError = (chatId) => {
        setImageLoadErrors(prev => ({ ...prev, [chatId]: true }));
    };

    const filteredChats = useMemo(() => {
        let result = [...chats];

        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            result = result.filter(chat => {
                const userInfo = getUserInfo(chat, currentUserId);
                const userDisplayName = userInfo?.nickname || userInfo?.name || '';
                
                return chat.title?.toLowerCase().includes(query) ||
                    getLastMessageText(chat)?.toLowerCase().includes(query) ||
                    userDisplayName.toLowerCase().includes(query);
            });
        }

        return result.sort((a, b) => new Date(b.last_message_at) - new Date(a.last_message_at));
    }, [chats, searchQuery, currentUserId]);

    const unopenedChats = useMemo(() =>
        filteredChats.filter(c => c.unread_count > 0 && c.id !== selectedChatId),
        [filteredChats, selectedChatId]
    );

    const regularChats = useMemo(() =>
        filteredChats.filter(c => !(c.unread_count > 0 && c.id !== selectedChatId)),
        [filteredChats, selectedChatId]
    );

    const formatTimeAgo = (timestamp) => {
        return DateTime.fromISO(timestamp).toRelative();
    };

    // Setup intersection observer for infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && hasMore && !isFetchingMore) {
                    onLoadMore();
                }
            },
            { threshold: 0.1 }
        );

        if (loadMoreRef.current) {
            observer.observe(loadMoreRef.current);
        }

        observerRef.current = observer;

        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, [hasMore, isFetchingMore, onLoadMore]);
    
    // Handle scroll events to show/hide scroll-to-top button
    useEffect(() => {
        let timeoutId;
        const handleScroll = (event) => {
            if (timeoutId) return;
            timeoutId = setTimeout(() => {
                const scrollTop = event.target.scrollTop;
                setShowScrollTop(scrollTop > 300);
                timeoutId = null;
            }, 100);
        };

        const scrollContainer = document.querySelector('.chat-list-scroll-container');
        if (scrollContainer) {
            scrollContainer.addEventListener('scroll', handleScroll, { passive: true });

            return () => {
                scrollContainer.removeEventListener('scroll', handleScroll);
                if (timeoutId) clearTimeout(timeoutId);
            };
        }
    }, []);
    
    // Function to scroll to top
    const scrollToTop = () => {
        const scrollContainer = document.querySelector('.chat-list-scroll-container');
        if (scrollContainer) {
            scrollContainer.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    };


    // Show error state
    if (error) {
        return (
            <div className="flex h-screen items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 text-red-500">
                        <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('list.errorLoading')}</h3>
                    <p className="text-gray-500 mb-4 max-w-[300px] mx-auto overflow-hidden text-ellipsis" title={error}>
                        {error && error.length > 100 ? `${error.substring(0, 100)}...` : error}
                    </p>
                    <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                        {t('list.tryAgain')}
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="flex h-screen bg-gray-50/50 dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 dark:text-gray-100">
            <div className={cn(
                "w-96 bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 border-r border-gray-100 dark:border-gray-800 shadow-lg dark:shadow-indigo-900/10",
                "transition-all duration-300 ease-in-out",
                selectedChat ? 'hidden lg:block' : ''
            )}>
                <ChatListHeader
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    filteredChats={filteredChats}
                    onChatSelect={handleChatSelect}
                    onToggleFavorite={onToggleFavorite}
                    selectedChat={selectedChat}
                    favorites={favorites}
                    onSearchChange={onSearchChange}
                    currentUserId={currentUserId}
                />

                {/* Chat List */}
                <ScrollArea ref={scrollAreaRef} className="h-[calc(100vh-64px)] custom-scrollbar chat-list-scroll-container" style={{ scrollBehavior: 'smooth' }}>
                    <div className="p-4 space-y-4 bg-gradient-to-b from-transparent via-indigo-50/10 to-transparent dark:from-transparent dark:via-indigo-900/5 dark:to-transparent">
                        {loading ? (
                            <SectionLoader message={t("list.loading")} size="medium" color="indigo" />
                        ) : (
                            <>
                                {unopenedChats.length > 0 && (
                                    <div className="space-y-4">
                                        <h3 className="px-2 text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase">{t("list.newMessages")}</h3>
                                        {unopenedChats.map((chat, index) => (
                                            <motion.div
                                                key={chat.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ delay: index * 0.05, duration: 0.3 }}
                                            >
                                                <ChatListItem
                                                    chat={chat}
                                                    isSelected={selectedChatId === chat.id}
                                                    isFavorite={favorites.includes(chat.id)}
                                                    onSelect={handleChatSelect}
                                                    formatTimeAgo={formatTimeAgo}
                                                    onImageError={() => handleImageError(chat.id)}
                                                    hasImageError={imageLoadErrors[chat.id]}
                                                    optimisticUpdates={optimisticUpdates}
                                                    messageQueue={messageQueue}
                                                    currentUserId={currentUserId}
                                                />
                                            </motion.div>
                                        ))}
                                        <hr className="border-gray-200 dark:border-gray-800" />
                                    </div>
                                )}

                                {regularChats.map((chat, index) => (
                                    <motion.div
                                        key={chat.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: index * 0.03, duration: 0.3 }}
                                    >
                                        <ChatListItem
                                            chat={chat}
                                            isSelected={selectedChatId === chat.id}
                                            isFavorite={favorites.includes(chat.id)}
                                            onSelect={handleChatSelect}
                                            formatTimeAgo={formatTimeAgo}
                                            onImageError={() => handleImageError(chat.id)}
                                            hasImageError={imageLoadErrors[chat.id]}
                                        optimisticUpdates={optimisticUpdates}
                                        messageQueue={messageQueue}
                                        currentUserId={currentUserId}
                                    />
                                </motion.div>
                            ))}

                                {hasMore && (
                                    <div ref={loadMoreRef} className="py-4">
                                        {isFetchingMore ? (
                                            <div className="flex justify-center">
                                                <div className="w-6 h-6 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                                            </div>
                                        ) : null}
                                    </div>
                                )}

                                {!loading && unopenedChats.length + regularChats.length === 0 && (
                                    <motion.div 
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="text-center py-12 px-6"
                                    >
                                        <div className="relative w-24 h-24 mx-auto mb-6">
                                            <div className="absolute inset-0 bg-gradient-to-br from-indigo-100 to-violet-100 dark:from-indigo-900 dark:to-violet-900 rounded-full animate-pulse"></div>
                                            <div className="absolute inset-2 bg-gradient-to-br from-indigo-200 to-violet-200 dark:from-indigo-800 dark:to-violet-800 rounded-full flex items-center justify-center">
                                                <svg className="w-12 h-12 text-indigo-500 dark:text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                            </div>
                                        </div>
                                        <h3 className="text-lg font-semibold bg-gradient-to-r from-indigo-600 to-violet-600 bg-clip-text text-transparent dark:from-indigo-200 dark:to-violet-200 mb-2">
                                            {t('list.noConversations')}
                                        </h3>
                                        <p className="text-gray-500 dark:text-gray-300 mb-6">{t('list.startConversation')}</p>
                                        <motion.button 
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                            className="px-6 py-2 bg-gradient-to-r from-indigo-600 to-violet-600 text-white rounded-lg hover:from-indigo-700 hover:to-violet-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                                            onClick={() => navigate('/talent')}
                                        >
                                            {t('list.browseTalents')}
                                        </motion.button>
                                    </motion.div>
                                )}
                            </>
                        )}
                    </div>
                </ScrollArea>
                
                {/* Scroll to top button */}
                <AnimatePresence>
                    {showScrollTop && (
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={scrollToTop}
                            className="absolute bottom-6 right-6 w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-violet-500 text-white shadow-lg flex items-center justify-center z-10 hover:shadow-indigo-500/30 transition-all duration-300"
                            aria-label={t('common.scrollToTop')}
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </motion.button>
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
};

const ChatListItem = ({
    chat,
    isSelected,
    isFavorite,
    onSelect,
    formatTimeAgo,
    onImageError,
    hasImageError,
    optimisticUpdates,
    messageQueue,
    currentUserId
}) => {
    const [retryCount, setRetryCount] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const { t } = useTranslation(['chat', 'common']);
    const lastMsg = chat.last_message || {};
    const isCurrentUserSender = String(lastMsg.sender_id) === String(currentUserId);
    let lastMessagePreview = '';
    if (lastMsg.attachments && lastMsg.attachments.length > 0) {
        if (areAllImages(lastMsg.attachments)) {
            lastMessagePreview = t('list.imagePlaceholder');
        } else {
            lastMessagePreview = t('list.filePlaceholder');
        }
    } else if (lastMsg.content) {
        lastMessagePreview = lastMsg.content;
    } else {
        lastMessagePreview = t('list.noMessagesYet');
    }
    if (isCurrentUserSender && lastMessagePreview !== t('list.noMessagesYet')) {
        lastMessagePreview = `${t('list.youPrefix')}: ${lastMessagePreview}`;
    }
    const isUnread = chat.unread_count > 0;
    
    // Get user info using the helper function
    const userInfo = getUserInfo(chat, currentUserId);
    const profileImage = userInfo?.profile_image ? getCdnUrl(userInfo.profile_image) : null;
    const displayName = userInfo?.nickname || userInfo?.name || chat.title || t('list.unknownUser');
    const displayUid = userInfo?.uid || null;

    // Debug logging for profile image issues
    if (!userInfo) {
        console.warn(`No user info found for chat ${chat.id}`, {
            chat,
            currentUserId,
            participants: chat.participants,
            chat_partner: chat.chat_partner,
            user: chat.user,
            talent: chat.talent,
            availableKeys: Object.keys(chat)
        });
    }

    // Get latest message from queue or optimistic updates
    const latestMessage = useMemo(() => {
        const queuedMessages = messageQueue.filter(m => m.conversationId === chat.id);
        const optimisticMessage = Object.values(optimisticUpdates).find(m => m.conversationId === chat.id);
        
        if (queuedMessages.length > 0) {
            const latestQueued = queuedMessages.reduce((latest, current) => 
                new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest
            );
            return {
                content: latestQueued.content,
                timestamp: latestQueued.timestamp,
                status: latestQueued.status
            };
        }
        
        if (optimisticMessage) {
            return {
                content: optimisticMessage.content,
                timestamp: optimisticMessage.timestamp,
                status: 'sending'
            };
        }

        return {
            content: lastMessagePreview,
            timestamp: chat.last_message_at,
            status: 'sent'
        };
    }, [chat.id, chat.last_message, chat.last_message_at, messageQueue, optimisticUpdates]);

    // Handle profile image loading with better error handling
    const handleImageLoad = () => {
        setIsLoading(false);
        setRetryCount(0);
    };

    const handleImageError = () => {
        console.warn(`Failed to load profile image for user ${displayName} (ID: ${displayUid})`, {
            profileImage,
            userInfo
        });
        if (retryCount < MAX_IMAGE_RETRIES) {
            setTimeout(() => {
                setRetryCount(prev => prev + 1);
            }, RETRY_DELAYS[retryCount]);
        } else {
            setIsLoading(false);
            onImageError();
        }
    };

    return (
        <motion.div
            initial={isUnread ? { opacity: 0, x: 32 } : { opacity: 0, y: 20 }}
            animate={{ opacity: 1, x: 0, y: 0 }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
            whileHover={{ scale: 1.02, boxShadow: '0 8px 24px rgba(99,102,241,0.15)', backgroundColor: isSelected ? 'rgba(237, 241, 254, 0.8)' : 'rgba(238, 242, 255, 0.6)' }}
            whileTap={{ scale: 0.98 }}
            variants={listItemVariants}
            className={cn(
                "group relative px-4 py-3 cursor-pointer transition-all duration-300 rounded-xl mx-2 mb-2 border-l-4 backdrop-blur-sm",
                isSelected ?
                    "bg-gradient-to-r from-indigo-100 to-indigo-50 border-indigo-500 shadow-lg dark:bg-gradient-to-r dark:from-indigo-800 dark:to-indigo-900 dark:border-indigo-400 dark:shadow-indigo-900/20 scale-[1.02]" :
                    isUnread ? "bg-gradient-to-r from-indigo-50/80 to-violet-50/80 border-indigo-400 dark:bg-gradient-to-r dark:from-indigo-900 dark:to-violet-900 dark:border-indigo-600 dark:ring-2 dark:ring-indigo-700/40" : "hover:bg-indigo-50/80 hover:shadow-md border-transparent dark:hover:bg-gray-900/80 dark:hover:shadow-indigo-900/10",
                isUnread && "shadow-indigo-100/60 dark:shadow-indigo-900/20",
                isUnread && "ring-2 ring-indigo-200/40 dark:ring-indigo-700/40"
            )}
            onClick={() => onSelect(chat)}
        >
            <div className="flex items-start space-x-3">
                <div className="relative">
                    <Avatar
                        className={cn(
                            "h-12 w-12 transition-all duration-300 border-2",
                            isSelected ? "border-indigo-500 dark:border-indigo-400" : "border-gray-200 group-hover:border-indigo-200 dark:border-gray-800 dark:group-hover:border-indigo-500",
                            "group-hover:shadow-lg dark:group-hover:shadow-indigo-900/20",
                            isLoading && "animate-pulse"
                        )}
                    >
                        <AvatarImage 
                            src={hasImageError || !profileImage ? null : profileImage}
                            alt={displayName}
                            onError={handleImageError}
                            onLoad={handleImageLoad}
                            className={cn(
                                "transition-opacity duration-300",
                                isLoading ? "opacity-0" : "opacity-100"
                            )}
                        />
                        <AvatarFallback className="bg-gradient-to-br from-indigo-100 to-violet-100 text-indigo-600 font-semibold dark:from-indigo-800 dark:to-violet-900 dark:text-indigo-200">
                            {displayName?.[0]?.toUpperCase() || '?'}
                        </AvatarFallback>
                    </Avatar>
                    {isFavorite && (
                        <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute -top-1 -right-1 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full p-1 shadow-lg dark:from-pink-600 dark:to-rose-600 dark:shadow-pink-900/30"
                        >
                            <HeartIcon className="w-3 h-3 text-white fill-current" />
                        </motion.div>
                    )}
                    {isUnread && (
                        <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: 'spring', stiffness: 600, damping: 20, repeat: Infinity, repeatType: 'reverse', duration: 0.8 }}
                            className="absolute -bottom-1 -right-1 bg-gradient-to-br from-indigo-500 via-violet-500 to-indigo-600 rounded-full p-1 shadow-lg ring-2 ring-indigo-300 animate-pulse dark:from-indigo-700 dark:via-violet-700 dark:to-indigo-800 dark:ring-indigo-700/40 dark:shadow-indigo-900/30"
                        >
                            <div className="w-2 h-2 bg-white rounded-full" />
                        </motion.div>
                    )}
                    {isUnread && (
                        <motion.div
                            className="absolute -bottom-3 -right-3 w-6 h-6 rounded-full bg-indigo-400/20 blur-xl z-0 dark:bg-indigo-800/30"
                            animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                            transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                        />
                    )}
                </div>

                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 max-w-[70%]">
                            <span className={cn(
                                "text-sm font-semibold truncate max-w-[180px] overflow-hidden text-ellipsis",
                                isUnread ? "text-indigo-700 font-extrabold dark:text-indigo-200" : "text-gray-700 dark:text-gray-100"
                            )} title={displayName}>
                                {displayName}
                                {displayUid && (
                                    <span className="ml-1 text-xs text-gray-500 font-normal dark:text-gray-400 truncate">#{displayUid}</span>
                                )}
                            </span>

                            {chat.metadata?.type === 'mission' && (
                                <Badge className="bg-gradient-to-r from-indigo-500 to-violet-500 text-white hover:from-indigo-600 hover:to-violet-600 transition-colors dark:from-indigo-700 dark:to-violet-700 flex-shrink-0">
                                    {t('list.missionBadge')}
                                </Badge>
                            )}
                        </div>
                        <time className="text-xs text-gray-400 tabular-nums dark:text-gray-500 flex-shrink-0 ml-2">
                            {formatTimeAgo(latestMessage.timestamp)}
                        </time>
                    </div>

                    <div className="mt-0.5 flex items-center justify-between">
                        <div className="flex items-center space-x-2 max-w-[75%]">
                        <p className={cn(
                                "text-xs truncate max-w-full overflow-hidden text-ellipsis whitespace-nowrap",
                                isUnread ? "font-bold text-indigo-900 dark:text-indigo-100" : "text-gray-500 dark:text-gray-400"
                        )} title={lastMessagePreview}>
                                {lastMessagePreview}
                        </p>
                            {latestMessage.status === 'sending' && (
                                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse dark:bg-indigo-400 flex-shrink-0" />
                            )}
                            {latestMessage.status === 'failed' && (
                                <div className="text-red-500 dark:text-red-400 flex-shrink-0" title={t('list.failedToSend')}>
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                </div>
                            )}
                        </div>
                        {isUnread && (
                            <Badge variant="default" className="ml-2 bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-sm animate-pulse dark:from-pink-600 dark:to-rose-600 dark:shadow-pink-900/30">
                                {chat.unread_count}
                            </Badge>
                        )}
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

export default React.memo(ChatList);
