import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import profileService from '../../services/profileService';
import { getCdnUrl } from '../../utils/cdnUtils';
import useTranslation from '../../hooks/useTranslation';

const AuthenticatedUserWelcome = () => {
    const { isAuthenticated, user } = useAuth();
    const { t } = useTranslation(['home', 'common']);
    const [profileData, setProfileData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Availability status constants
    const AVAILABILITY_RED = '#FF0000';
    const AVAILABILITY_YELLOW = '#FFFF00';
    const AVAILABILITY_GREEN = '#00FF00';

    useEffect(() => {
        const fetchProfileData = async () => {
            if (!isAuthenticated) {
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                
                const response = await profileService.getCompleteProfile();
                
                if (response.success) {
                    setProfileData(response.data);
                } else {
                    setError(response.error || 'Failed to load profile data');
                }
            } catch (err) {
                console.error('Error fetching profile data:', err);
                setError('Failed to load profile data');
            } finally {
                setLoading(false);
            }
        };

        fetchProfileData();
    }, [isAuthenticated]);

    // Don't render anything if user is not authenticated
    if (!isAuthenticated) {
        return null;
    }

    // Show nothing if the user's role is customer
    if (!loading && profileData?.role === 'customer') {
        return null;
    }

    // Show loading state with enhanced skeleton
    if (loading) {
        return (
            <motion.div
                initial={{ opacity: 0, y: -20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="relative mb-4 sm:mb-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-4 sm:p-6 shadow-2xl border border-indigo-100/40 dark:border-gray-800/40 overflow-hidden"
            >
                {/* Animated background patterns */}
                <div className="absolute inset-0 opacity-5 dark:opacity-10 overflow-hidden pointer-events-none">
                    <motion.div
                        className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full blur-xl"
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.1, 0.2, 0.1]
                        }}
                        transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    />
                    <motion.div
                        className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-pink-400 to-blue-500 rounded-full blur-xl"
                        animate={{
                            scale: [1.2, 1, 1.2],
                            opacity: [0.1, 0.2, 0.1]
                        }}
                        transition={{
                            duration: 6,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 2
                        }}
                    />
                </div>

                <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                    <div className="flex items-center space-x-3 sm:space-x-5">
                        <div className="relative flex-shrink-0">
                            <motion.div
                                className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-indigo-200/60 to-purple-300/60 dark:from-gray-600/60 dark:to-gray-700/60 rounded-full shadow-lg"
                                animate={{
                                    scale: [1, 1.05, 1],
                                    rotate: [0, 5, -5, 0]
                                }}
                                transition={{
                                    duration: 3,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            />
                            <motion.div
                                className="absolute -bottom-0.5 -right-0.5 sm:-bottom-1 sm:-right-1 w-4 h-4 sm:w-5 sm:h-5 bg-gradient-to-br from-blue-300/60 to-indigo-400/60 dark:from-gray-500/60 dark:to-gray-600/60 rounded-full shadow-md"
                                animate={{
                                    scale: [1, 1.1, 1],
                                    opacity: [0.6, 0.8, 0.6]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: 0.5
                                }}
                            />
                        </div>
                        <div className="flex-1 min-w-0 space-y-2 sm:space-y-3">
                            <motion.div
                                className="h-5 sm:h-6 bg-gradient-to-r from-indigo-200/60 via-purple-200/60 to-blue-200/60 dark:from-gray-600/60 dark:via-gray-700/60 dark:to-gray-600/60 rounded-xl w-32 sm:w-48 shadow-sm"
                                animate={{
                                    opacity: [0.6, 0.8, 0.6]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            />
                            <motion.div
                                className="h-3 sm:h-4 bg-gradient-to-r from-gray-200/60 to-gray-300/60 dark:from-gray-700/60 dark:to-gray-600/60 rounded-lg w-40 sm:w-64 shadow-sm"
                                animate={{
                                    opacity: [0.6, 0.8, 0.6]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: 0.3
                                }}
                            />
                        </div>
                    </div>
                    <motion.div
                        className="w-20 h-7 sm:w-24 sm:h-8 bg-gradient-to-r from-indigo-200/60 to-purple-200/60 dark:from-gray-600/60 dark:to-gray-700/60 rounded-full shadow-sm self-start sm:self-auto flex-shrink-0"
                        animate={{
                            opacity: [0.6, 0.8, 0.6],
                            scale: [1, 1.02, 1]
                        }}
                        transition={{
                            duration: 2.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.7
                        }}
                    />
                </div>
            </motion.div>
        );
    }

    // Show enhanced error state
    if (error) {
        return (
            <motion.div
                initial={{ opacity: 0, y: -20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="relative mb-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-red-200/40 dark:border-red-800/40 rounded-3xl p-6 shadow-2xl overflow-hidden"
            >
                {/* Animated error background effects */}
                <div className="absolute inset-0 opacity-5 dark:opacity-10 overflow-hidden pointer-events-none">
                    <motion.div
                        className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-red-400 to-orange-500 rounded-full blur-xl"
                        animate={{
                            scale: [1, 1.1, 1],
                            opacity: [0.1, 0.15, 0.1]
                        }}
                        transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    />
                    <motion.div
                        className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-pink-400 to-red-500 rounded-full blur-xl"
                        animate={{
                            scale: [1.1, 1, 1.1],
                            opacity: [0.1, 0.15, 0.1]
                        }}
                        transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 1.5
                        }}
                    />
                </div>

                <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                    <div className="flex items-center space-x-3 sm:space-x-5">
                        <motion.div
                            className="relative w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-red-100/80 to-red-200/80 dark:from-red-800/80 dark:to-red-900/80 rounded-full flex items-center justify-center shadow-xl border border-red-200/50 dark:border-red-700/50 flex-shrink-0"
                            whileHover={{ scale: 1.05, rotate: 5 }}
                            animate={{
                                boxShadow: [
                                    "0 10px 25px -5px rgba(239, 68, 68, 0.1)",
                                    "0 10px 25px -5px rgba(239, 68, 68, 0.2)",
                                    "0 10px 25px -5px rgba(239, 68, 68, 0.1)"
                                ]
                            }}
                            transition={{
                                boxShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                                hover: { duration: 0.2 }
                            }}
                        >
                            <motion.svg
                                className="w-6 h-6 sm:w-8 sm:h-8 text-red-600 dark:text-red-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                animate={{ rotate: [0, -5, 5, 0] }}
                                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </motion.svg>

                            {/* Pulsing ring effect */}
                            <motion.div
                                className="absolute inset-0 rounded-full border-2 border-red-400/30"
                                animate={{
                                    scale: [1, 1.2, 1],
                                    opacity: [0.3, 0, 0.3]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            />
                        </motion.div>
                        <div className="flex-1 min-w-0">
                            <motion.p
                                className="text-red-800 dark:text-red-200 font-bold text-lg sm:text-xl bg-gradient-to-r from-red-700 to-red-600 dark:from-red-300 dark:to-red-200 bg-clip-text text-transparent text-left"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.2, duration: 0.5 }}
                            >
                                Unable to load profile
                            </motion.p>
                            <motion.p
                                className="text-red-600 dark:text-red-400 text-xs sm:text-sm mt-1 font-medium text-left leading-relaxed"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                            >
                                {error}
                            </motion.p>
                        </div>
                    </div>
                    <motion.button
                        whileHover={{
                            scale: 1.05,
                            boxShadow: "0 10px 25px -5px rgba(239, 68, 68, 0.3)"
                        }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.location.reload()}
                        className="group relative px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-red-100 to-red-200 hover:from-red-200 hover:to-red-300 dark:from-red-800 dark:to-red-700 dark:hover:from-red-700 dark:hover:to-red-600 text-red-700 dark:text-red-200 rounded-xl text-xs sm:text-sm font-semibold transition-all duration-300 shadow-lg border border-red-200/50 dark:border-red-700/50 overflow-hidden self-start sm:self-auto flex-shrink-0"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4, duration: 0.5 }}
                    >
                        <span className="relative z-10 flex items-center">
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Retry
                        </span>

                        {/* Button shine effect */}
                        <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                            animate={{ x: ["-100%", "100%"] }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                                repeatDelay: 3
                            }}
                        />
                    </motion.button>
                </div>
            </motion.div>
        );
    }

    // Extract data from profile response
    const displayName = profileData?.nickname || user?.name || 'User';
    const profilePicture = profileData?.profile_picture;
    const availabilityStatus = profileData?.availability_status;

    // Get profile image URL
    const getProfileImageUrl = () => {
        if (profilePicture) {
            return getCdnUrl(profilePicture);
        }
        return '/images/profile-placeholder.svg'; // Fallback avatar
    };

    // Get availability status information based on hex codes
    const getAvailabilityInfo = () => {
        if (!availabilityStatus) {
            return {
                color: '#6B7280', // gray-500
                text: 'Offline',
                description: 'You appear offline',
                bgColor: 'bg-gray-100 dark:bg-gray-700/50',
                textColor: 'text-gray-700 dark:text-gray-300',
                gradientFrom: 'from-gray-50',
                gradientTo: 'to-gray-100',
                darkGradientFrom: 'dark:from-gray-800',
                darkGradientTo: 'dark:to-gray-700',
                icon: (
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                )
            };
        }

        switch (availabilityStatus.toUpperCase()) {
            case AVAILABILITY_RED:
                return {
                    color: '#EF4444', // Enhanced red for better visibility
                    text: 'Offline',
                    description: 'Currently offline, Please update your user availability in Profile, My Availability',
                    bgColor: 'bg-red-100 dark:bg-red-900/30',
                    textColor: 'text-red-700 dark:text-red-300',
                    gradientFrom: 'from-red-50',
                    gradientTo: 'to-red-100',
                    darkGradientFrom: 'dark:from-red-900/20',
                    darkGradientTo: 'dark:to-red-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                    )
                };
            case AVAILABILITY_YELLOW:
                return {
                    color: '#F59E0B', // Enhanced yellow/amber for better visibility
                    text: 'Busy',
                    description: 'In game or busy',
                    bgColor: 'bg-amber-100 dark:bg-amber-900/30',
                    textColor: 'text-amber-700 dark:text-amber-300',
                    gradientFrom: 'from-amber-50',
                    gradientTo: 'to-yellow-100',
                    darkGradientFrom: 'dark:from-amber-900/20',
                    darkGradientTo: 'dark:to-yellow-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                    )
                };
            case AVAILABILITY_GREEN:
                return {
                    color: '#10B981', // Enhanced green for better visibility
                    text: 'Online',
                    description: 'Ready for missions',
                    bgColor: 'bg-emerald-100 dark:bg-emerald-900/30',
                    textColor: 'text-emerald-700 dark:text-emerald-300',
                    gradientFrom: 'from-emerald-50',
                    gradientTo: 'to-green-100',
                    darkGradientFrom: 'dark:from-emerald-900/20',
                    darkGradientTo: 'dark:to-green-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    )
                };
            default:
                // If it's a hex color but not one of our predefined ones, treat as online
                return {
                    color: availabilityStatus,
                    text: 'Online',
                    description: 'Active',
                    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
                    textColor: 'text-blue-700 dark:text-blue-300',
                    gradientFrom: 'from-blue-50',
                    gradientTo: 'to-blue-100',
                    darkGradientFrom: 'dark:from-blue-900/20',
                    darkGradientTo: 'dark:to-blue-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    )
                };
        }
    };

    const availabilityInfo = getAvailabilityInfo();

    return (
        <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="relative mb-4 sm:mb-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-4 sm:p-5 shadow-lg dark:shadow-2xl border border-indigo-100/40 dark:border-gray-800/40 overflow-hidden group"
        >
            {/* Dynamic background patterns - Simplified for mobile */}
            <div className="absolute inset-0 opacity-5 dark:opacity-10 overflow-hidden pointer-events-none">
                <motion.div
                    className="absolute -top-6 -right-6 w-24 h-24 sm:w-32 sm:h-32 bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 rounded-full blur-xl sm:blur-2xl"
                    animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 180, 360],
                        opacity: [0.1, 0.2, 0.1]
                    }}
                    transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: "easeInOut"
                    }}
                />
                <motion.div
                    className="absolute -bottom-6 -left-6 w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-tr from-pink-400 via-purple-500 to-blue-600 rounded-full blur-xl sm:blur-2xl"
                    animate={{
                        scale: [1.2, 1, 1.2],
                        rotate: [360, 180, 0],
                        opacity: [0.1, 0.2, 0.1]
                    }}
                    transition={{
                        duration: 10,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 4
                    }}
                />

                {/* Floating particles - Reduced count on mobile */}
                {[...Array(4)].map((_, i) => (
                    <motion.div
                        key={`particle-${i}`}
                        className="absolute w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full"
                        style={{
                            left: `${15 + i * 20}%`,
                            top: `${25 + (i % 2) * 50}%`,
                        }}
                        animate={{
                            y: [0, -15, 0],
                            opacity: [0.2, 0.5, 0.2],
                            scale: [1, 1.2, 1]
                        }}
                        transition={{
                            duration: 3 + i * 0.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: i * 0.8
                        }}
                    />
                ))}
            </div>

            {/* Main content - Responsive layout */}
            <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-4 sm:space-x-5">
                    <div className="relative flex-shrink-0">
                        <motion.div
                            className="relative w-12 h-12 sm:w-14 md:w-16 sm:h-14 md:h-16 rounded-full overflow-hidden shadow-lg dark:shadow-2xl border-2 sm:border-3 border-white/50 dark:border-gray-700/50"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                        >
                            <motion.img
                                src={getProfileImageUrl()}
                                alt={`${displayName}'s profile`}
                                className="w-full h-full object-cover"
                                initial={{ scale: 1.1 }}
                                animate={{ scale: 1 }}
                                transition={{ duration: 0.6 }}
                                onError={(e) => {
                                    e.target.src = '/images/profile-placeholder.svg';
                                }}
                            />

                            {/* Profile picture glow effect */}
                            <motion.div
                                className="absolute inset-0 bg-gradient-to-br from-blue-400/20 via-transparent to-purple-500/20 rounded-full"
                                animate={{
                                    opacity: [0, 0.3, 0]
                                }}
                                transition={{
                                    duration: 3,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            />
                        </motion.div>

                        {/* Enhanced status indicator */}
                        <motion.div
                            className="absolute -bottom-0.5 -right-0.5 w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 border-white dark:border-gray-800 shadow-md"
                            style={{ backgroundColor: availabilityInfo.color }}
                            title={availabilityInfo.description}
                            animate={{
                                scale: [1, 1.2, 1],
                                boxShadow: [
                                    `0 0 0 0 ${availabilityInfo.color}40`,
                                    `0 0 0 4px ${availabilityInfo.color}10`,
                                    `0 0 0 0 ${availabilityInfo.color}40`
                                ]
                            }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                        >
                            <div className="w-full h-full rounded-full flex items-center justify-center">
                                <div className="text-white text-[8px] sm:text-xs">
                                    {availabilityInfo.icon}
                                </div>
                            </div>
                        </motion.div>

                        {/* Rotating ring around profile - hidden on mobile */}
                        <motion.div
                            className="absolute inset-0 w-12 h-12 sm:w-14 md:w-16 sm:h-14 md:h-16 rounded-full border-2 border-gradient-to-r from-blue-400 via-purple-500 to-pink-500 opacity-0 sm:opacity-20"
                            animate={{ rotate: 360 }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear"
                            }}
                        />
                    </div>

                    <div className="flex-1 min-w-0">
                        <motion.h2
                            className="text-base sm:text-lg md:text-xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent leading-tight text-left"
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2, duration: 0.6, ease: "easeOut" }}
                            whileHover={{
                                scale: 1.02,
                                textShadow: "0 0 20px rgba(59, 130, 246, 0.3)"
                            }}
                        >
                            Welcome back, {displayName}!
                        </motion.h2>
                        <motion.p
                            className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm mt-0.5 sm:mt-1 font-medium text-left leading-relaxed"
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3, duration: 0.6, ease: "easeOut" }}
                        >
                            {availabilityInfo.text === 'Online'
                                ? "Ready for your next mission?"
                                : availabilityInfo.text === 'Busy'
                                ? "We see you're busy!"
                                : "You're offline. Update status?"
                            }
                        </motion.p>
                    </div>
                </div>

                {/* Enhanced status badge - Responsive positioning */}
                <motion.div
                    className="relative px-3 py-1.5 sm:px-4 sm:py-2 rounded-full sm:rounded-xl text-xs sm:text-sm font-semibold shadow-md dark:shadow-lg border backdrop-blur-sm overflow-hidden self-start sm:self-auto flex-shrink-0 mt-2 sm:mt-0"
                    style={{
                        backgroundColor: `${availabilityInfo.color}15`,
                        borderColor: `${availabilityInfo.color}30`,
                        color: availabilityInfo.color
                    }}
                    whileHover={{
                        scale: 1.05,
                        boxShadow: `0 8px 20px -5px ${availabilityInfo.color}30`
                    }}
                    transition={{ duration: 0.2 }}
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4, duration: 0.6, ease: "easeOut" }}
                >
                    <span className="relative z-10 flex items-center">
                        <motion.div
                            className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full mr-1.5 sm:mr-2"
                            style={{ backgroundColor: availabilityInfo.color }}
                            animate={{
                                scale: [1, 1.3, 1],
                                opacity: [0.7, 1, 0.7]
                            }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                        />
                        {availabilityInfo.text}
                    </span>

                    {/* Badge shine effect */}
                    <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        animate={{ x: ["-100%", "100%"] }}
                        transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut",
                            repeatDelay: 4
                        }}
                    />
                </motion.div>
            </div>

            {/* Subtle border glow effect */}
            <motion.div
                className="absolute inset-0 rounded-3xl border border-gradient-to-r from-blue-400/20 via-purple-500/20 to-pink-500/20 pointer-events-none"
                animate={{
                    opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                }}
            />
        </motion.div>
    );
};

export default AuthenticatedUserWelcome;