import React from 'react';
import SkeletonBase from '../ui/SkeletonBase';

const HomeSkeleton = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white dark:from-gray-950 dark:via-blue-950 dark:to-gray-950">
      <div className="max-w-screen-2xl mx-auto px-8 py-8 space-y-12">
        {/* Welcome header */}
        <div className="flex items-center space-x-4">
          <SkeletonBase variant="avatar" className="w-12 h-12" />
          <div className="flex-1 space-y-2">
            <SkeletonBase variant="text" className="h-4 w-1/3" />
            <SkeletonBase variant="text" className="h-4 w-1/2" />
          </div>
        </div>

        {/* Hero banner */}
        <SkeletonBase variant="card" className="w-full h-64" />

        {/* Popular games section */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <SkeletonBase variant="text" className="h-6 w-40" />
            <SkeletonBase variant="text" className="h-6 w-6" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <SkeletonBase key={i} variant="card" className="h-40 w-full" />
            ))}
          </div>
        </div>

        {/* CTA banner */}
        <SkeletonBase variant="card" className="w-full h-48" />

        {/* Wallet section */}
        <div className="space-y-4">
          <SkeletonBase variant="text" className="h-6 w-32" />
          <SkeletonBase variant="card" className="h-60 w-full" />
        </div>

        {/* Recommended talents */}
        <div className="space-y-4">
          <SkeletonBase variant="text" className="h-6 w-56" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <SkeletonBase key={i} variant="card" className="h-56 w-full" />
            ))}
          </div>
        </div>

        {/* New talents */}
        <div className="space-y-4">
          <SkeletonBase variant="text" className="h-6 w-40" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <SkeletonBase key={i} variant="card" className="h-56 w-full" />
            ))}
          </div>
        </div>

        {/* Online talents */}
        <div className="space-y-4">
          <SkeletonBase variant="text" className="h-6 w-40" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <SkeletonBase key={i} variant="card" className="h-56 w-full" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomeSkeleton;
