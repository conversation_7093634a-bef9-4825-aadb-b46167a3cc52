import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import OrderToast from './OrderToast';
import { useNotifications } from '../../context/NotificationContext';

const ToastContainer = ({ onAcceptOrder, onRejectOrder }) => {
    const { notifications, removeNotification } = useNotifications();
    const [activeToasts, setActiveToasts] = useState([]);

    // Filter for toast-worthy notifications (urgent order notifications)
    useEffect(() => {
        const urgent = notifications.filter(notification => {
            const type = notification.data?.type || notification.data?.notification_type || '';
            // Show toasts for new orders and important status updates
            return (
                type === 'order_placed' ||
                type === 'new_order' ||
                type === 'order_accepted' ||
                type === 'order_rejected' ||
                type === 'order_completed' ||
                type === 'order_cancelled'
            ) && !notification.read;
        });

        // Ensure only one toast per order ID
        const seen = new Set();
        const unique = [];
        urgent.forEach(n => {
            const raw = n.data?.order_id || n.data?.orderId;
            const oid = raw != null ? String(raw) : null;
            if (oid) {
                if (seen.has(oid)) return;
                seen.add(oid);
            }
            unique.push(n);
        });

        setActiveToasts(unique);
    }, [notifications]);

    const handleDismissToast = (toastId) => {
        setActiveToasts(prevToasts =>
            prevToasts.filter(toast => toast.id !== toastId)
        );
        removeNotification(toastId);
    };

    const handleAcceptOrder = async (notificationId, orderId, orderType) => {
        if (onAcceptOrder) {
            try {
                await onAcceptOrder(orderId, orderType);
            } catch (err) {
                console.error('Failed to accept order toast action:', err);
            }
        }
        removeNotification(notificationId);
        setActiveToasts(prevToasts =>
            prevToasts.filter(toast => toast.id !== notificationId)
        );
    };

    const handleRejectOrder = async (notificationId, orderId, orderType) => {
        if (onRejectOrder) {
            try {
                await onRejectOrder(orderId, orderType);
            } catch (err) {
                console.error('Failed to reject order toast action:', err);
            }
        }
        removeNotification(notificationId);
        setActiveToasts(prevToasts =>
            prevToasts.filter(toast => toast.id !== notificationId)
        );
    };

    return (
        <div className="fixed top-4 right-4 z-[9999] space-y-4 pointer-events-none">
            <AnimatePresence>
                {activeToasts.map((notification, index) => {
                    const orderType = notification.data?.type || notification.data?.notification_type || '';
                    const isNewOrder =
                        orderType === 'order_placed' ||
                        orderType === 'new_order';
                    
                    return (
                        <div
                            key={notification.id}
                            className="pointer-events-auto"
                            style={{
                                zIndex: 1000 - index // Stack toasts properly
                            }}
                        >
                            <OrderToast
                                notification={notification}
                                onAccept={
                                    isNewOrder
                                        ? (id) => handleAcceptOrder(notification.id, id, orderType)
                                        : undefined
                                }
                                onReject={
                                    isNewOrder
                                        ? (id) => handleRejectOrder(notification.id, id, orderType)
                                        : undefined
                                }
                                onDismiss={() => handleDismissToast(notification.id)}
                                autoHide={!isNewOrder} // Don't auto-hide new orders
                                duration={isNewOrder ? 60000 : 5000} // New orders have 60s timer
                            />
                        </div>
                    );
                })}
            </AnimatePresence>
        </div>
    );
};

export default ToastContainer;
