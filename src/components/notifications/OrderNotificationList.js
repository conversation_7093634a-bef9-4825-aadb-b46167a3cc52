import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import OrderNotificationItem from './OrderNotificationItem';
import { InlineLoader } from '../ui/LoadingIndicator';
import { FaShoppingBag, FaFilter, FaSort, FaCheckDouble, FaTimes, FaBox, FaTruck, FaCheck, FaClock } from 'react-icons/fa';

const OrderNotificationList = ({ 
    notifications = [], 
    loading = false, 
    onMarkAsRead, 
    onDismiss, 
    onClearAll,
    onNotificationClick,
    maxHeight = '100%'
}) => {
    const [filter, setFilter] = useState('all'); // all, unread, delivered, processing, cancelled
    const [sortBy, setSortBy] = useState('newest'); // newest, oldest, status
    const [showFilters, setShowFilters] = useState(false);

    // Filter notifications to show only order-related ones
    const orderNotifications = notifications.filter(notification => {
        const type = notification.data?.type || notification.data?.notification_type || '';
        return type.includes('order') || notification.title?.toLowerCase().includes('order');
    });

    // Apply filters with enhanced filtering options
    const filteredNotifications = orderNotifications.filter(notification => {
        switch (filter) {
            case 'unread':
                return !notification.read;
            case 'delivered':
                return notification.title?.toLowerCase().includes('delivered') || 
                       notification.data?.status === 'delivered';
            case 'processing':
                return notification.title?.toLowerCase().includes('processing') || 
                       notification.data?.status === 'processing';
            case 'cancelled':
                return notification.title?.toLowerCase().includes('cancelled') || 
                       notification.data?.status === 'cancelled';
            default:
                return true;
        }
    });

    // Apply enhanced sorting
    const sortedNotifications = [...filteredNotifications].sort((a, b) => {
        const dateA = new Date(a.timestamp);
        const dateB = new Date(b.timestamp);
        
        switch (sortBy) {
            case 'oldest':
                return dateA - dateB;
            case 'status':
                const statusOrder = { 'processing': 1, 'delivered': 2, 'cancelled': 3 };
                const statusA = statusOrder[a.data?.status] || 4;
                const statusB = statusOrder[b.data?.status] || 4;
                return statusA - statusB;
            default: // newest
                return dateB - dateA;
        }
    });

    // Enhanced grouping with order status
    const groupedNotifications = sortedNotifications.reduce((groups, notification) => {
        const orderId = notification.data?.order_id || notification.data?.orderId || 'unknown';
        const status = notification.data?.status || 'pending';
        const groupKey = `${orderId}-${status}`;
        
        if (!groups[groupKey]) {
            groups[groupKey] = {
                orderId,
                status,
                notifications: []
            };
        }
        groups[groupKey].notifications.push(notification);
        return groups;
    }, {});

    const unreadCount = orderNotifications.filter(n => !n.read).length;
    const statusCounts = {
        delivered: orderNotifications.filter(n => n.data?.status === 'delivered').length,
        processing: orderNotifications.filter(n => n.data?.status === 'processing').length,
        cancelled: orderNotifications.filter(n => n.data?.status === 'cancelled').length
    };

    const handleMarkAllAsRead = () => {
        filteredNotifications.forEach(notification => {
            if (!notification.read) {
                onMarkAsRead(notification.id);
            }
        });
    };

    const handleClearAllOrders = () => {
        filteredNotifications.forEach(notification => {
            onDismiss(notification.id);
        });
        if (onClearAll) {
            onClearAll();
        }
    };

    const getFilterIcon = (filterType) => {
        switch (filterType) {
            case 'delivered': return <FaCheck className="w-3 h-3" />;
            case 'processing': return <FaClock className="w-3 h-3" />;
            case 'cancelled': return <FaTimes className="w-3 h-3" />;
            default: return <FaBox className="w-3 h-3" />;
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'delivered': return 'from-green-500 to-emerald-500';
            case 'processing': return 'from-blue-500 to-indigo-500';
            case 'cancelled': return 'from-red-500 to-pink-500';
            default: return 'from-gray-500 to-slate-500';
        }
    };

    if (loading) {
        return (
            <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex flex-col items-center justify-center py-12 px-6"
            >
                <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 rounded-2xl flex items-center justify-center shadow-lg">
                        <FaShoppingBag className="w-8 h-8 text-indigo-600 dark:text-indigo-300 animate-pulse" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <InlineLoader size="small" color="white" />
                    </div>
                </div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-gray-200 mb-2">Loading Orders</h3>
                <p className="text-gray-600 dark:text-gray-400 text-center">Fetching your order notifications...</p>
            </motion.div>
        );
    }

    return (
        <div className="order-notification-list flex flex-col h-full relative">
            {/* Enhanced Header with Modern Design */}
            <div className="bg-gradient-to-r from-indigo-50 via-blue-50 to-purple-50 dark:from-indigo-950 dark:via-blue-950 dark:to-purple-950 border-b border-indigo-200/50 dark:border-indigo-800/50 p-6 backdrop-blur-sm">
                {/* Title Section */}
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl shadow-lg">
                            <FaShoppingBag className="w-5 h-5 text-white" />
                        </div>
                        <div>
                            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                                Order Updates
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                {orderNotifications.length} total notifications
                            </p>
                        </div>
                    </div>

                    {/* Unread Badge */}
                    {unreadCount > 0 && (
                        <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="flex items-center space-x-2"
                        >
                            <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-lg animate-pulse">
                                {unreadCount} new
                            </div>
                        </motion.div>
                    )}
                </div>

                {/* Status Overview Cards */}
                <div className="grid grid-cols-3 gap-3 mb-4">
                    <motion.div 
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-3 border border-green-200/50 dark:border-green-800/50 shadow-sm"
                    >
                        <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                                <FaCheck className="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <p className="text-xs text-gray-600 dark:text-gray-300">Delivered</p>
                                <p className="text-lg font-bold text-green-600 dark:text-green-400">{statusCounts.delivered}</p>
                            </div>
                        </div>
                    </motion.div>

                    <motion.div 
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-3 border border-blue-200/50 dark:border-blue-800/50 shadow-sm"
                    >
                        <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                                <FaTruck className="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <p className="text-xs text-gray-600 dark:text-gray-300">Processing</p>
                                <p className="text-lg font-bold text-blue-600 dark:text-blue-400">{statusCounts.processing}</p>
                            </div>
                        </div>
                    </motion.div>

                    <motion.div 
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-3 border border-red-200/50 dark:border-red-800/50 shadow-sm"
                    >
                        <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                                <FaTimes className="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <p className="text-xs text-gray-600 dark:text-gray-300">Issues</p>
                                <p className="text-lg font-bold text-red-600 dark:text-red-400">{statusCounts.cancelled}</p>
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setShowFilters(!showFilters)}
                            className={`flex items-center space-x-2 px-4 py-2 rounded-xl font-bold text-sm transition-all duration-200 shadow-md backdrop-blur-sm ${
                                showFilters
                                    ? 'bg-indigo-500 text-white shadow-indigo-200 dark:shadow-indigo-900'
                                    : 'bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-900'
                            }`}
                        >
                            <FaFilter className="w-3 h-3" />
                            <span>Filters</span>
                        </motion.button>
                    </div>

                    <div className="flex items-center space-x-3">
                        {unreadCount > 0 && (
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={handleMarkAllAsRead}
                                className="flex items-center space-x-2 px-4 py-2 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-800/50 rounded-xl font-bold text-sm transition-all duration-200 shadow-md backdrop-blur-sm"
                            >
                                <FaCheckDouble className="w-3 h-3" />
                                <span>Mark all read</span>
                            </motion.button>
                        )}
                        
                        {orderNotifications.length > 0 && (
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={handleClearAllOrders}
                                className="flex items-center space-x-2 px-4 py-2 bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-800/50 rounded-xl font-bold text-sm transition-all duration-200 shadow-md backdrop-blur-sm"
                            >
                                <FaTimes className="w-3 h-3" />
                                <span>Clear all</span>
                            </motion.button>
                        )}
                    </div>
                </div>

                {/* Enhanced Filter Panel */}
                <AnimatePresence>
                    {showFilters && (
                        <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mt-4 overflow-hidden"
                        >
                            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-indigo-200/50 dark:border-indigo-800/50 shadow-lg">
                                <div className="grid grid-cols-2 gap-4">
                                    {/* Filter Options */}
                                    <div className="space-y-3">
                                        <label className="text-sm font-bold text-gray-700 dark:text-gray-300 flex items-center space-x-2">
                                            <FaFilter className="w-3 h-3" />
                                            <span>Filter by Status</span>
                                        </label>
                                        <div className="grid grid-cols-2 gap-2">
                                            {[
                                                { value: 'all', label: 'All Orders', icon: 'box' },
                                                { value: 'unread', label: 'Unread', icon: 'clock' },
                                                { value: 'delivered', label: 'Delivered', icon: 'check' },
                                                { value: 'processing', label: 'Processing', icon: 'truck' },
                                                { value: 'cancelled', label: 'Issues', icon: 'times' }
                                            ].map((option) => (
                                                <motion.button
                                                    key={option.value}
                                                    whileHover={{ scale: 1.02 }}
                                                    whileTap={{ scale: 0.98 }}
                                                    onClick={() => setFilter(option.value)}
                                                    className={`flex items-center space-x-2 px-3 py-2 text-xs font-bold rounded-lg transition-all duration-200 ${
                                                        filter === option.value
                                                            ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg'
                                                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-900'
                                                    }`}
                                                >
                                                    {getFilterIcon(option.value)}
                                                    <span>{option.label}</span>
                                                </motion.button>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Sort Options */}
                                    <div className="space-y-3">
                                        <label className="text-sm font-bold text-gray-700 dark:text-gray-300 flex items-center space-x-2">
                                            <FaSort className="w-3 h-3" />
                                            <span>Sort Order</span>
                                        </label>
                                        <div className="space-y-2">
                                            {[
                                                { value: 'newest', label: 'Newest First' },
                                                { value: 'oldest', label: 'Oldest First' },
                                                { value: 'status', label: 'By Status' }
                                            ].map((option) => (
                                                <motion.button
                                                    key={option.value}
                                                    whileHover={{ scale: 1.02 }}
                                                    whileTap={{ scale: 0.98 }}
                                                    onClick={() => setSortBy(option.value)}
                                                    className={`w-full flex items-center justify-center px-3 py-2 text-xs font-bold rounded-lg transition-all duration-200 ${
                                                        sortBy === option.value
                                                            ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg'
                                                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900'
                                                    }`}
                                                >
                                                    {option.label}
                                                </motion.button>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>

            {/* Enhanced Notification List */}
            <div 
                className="flex-1 overflow-y-auto px-4 py-4 space-y-3"
                style={{ maxHeight: maxHeight === '100%' ? 'calc(100vh - 400px)' : maxHeight }}
            >
                {sortedNotifications.length === 0 ? (
                    <motion.div 
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex flex-col items-center justify-center py-16 px-6 text-center"
                    >
                        <div className="relative mb-6">
                            <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 rounded-3xl flex items-center justify-center shadow-lg">
                                <FaShoppingBag className="w-12 h-12 text-indigo-400 dark:text-indigo-500" />
                            </div>
                            <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700 rounded-full flex items-center justify-center shadow-lg">
                                <FaCheck className="w-4 h-4 text-white" />
                            </div>
                        </div>
                        
                        <h4 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-3">
                            {filter === 'unread' ? 'All caught up!' : 'No order notifications'}
                        </h4>
                        
                        <p className="text-gray-600 dark:text-gray-400 leading-relaxed max-w-md">
                            {filter === 'unread' 
                                ? "Great job! You've read all your order notifications. New updates will appear here when they arrive."
                                : "Your order notifications will appear here. You'll be notified about order confirmations, shipping updates, and delivery status."
                            }
                        </p>

                        {filter !== 'all' && (
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => setFilter('all')}
                                className="mt-6 px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                            >
                                View All Notifications
                            </motion.button>
                        )}
                    </motion.div>
                ) : (
                    <AnimatePresence>
                        {sortedNotifications.map((notification, index) => {
                            // Ensure unique key by combining multiple identifiers
                            // Handle empty strings, null, undefined, and other falsy values
                            const notificationId = notification.id && notification.id.toString().trim();
                            const messageId = notification.data?.message_id && notification.data.message_id.toString().trim();
                            const notificationDataId = notification.data?.notification_id && notification.data.notification_id.toString().trim();
                            const orderId = notification.data?.order_id && notification.data.order_id.toString().trim();
                            const timestamp = notification.timestamp;

                            const uniqueKey = notificationId || messageId || notificationDataId || orderId || `order-notification-${timestamp}-${index}` || `order-fallback-${Date.now()}-${index}`;
                            return (
                            <motion.div
                                key={`order-${uniqueKey}`}
                                initial={{ opacity: 0, x: 20, scale: 0.95 }}
                                animate={{ opacity: 1, x: 0, scale: 1 }}
                                exit={{ opacity: 0, x: -20, scale: 0.95 }}
                                transition={{ duration: 0.3, delay: index * 0.05 }}
                            >
                                <OrderNotificationItem
                                    notification={notification}
                                    onMarkAsRead={onMarkAsRead}
                                    onDismiss={onDismiss}
                                    onClick={onNotificationClick}
                                />
                            </motion.div>
                            );
                        })}
                    </AnimatePresence>
                )}
            </div>

            {/* Enhanced Footer Summary */}
            {sortedNotifications.length > 0 && (
                <div className="bg-gradient-to-r from-gray-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-indigo-950 dark:to-purple-950 border-t border-indigo-200/50 dark:border-indigo-800/50 px-6 py-4 backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                Showing {sortedNotifications.length} of {orderNotifications.length}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400 bg-white/60 dark:bg-gray-800/60 px-3 py-1 rounded-full backdrop-blur-sm">
                                {filter === 'all' ? 'All orders' : `Filtered by ${filter}`}
                            </span>
                        </div>
                        
                        {unreadCount > 0 && (
                            <div className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                <span className="text-sm font-bold text-red-600 dark:text-red-400">
                                    {unreadCount} unread
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default OrderNotificationList;