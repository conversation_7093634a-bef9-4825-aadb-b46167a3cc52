import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useNotifications } from '../../context/NotificationContext';
import { useNavigate } from 'react-router-dom';
import OrderNotificationList from './OrderNotificationList';
import { InlineLoader } from '../ui/LoadingIndicator';
import { useAuth } from '../../contexts/AuthContext';
import { formatRelativeTime } from '../../utils/i18nUtils';
import GiftNotificationCard from './GiftNotificationCard';
import { FaCheckCircle, FaTrash, FaBell, FaTimes, FaShoppingBag, FaComments, FaList } from 'react-icons/fa';

const NotificationDropdown = ({ isOpen, onClose, onToggle }) => {
    const { isAuthenticated } = useAuth();
    const {
        notifications,
        unreadCount,
        markAsRead,
        removeNotification,
        deleteNotification,
        getNotificationIcon,
        getNotificationRoute
    } = useNotifications();
    const navigate = useNavigate();

    const [activeTab, setActiveTab] = useState('all'); // all, orders, messages
    const [loading, setLoading] = useState(false);
    const panelRef = useRef(null);

    // Close panel when clicking outside or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (panelRef.current && !panelRef.current.contains(event.target)) {
                onClose();
            }
        };

        const handleEscapeKey = (event) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            document.addEventListener('keydown', handleEscapeKey);
            document.body.style.overflow = 'hidden'; // Prevent background scroll
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleEscapeKey);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    // Filter notifications by tab
    const getFilteredNotifications = () => {
        switch (activeTab) {
            case 'orders':
                return notifications.filter(n => {
                    const type = n.data?.type || n.data?.notification_type || '';
                    return type.includes('order') || n.title?.toLowerCase().includes('order');
                });
            case 'messages':
                return notifications.filter(n => {
                    const type = n.data?.type || n.data?.notification_type || n.category || '';
                    return type === 'chat_message' || n.category === 'chat';
                });
            default:
                return notifications;
        }
    };

    const filteredNotifications = getFilteredNotifications();
    const orderNotifications = notifications.filter(n => {
        const type = n.data?.type || n.data?.notification_type || '';
        return type.includes('order') || n.title?.toLowerCase().includes('order');
    });
    const chatNotifications = notifications.filter(n => {
        const type = n.data?.type || n.data?.notification_type || n.category || '';
        return type === 'chat_message' || n.category === 'chat';
    });

    const orderUnreadCount = orderNotifications.filter(n => !n.read).length;
    const chatUnreadCount = chatNotifications.filter(n => !n.read).length;

    const handleNotificationClick = (notification) => {
        if (!notification.read) {
            markAsRead(notification.id);
        }
        const route = getNotificationRoute(notification);
        if (route) {
            navigate(route);
        }
        onClose();
    };

    const handleMarkAllAsRead = () => {
        filteredNotifications.forEach(notification => {
            if (!notification.read) {
                markAsRead(notification.id);
            }
        });
    };

    const handleClearAll = () => {
        filteredNotifications.forEach(notification => {
            removeNotification(notification.id);
        });
    };

    if (!isAuthenticated) return null;

    // Render the notification panel using React Portal to escape all container constraints
    if (!isOpen) return null;

    const notificationPanel = (
        <AnimatePresence>
            {/* Global Backdrop Overlay - Rendered directly to body */}
            <motion.div
                key="notification-backdrop"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="fixed inset-0 bg-black/30 backdrop-blur-sm"
                onClick={onClose}
                style={{ 
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 999998,
                    pointerEvents: 'auto'
                }}
            />

            {/* Global Slide-in Notification Panel - Rendered directly to body */}
            <motion.div
                key="notification-panel"
                ref={panelRef}
                initial={{ x: '100%', opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: '100%', opacity: 0 }}
                transition={{ 
                    type: "spring", 
                    stiffness: 300, 
                    damping: 30,
                    duration: 0.4
                }}
                className="fixed top-0 right-0 h-full w-[32rem] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl flex flex-col overflow-hidden border-l border-indigo-200/50 dark:border-indigo-800/50"
                style={{ 
                    position: 'fixed',
                    top: 0,
                    right: 0,
                    bottom: 0,
                    width: '32rem', // 384px
                    zIndex: 999999,
                    transform: 'translateZ(0)', // Force hardware acceleration
                    pointerEvents: 'auto'
                }}
            >
                        {/* Gradient Background Effects */}
                        <div className="absolute inset-0 pointer-events-none">
                            <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/80 via-blue-50/60 to-purple-50/40 dark:from-indigo-950/80 dark:via-blue-950/60 dark:to-purple-950/40" />
                            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-indigo-300/20 via-transparent to-transparent dark:from-indigo-600/20 rounded-full blur-3xl transform translate-x-48 -translate-y-48" />
                            <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-purple-300/20 via-transparent to-transparent dark:from-purple-600/20 rounded-full blur-3xl transform -translate-x-32 translate-y-32" />
                        </div>

                        {/* Header */}
                        <div className="relative z-10 bg-gradient-to-r from-indigo-600 via-blue-600 to-purple-600 dark:from-indigo-800 dark:via-blue-800 dark:to-purple-800 px-6 py-6 border-b border-indigo-200/50 dark:border-indigo-800/50 shadow-lg">
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-white/20 dark:bg-black/20 rounded-full backdrop-blur-sm">
                                        <FaBell className="w-6 h-6 text-white drop-shadow-sm" />
                                    </div>
                                    <div>
                                        <h2 className="text-xl font-bold text-white drop-shadow-sm">
                                            Notifications
                                        </h2>
                                        {unreadCount > 0 && (
                                            <p className="text-indigo-100 text-sm">
                                                {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                
                                <button
                                    onClick={onClose}
                                    className="p-2 bg-white/20 dark:bg-black/20 hover:bg-white/30 dark:hover:bg-black/30 rounded-full transition-all duration-200 backdrop-blur-sm shadow-lg hover:shadow-xl hover:scale-105"
                                    aria-label="Close notifications"
                                >
                                    <FaTimes className="w-5 h-5 text-white" />
                                </button>
                            </div>

                            {/* Enhanced Tab Navigation */}
                            <div className="flex space-x-2 bg-white/20 dark:bg-black/20 rounded-xl p-1.5 backdrop-blur-sm shadow-inner">
                                <button
                                    onClick={() => setActiveTab('all')}
                                    className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-bold rounded-lg transition-all duration-300 ${
                                        activeTab === 'all'
                                            ? 'bg-white dark:bg-gray-800 text-indigo-700 dark:text-indigo-300 shadow-lg transform scale-105'
                                            : 'text-white/80 hover:text-white hover:bg-white/10 dark:hover:bg-black/10'
                                    }`}
                                >
                                    <FaList className="w-4 h-4" />
                                    <span>All</span>
                                    <span className="bg-indigo-500 dark:bg-indigo-600 text-white text-xs px-2 py-0.5 rounded-full">
                                        {notifications.length}
                                    </span>
                                </button>
                                
                                <button
                                    onClick={() => setActiveTab('orders')}
                                    className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-bold rounded-lg transition-all duration-300 relative ${
                                        activeTab === 'orders'
                                            ? 'bg-white dark:bg-gray-800 text-indigo-700 dark:text-indigo-300 shadow-lg transform scale-105'
                                            : 'text-white/80 hover:text-white hover:bg-white/10 dark:hover:bg-black/10'
                                    }`}
                                >
                                    <FaShoppingBag className="w-4 h-4" />
                                    <span>Orders</span>
                                    <span className="bg-green-500 dark:bg-green-600 text-white text-xs px-2 py-0.5 rounded-full">
                                        {orderNotifications.length}
                                    </span>
                                    {orderUnreadCount > 0 && (
                                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse shadow-lg" />
                                    )}
                                </button>
                                
                                <button
                                    onClick={() => setActiveTab('messages')}
                                    className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-bold rounded-lg transition-all duration-300 relative ${
                                        activeTab === 'messages'
                                            ? 'bg-white dark:bg-gray-800 text-indigo-700 dark:text-indigo-300 shadow-lg transform scale-105'
                                            : 'text-white/80 hover:text-white hover:bg-white/10 dark:hover:bg-black/10'
                                    }`}
                                >
                                    <FaComments className="w-4 h-4" />
                                    <span>Messages</span>
                                    <span className="bg-purple-500 dark:bg-purple-600 text-white text-xs px-2 py-0.5 rounded-full">
                                        {chatNotifications.length}
                                    </span>
                                    {chatUnreadCount > 0 && (
                                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse shadow-lg" />
                                    )}
                                </button>
                            </div>
                        </div>

                        {/* Content Area */}
                        <div className="flex-1 flex flex-col overflow-hidden relative z-10">
                            {activeTab === 'orders' ? (
                                <OrderNotificationList
                                    notifications={orderNotifications}
                                    loading={loading}
                                    onMarkAsRead={markAsRead}
                                    onDismiss={removeNotification}
                                    onClearAll={handleClearAll}
                                    onNotificationClick={handleNotificationClick}
                                    maxHeight="100%"
                                />
                            ) : (
                                <>
                                    {/* Action Bar */}
                                    {filteredNotifications.length > 0 && (
                                        <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-b border-indigo-200/50 dark:border-indigo-800/50 px-6 py-4">
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                                    {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''}
                                                </span>
                                                <div className="flex items-center space-x-3">
                                                    {filteredNotifications.some(n => !n.read) && (
                                                        <motion.button
                                                            whileHover={{ scale: 1.05 }}
                                                            whileTap={{ scale: 0.95 }}
                                                            onClick={handleMarkAllAsRead}
                                                            className="text-sm font-bold text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/50 hover:bg-green-200 dark:hover:bg-green-800/50 px-4 py-2 rounded-lg shadow-md transition-all duration-200 backdrop-blur-sm"
                                                        >
                                                            Mark all read
                                                        </motion.button>
                                                    )}
                                                    <motion.button
                                                        whileHover={{ scale: 1.05 }}
                                                        whileTap={{ scale: 0.95 }}
                                                        onClick={handleClearAll}
                                                        className="text-sm font-bold text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/50 hover:bg-red-200 dark:hover:bg-red-800/50 px-4 py-2 rounded-lg shadow-md transition-all duration-200 backdrop-blur-sm"
                                                    >
                                                        Clear all
                                                    </motion.button>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Notifications List */}
                                    <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
                                        {filteredNotifications.length === 0 ? (
                                            <motion.div 
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.5 }}
                                                className="flex flex-col items-center justify-center py-20 px-6 text-center"
                                            >
                                                <div className="w-32 h-32 mb-6 relative">
                                                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-200 to-purple-200 dark:from-indigo-800 dark:to-purple-800 rounded-full opacity-20 animate-pulse" />
                                                    <div className="absolute inset-4 bg-gradient-to-br from-indigo-300 to-purple-300 dark:from-indigo-700 dark:to-purple-700 rounded-full flex items-center justify-center">
                                                        <FaBell className="w-12 h-12 text-indigo-600 dark:text-indigo-300" />
                                                    </div>
                                                </div>
                                                <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-3">
                                                    All caught up!
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                                                    No new notifications to show. You're up to date with everything!
                                                </p>
                                            </motion.div>
                                        ) : (
                                            <AnimatePresence>
                                                {filteredNotifications.map((notification, index) => {
                                                    // Ensure unique key by combining multiple identifiers
                                                    // Handle empty strings, null, undefined, and other falsy values
                                                    const notificationId = notification.id && notification.id.toString().trim();
                                                    const messageId = notification.data?.message_id && notification.data.message_id.toString().trim();
                                                    const notificationDataId = notification.data?.notification_id && notification.data.notification_id.toString().trim();
                                                    const timestamp = notification.timestamp;

                                                    const uniqueKey = notificationId || messageId || notificationDataId || `notification-${timestamp}-${index}` || `fallback-${Date.now()}-${index}`;

                                                    if (notification.data?.type === 'gift') {
                                                        return (
                                                            <motion.div
                                                                key={`gift-${uniqueKey}`}
                                                                initial={{ opacity: 0, x: 50 }}
                                                                animate={{ opacity: 1, x: 0 }}
                                                                exit={{ opacity: 0, x: -50 }}
                                                                transition={{ duration: 0.3, delay: index * 0.05 }}
                                                                className="bg-gradient-to-br from-pink-50 to-purple-50 dark:from-pink-950/30 dark:to-purple-950/30 rounded-2xl p-6 shadow-lg border border-pink-200/50 dark:border-pink-800/50 backdrop-blur-sm"
                                                            >
                                                                {/* Gift notification content would go here */}
                                                            </motion.div>
                                                        );
                                                    }

                                                    return (
                                                        <motion.div
                                                            key={`notification-${uniqueKey}`}
                                                            initial={{ opacity: 0, x: 50 }}
                                                            animate={{ opacity: 1, x: 0 }}
                                                            exit={{ opacity: 0, x: -50 }}
                                                            transition={{ duration: 0.3, delay: index * 0.05 }}
                                                            whileHover={{ scale: 1.02, y: -2 }}
                                                            className={`group relative cursor-pointer rounded-2xl p-5 shadow-lg backdrop-blur-sm border transition-all duration-300 hover:shadow-xl ${
                                                                !notification.read
                                                                    ? 'bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 dark:from-blue-950/40 dark:via-indigo-950/30 dark:to-purple-950/20 border-indigo-200/60 dark:border-indigo-700/60 shadow-indigo-100/50 dark:shadow-indigo-900/50'
                                                                    : 'bg-white/80 dark:bg-gray-800/80 border-gray-200/50 dark:border-gray-700/50'
                                                            }`}
                                                            onClick={() => handleNotificationClick(notification)}
                                                        >
                                                            {/* Unread indicator */}
                                                            {!notification.read && (
                                                                <div className="absolute top-4 left-4 w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full shadow-lg animate-pulse" />
                                                            )}

                                                            <div className="flex items-start space-x-4 ml-6">
                                                                <div className="flex-shrink-0 mt-1">
                                                                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 dark:from-indigo-600 dark:to-purple-600 rounded-xl flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                                                                        {getNotificationIcon(notification.data?.type)}
                                                                    </div>
                                                                </div>
                                                                
                                                                <div className="flex-1 min-w-0">
                                                                    <h4 className="text-base font-bold text-gray-900 dark:text-gray-100 mb-1 group-hover:text-indigo-700 dark:group-hover:text-indigo-300 transition-colors">
                                                                        {notification.title}
                                                                    </h4>
                                                                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 leading-relaxed">
                                                                        {notification.body}
                                                                    </p>
                                                                    <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                                                        {formatRelativeTime(notification.timestamp)}
                                                                    </p>
                                                                </div>

                                                                <div className="flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                                                    <motion.button
                                                                        whileHover={{ scale: 1.1 }}
                                                                        whileTap={{ scale: 0.9 }}
                                                                        className={`p-2 rounded-lg transition-all duration-200 shadow-md ${
                                                                            notification.read 
                                                                                ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-600'
                                                                                : 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-800/50'
                                                                        }`}
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            if (notification.read) {
                                                                                removeNotification(notification.id);
                                                                            } else {
                                                                                markAsRead(notification.id);
                                                                            }
                                                                        }}
                                                                        aria-label={notification.read ? 'Remove notification' : 'Mark as read'}
                                                                    >
                                                                        <FaCheckCircle className="w-4 h-4" />
                                                                    </motion.button>
                                                                    
                                                                    <motion.button
                                                                        whileHover={{ scale: 1.1 }}
                                                                        whileTap={{ scale: 0.9 }}
                                                                        className="p-2 bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800/50 rounded-lg transition-all duration-200 shadow-md"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            deleteNotification(notification.id);
                                                                        }}
                                                                        aria-label="Delete notification"
                                                                    >
                                                                        <FaTrash className="w-4 h-4" />
                                                                    </motion.button>
                                                                </div>
                                                            </div>
                                                        </motion.div>
                                                    );
                                                })}
                                            </AnimatePresence>
                                        )}
                                    </div>
                                </>
                            )}
                </div>
            </motion.div>
        </AnimatePresence>
    );

    // Use React Portal to render directly to document.body, completely outside MainNavigation.js
    return createPortal(notificationPanel, document.body);
};

export default NotificationDropdown;