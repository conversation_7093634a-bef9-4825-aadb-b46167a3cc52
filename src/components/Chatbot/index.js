import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../features/wallet/contexts/WalletContext';
import { useProfile } from '../../contexts/ProfileContext';
import useGlobalBalance from '../../hooks/useGlobalBalance';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import TypingText from './TypingText';
import { useAI } from '../../contexts/AIContext';
import useTranslation from '../../hooks/useTranslation';
import { processAIMessage } from './aiFeatures';

const Chatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { ai, updateAIContext } = useAI();
  const { t } = useTranslation('chatbot');

  // Enhanced context hooks
  const { user, isAuthenticated } = useAuth();
  const { transactions } = useWallet();
  const { profile, services, availability } = useProfile();
  const { balance } = useGlobalBalance({ autoLoad: isAuthenticated });
  const initialMessages = useMemo(() => [
    {
      text: t('greeting.initial'),
      sender: 'bot',
      commands: [
        { label: t('commands.help'), command: '!help' },
        { label: t('commands.balance'), command: '!balance' },
        { label: t('commands.find'), command: '!find' },
        { label: t('commands.post'), command: '!post' },
        { label: t('commands.missions'), command: '!missions' },
        { label: t('commands.settings'), command: 'settings' }
      ]
    }
  ], [t]);

  const [messages, setMessages] = useState(initialMessages);
  const [showWelcome, setShowWelcome] = useState(false);
  const [botTyping, setBotTyping] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);
  const [incomingMessage, setIncomingMessage] = useState('');
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Keep AI context synchronized with latest user data
  useEffect(() => {
    updateAIContext({
      user,
      profile,
      wallet: { balance, transactions },
      services,
      availability,
      isAuthenticated
    });
  }, [user, profile, balance, transactions, services, availability, isAuthenticated, updateAIContext]);

  useEffect(() => {
    const handlePrebooked = (e) => {
      const { customerName, body } = e.detail || {};
      const nickname = profile?.nickname || user?.name || '';
      const text = t('notifications.prebookedRequest', {
        nickname,
        customerName,
      });
      setMessages((prev) => [...prev, { text, sender: 'bot' }]);
      setIncomingMessage(body || text);
      setHasNewMessage(true);
      setUnreadCount((c) => c + 1);
    };

    window.addEventListener('prebooked-order-request', handlePrebooked);
    return () => window.removeEventListener('prebooked-order-request', handlePrebooked);
  }, [profile, user, t]);

  useEffect(() => {
    const handleNewOrder = (e) => {
      const { customerName, body } = e.detail || {};
      const nickname = profile?.nickname || user?.name || '';
      const text = t('notifications.newOrder', {
        nickname,
        customerName,
      });
      setMessages((prev) => [...prev, { text, sender: 'bot' }]);
      setIncomingMessage(body || text);
      setHasNewMessage(true);
      setUnreadCount((c) => c + 1);
    };

    window.addEventListener('new-order', handleNewOrder);
    return () => window.removeEventListener('new-order', handleNewOrder);
  }, [profile, user, t]);

  useEffect(() => {
    const handleWelcome = (e) => {
      const { body } = e.detail || {};
      const nickname = profile?.nickname || user?.name || '';
      const text =
        body ||
        `Hello ${nickname}, welcome back to mission x, i missed you, wanna hop on some talents or missions?`;
      setMessages((prev) => [...prev, { text, sender: 'bot' }]);
      setIncomingMessage(body || text);
      setHasNewMessage(true);
      setUnreadCount((c) => c + 1);
    };

    window.addEventListener('chatbot-welcome', handleWelcome);
    return () => window.removeEventListener('chatbot-welcome', handleWelcome);
  }, [profile, user]);

  useEffect(() => {
    const handleOrderSummary = (e) => {
      const { body } = e.detail || {};
      const text = body || 'You have pending orders waiting for you.';
      setMessages((prev) => [...prev, { text, sender: 'bot' }]);
      setIncomingMessage(body || text);
      setHasNewMessage(true);
      setUnreadCount((c) => c + 1);
    };

    window.addEventListener('chatbot-order-summary', handleOrderSummary);
    return () => window.removeEventListener('chatbot-order-summary', handleOrderSummary);
  }, []);

  useEffect(() => {
    if (isOpen) {
      setHasNewMessage(false);
      setIncomingMessage('');
      setUnreadCount(0);
    }
  }, [isOpen]);

  // Enhanced AI response function
  const getEnhancedBotResponse = async (userInput) => {
    try {
      // Get AI response using centralized context
      const aiResponse = await ai.processMessage(userInput);

      return {
        text: aiResponse.text,
        type: aiResponse.type || 'default',
        actions: aiResponse.actions,
        suggestions: aiResponse.suggestions,
        commands: aiResponse.commands,
        checklist: aiResponse.checklist,
        allDone: aiResponse.allDone
      };
    } catch (error) {
      console.error('Enhanced AI Error:', error);
      // Fallback to basic response
      return getBasicBotResponse(userInput);
    }
  };

  // Keep basic responses as fallback
  const getBasicBotResponse = (userInput) => {
    const text = userInput.toLowerCase().trim();

    if (['hi', 'hello', 'hey'].some((greet) => text.startsWith(greet))) {
      return { text: t('basic.greeting'), type: 'greeting' };
    }
    if (text.includes('thank')) {
      return { text: t('basic.thanks'), type: 'default' };
    }
    if (text.includes('post') || text.includes('where to post')) {
      return { text: t('basic.post'), type: 'default' };
    }
    if (text.includes('mission') || text.includes('dashboard') || text.includes('jobs')) {
      return { text: t('basic.missions'), type: 'default' };
    }
    if (text.includes('balance') || text.includes('credit') || text.includes('wallet')) {
      return {
        text: t('basic.balance'),
        type: 'default'
      };
    }
    if (
      (text.includes('find') || text.includes('hire') || text.includes('coach')) &&
      text.includes('talent') &&
      !text.includes('how to be') &&
      !text.includes('how to become')
    ) {
      return {
        text: t('basic.findTalent'),
        type: 'default'
      };
    }
    if (
      text.includes('how to be a talent') ||
      text.includes('how to become a talent') ||
      (text.includes('be a talent') && text.includes('how')) ||
      text.includes('become a talent')
    ) {
      const servicesList = profile?.services || profile?.service || services || [];
      const hasProfilePicture = !!profile?.profile_picture;
      const photos = profile?.profile_media?.photos || [];
      const hasCoverMedia = Array.isArray(photos) && photos.length > 0 && photos.every(p => p?.path);
      const hasBiography = !!profile?.biography;
      const hasApprovedService = Array.isArray(servicesList) && servicesList.some(s => s.status === 'approved');

      const checklist = [
        { label: t('checklist.profilePicture'), done: hasProfilePicture },
        { label: t('checklist.coverMedia'), done: hasCoverMedia },
        { label: t('checklist.biography'), done: hasBiography },
        { label: t('checklist.approvedService'), done: hasApprovedService }
      ];

      const allDone = checklist.every(item => item.done);

      return {
        text: t('basic.talentChecklistIntro'),
        type: 'talent-checklist',
        checklist,
        allDone
      };
    }
    if (text.includes('what') && text.includes('order')) {
      const pending = profile?.pending_order?.length || 0;
      const accepted = profile?.accepted_order?.length || 0;
      const inProg = profile?.inprogress_order?.length || 0;
      return {
        text: `Here's the latest: ${pending} pending, ${accepted} accepted and ${inProg} in-progress orders.`,
        type: 'data'
      };
    }
    if (text.includes('accepted') && text.includes('order')) {
      const list = profile?.accepted_order || [];
      if (list.length) {
        const ids = list.map(o => `#${o.id}`).join(', ');
        return {
          text: `You've accepted ${list.length} order${list.length > 1 ? 's' : ''}: ${ids}.`,
          type: 'data'
        };
      }
      return { text: 'No accepted orders at the moment.', type: 'data' };
    }
    if ((text.includes('in progress') || text.includes('in-progress')) && text.includes('order')) {
      const list = profile?.inprogress_order || [];
      if (list.length) {
        const ids = list.map(o => `#${o.id}`).join(', ');
        return {
          text: `Currently working on ${list.length} order${list.length > 1 ? 's' : ''}: ${ids}.`,
          type: 'data'
        };
      }
      return { text: 'You have no orders in progress.', type: 'data' };
    }
    if (text.includes('availability')) {
      return { text: t('basic.availability'), type: 'default' };
    }
    if (text.includes('service')) {
      return { text: t('basic.service'), type: 'default' };
    }
    if (text.includes('help') || text.includes('support') || text.includes('issue')) {
      return { text: t('basic.help'), type: 'help' };
    }

    const fallbacks = [
      t('basic.fallback1'),
      t('basic.fallback2'),
      t('basic.fallback3')
    ];
    return { text: fallbacks[Math.floor(Math.random() * fallbacks.length)], type: 'fallback' };
  };

  // Enhanced message handler with AI integration
  const handleSendMessage = async (userText) => {
    // Add user message and typing indicator
    setMessages((prev) => [
      ...prev,
      { text: userText, sender: 'user' },
      { text: '...', sender: 'bot', typing: true }
    ]);
    setBotTyping(true);

    const input = userText.toLowerCase().trim();

    try {
      // First check for navigation commands (keep existing functionality)
      const navigationResult = handleNavigationCommands(input);

      if (navigationResult.shouldNavigate) {
        // Handle navigation
        setTimeout(() => {
          if (navigationResult.navigationAction) {
            navigationResult.navigationAction();
          }
          setMessages((prev) => [
            ...prev.slice(0, -1),
            {
              text: navigationResult.botResponse,
              sender: 'bot',
              type: 'navigation'
            }
          ]);
          setBotTyping(false);
        }, 1000);
        return;
      }

      // AI feature-specific intents
      const featureResponse = await processAIMessage(userText, { t, pendingAction, setPendingAction });
      if (featureResponse) {
        setTimeout(() => {
          setMessages((prev) => [
            ...prev.slice(0, -1),
            {
              ...featureResponse,
              sender: 'bot'
            }
          ]);
          setBotTyping(false);
        }, 1000);
        return;
      }

      // Use enhanced AI for other responses
      const aiResponse = await getEnhancedBotResponse(userText);

      setTimeout(() => {
        setMessages((prev) => [
          ...prev.slice(0, -1),
          {
            ...aiResponse,
            sender: 'bot'
          }
        ]);
        setBotTyping(false);
      }, 1000);

    } catch (error) {
      console.error('Message handling error:', error);
      // Fallback response
      setTimeout(() => {
        setMessages((prev) => [
          ...prev.slice(0, -1),
          {
            text: t('errors.processing'),
            sender: 'bot',
            type: 'error'
          }
        ]);
        setBotTyping(false);
      }, 1000);
    }
  };

  // Separate navigation handling (keeping existing functionality)
  const handleNavigationCommands = (input) => {
    let shouldNavigate = false;
    let navigationAction = null;
    let botResponse = '';

    // Check for navigation commands
    if (
      input.includes('explore') ||
      input.includes('where to post') ||
      input.includes('post') ||
      input === '!explore' ||
      input === '!post'
    ) {
      shouldNavigate = true;
      navigationAction = () => navigate('/explore?create=post');
      botResponse = t('navigation.explore');
    } else if (
      input.includes('set availability') ||
      input.includes('availability') ||
      input === '!availability'
    ) {
      shouldNavigate = true;
      navigationAction = () => {
        if (location.pathname !== '/profile') {
          navigate('/profile?availability=true');
        } else {
          window.dispatchEvent(new Event('openAvailabilityModal'));
        }
      };
      botResponse = t('navigation.availability');
    } else if (
      input.includes('add services') ||
      input.includes('add service') ||
      input === '!services'
    ) {
      shouldNavigate = true;
      navigationAction = () => {
        if (location.pathname !== '/profile') {
          navigate('/profile?edit=services');
        } else {
          window.dispatchEvent(new Event('openServicesModal'));
        }
      };
      botResponse = t('navigation.services');
    } else if (input.includes('mission') || input === '!missions') {
      shouldNavigate = true;
      navigationAction = () => navigate('/missions');
      botResponse = t('navigation.missions');
    } else if (input.includes('client') || input === '!clients') {
      shouldNavigate = true;
      navigationAction = () => {
        if (location.pathname !== '/profile') {
          navigate('/profile?clients=true');
        } else {
          window.dispatchEvent(new Event('openClientsModal'));
        }
      };
      botResponse = t('navigation.clients');
    } else if (input.includes('order') || input === '!orders') {
      shouldNavigate = true;
      navigationAction = () => {
        if (location.pathname !== '/profile') {
          navigate('/profile?orders=true');
        } else {
          window.dispatchEvent(new Event('openOrdersModal'));
        }
      };
      botResponse = t('navigation.orders');
    } else if (input.includes('settings')) {
      shouldNavigate = true;
      navigationAction = () => {
        if (location.pathname !== '/profile') {
          navigate('/profile');
          setTimeout(() => {
            window.dispatchEvent(new Event('openSettingsModal'));
          }, 300);
        } else {
          window.dispatchEvent(new Event('openSettingsModal'));
        }
      };
      botResponse = t('navigation.settings');
    } else if (input.includes('top up') || input.includes('topup')) {
      shouldNavigate = true;
      navigationAction = () => {
        if (location.pathname !== '/wallet') {
          navigate('/wallet?topup=true');
        } else {
          window.dispatchEvent(new Event('openTopUpModal'));
        }
      };
      botResponse = t('navigation.topup');
    } else if (input.includes('create mission')) {
      shouldNavigate = true;
      navigationAction = () => navigate('/missions/create');
      botResponse = t('navigation.createMission');
    } else if (
      (input.includes('talent') || input === '!talent') &&
      !input.includes('how to be') &&
      !input.includes('how to become') &&
      !input.includes('become a talent')
    ) {
      shouldNavigate = true;
      navigationAction = () => navigate('/talent');
      botResponse = t('navigation.talent');
    }

    return { shouldNavigate, navigationAction, botResponse };
  };

  // Handle action clicks from enhanced messages
  const handleActionClick = (action) => {
    switch (action) {
      case 'topup':
        if (location.pathname !== '/wallet') {
          navigate('/wallet?topup=true');
        } else {
          window.dispatchEvent(new Event('openTopUpModal'));
        }
        break;
      case 'transactions':
        navigate('/wallet');
        break;
      case 'find_missions':
        navigate('/missions');
        break;
      case 'create_mission':
        navigate('/missions/create');
        break;
      case 'profile':
        navigate('/profile');
        break;
      case 'services':
        if (location.pathname !== '/profile') {
          navigate('/profile?services=true');
        } else {
          window.dispatchEvent(new Event('openServicesModal'));
        }
        break;
      case 'set_availability':
        if (location.pathname !== '/profile') {
          navigate('/profile?availability=true');
        } else {
          window.dispatchEvent(new Event('openAvailabilityModal'));
        }
        break;
      default:
        // Treat as a regular message
        handleSendMessage(action);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setMessages(initialMessages);
    }
  }, [isOpen, initialMessages]);

  return (
    <div className="fixed left-6 bottom-24 md:bottom-6 z-40">
      <div className="relative">
        {/* Enhanced Chatbot Button with Modern Design */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          onMouseEnter={() => setShowWelcome(true)}
          onMouseLeave={() => setShowWelcome(false)}
          className={`group w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-xl hover:shadow-2xl transition-all duration-300 items-center justify-center relative z-10 hover:scale-110 active:scale-95 focus:outline-none focus:ring-4 focus:ring-indigo-300/50 focus:ring-offset-2 dark:bg-gradient-to-br dark:from-indigo-600 dark:via-purple-600 dark:to-indigo-700 dark:text-gray-100 overflow-hidden border-2 border-white/20 dark:border-gray-700/30 ${isOpen ? 'hidden md:flex' : 'flex'}`}
          aria-label={t('toggle.label')}
          aria-expanded={isOpen}
        >
          <span className="sr-only">{isOpen ? t('toggle.close') : t('toggle.open')}</span>

          {/* Animated Background Glow */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-indigo-400/20 to-purple-400/20 animate-pulse group-hover:animate-none" />

          {/* Button Content */}
          <div className="relative z-10 transition-transform duration-300 group-hover:scale-105">
            {isOpen ? (
              <svg className="w-7 h-7 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <div className="relative">
                <img
                  src="/IonaMascot.png"
                  alt="Iona Mascot"
                  className="w-20 h-20 object-cover rounded-full shadow-lg transition-transform duration-300 group-hover:scale-105"
                />
                {/* Subtle shine effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            )}
          </div>

          {/* Ripple Effect on Click */}
          <div className="absolute inset-0 rounded-full bg-white/20 scale-0 group-active:scale-100 transition-transform duration-200" />
        </button>
        {hasNewMessage && !isOpen && (
          <span className="absolute -top-1 -right-1 z-20 flex h-5 w-5">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex items-center justify-center rounded-full h-5 w-5 bg-red-500 text-white text-xs font-bold">
              {unreadCount}
            </span>
          </span>
        )}
        {incomingMessage && !isOpen && (
          <div className="absolute -top-2 left-20 z-20 animate-in fade-in-0 slide-in-from-left-2 duration-300">
            <div className="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-700 text-white text-sm px-5 py-3 rounded-2xl shadow-2xl border border-indigo-400/20 backdrop-blur-sm w-80 max-w-sm">
              <div className="relative z-10">{incomingMessage}</div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
            </div>
          </div>
        )}
        {/* Enhanced Welcome Tooltip */}
        {showWelcome && !isOpen && !incomingMessage && (
          <div className="absolute -top-2 left-20 z-20 animate-in fade-in-0 slide-in-from-left-2 duration-300">
            <div className="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-700 text-white text-sm px-5 py-3 rounded-2xl shadow-2xl border border-indigo-400/20 backdrop-blur-sm w-80 max-w-sm">
              {/* Tooltip Arrow */}
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-2">
                <div className="w-4 h-4 bg-gradient-to-br from-indigo-600 to-purple-600 rotate-45 border-l border-t border-indigo-400/20" />
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center">
                    <img src="/IonaMascot.png" alt="Iona" className="w-6 h-6 rounded-full" />
                  </div>
                  <span className="font-semibold text-white/90">{t('tooltip.title')}</span>
                </div>
                <TypingText text={t('tooltip.message')} />
              </div>

              {/* Subtle glow effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
            </div>
          </div>
        )}
        {/* Enhanced Chat Window */}
        {isOpen && (
          <div className="fixed inset-0 z-50 md:static md:inset-auto md:z-auto md:absolute md:bottom-20 md:left-0 md:w-auto md:h-auto animate-in fade-in-0 slide-in-from-bottom-4 duration-500 ease-out">
            <div className="absolute inset-0 bg-black/40 md:hidden" onClick={() => setIsOpen(false)} />
            <div className="relative w-full h-full md:w-[380px] md:h-[620px] flex flex-col md:rounded-3xl bg-white/95 dark:bg-gray-900/95 backdrop-blur-3xl backdrop-saturate-200 shadow-2xl border border-gray-200/50 dark:border-gray-700/50 transition-all duration-300 ease-out">

              {/* Modern Header - Fixed */}
              <div className="flex-shrink-0 px-6 py-4 bg-gradient-to-br from-indigo-500 via-purple-500 to-indigo-600 text-white border-b border-indigo-400/20 md:rounded-t-3xl text-left relative">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 flex items-center justify-center shadow-lg">
                      <img src="/IonaMascot.png" alt="Iona" className="w-8 h-8 rounded-full" />
                    </div>
                    <div className="text-left">
                      <h4 className="font-bold text-lg text-left">Iona</h4>
                      <p className="text-sm md:text-xs text-indigo-100 opacity-90 text-left">{t('header.assistant')}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 px-3 py-1.5 bg-green-500/20 rounded-full border border-green-400/30">
                    <div className="w-2 h-2 rounded-full bg-green-400" />
                    <span className="text-sm md:text-xs font-medium text-green-100 text-left">{t('header.status')}</span>
                  </div>
                </div>

                {/* Mobile close button */}
                <button
                  onClick={() => setIsOpen(false)}
                  className="absolute top-4 right-4 md:hidden w-10 h-10 flex items-center justify-center rounded-full bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:text-red-500 transition-colors"
                  aria-label={t('toggle.closeLabel')}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Message Area - Scrollable */}
              <div className="flex-1 min-h-0 bg-gradient-to-b from-gray-50/50 to-white dark:from-gray-800/30 dark:to-gray-900/50 overflow-hidden">
                <MessageList
                  messages={messages}
                  onCommandClick={handleSendMessage}
                  onActionClick={handleActionClick}
                />
              </div>

              {/* Enhanced Input Area - Fixed at Bottom */}
              <div className="flex-shrink-0 border-t border-gray-200/50 dark:border-gray-700/50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm md:rounded-b-3xl">
                <ChatInput onSendMessage={handleSendMessage} />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Chatbot;
