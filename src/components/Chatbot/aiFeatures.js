import { walletAPI } from '../../services/walletService';
import { missionApi } from '../../services/missionApi';
import { availabilityAPI } from '../../services/availabilityService';

// Simple regex-based entity extraction
const extractAmount = (text) => {
  const match = text.match(/(\d+(?:\.\d+)?)/);
  return match ? parseFloat(match[1]) : null;
};

const extractDate = (text) => {
  const today = new Date();
  if (/tomorrow/i.test(text)) {
    const tomorrow = new Date(today.getTime() + 86400000);
    return tomorrow.toISOString().split('T')[0];
  }
  if (/today/i.test(text)) {
    return today.toISOString().split('T')[0];
  }
  const match = text.match(/(\d{4}-\d{2}-\d{2})/);
  return match ? match[1] : null;
};

// Determine user intent from text
const recognizeIntent = (text) => {
  const lower = text.toLowerCase();
  if (/(balance|wallet|credits?)/.test(lower)) return 'balance';
  if (/(find|recommend).*missions?/.test(lower) || /(missions?)\b/.test(lower)) return 'missions';
  if (/(top\s?up|add\s?credits?)/.test(lower)) return 'topup';
  if (/(availability|schedule)/.test(lower)) return 'availability';
  return null;
};

// Handle recognized intent and return chatbot response object
export const processAIMessage = async (text, { t, pendingAction, setPendingAction }) => {
  // Handle follow-up for pending actions
  if (pendingAction === 'topup') {
    const amount = extractAmount(text);
    if (amount) {
      setPendingAction(null);
      return {
        text: t('ai.topup.confirm', { amount }),
        type: 'contextual',
        actions: [{ label: t('quickSuggestions.topUpCredits'), action: 'topup' }]
      };
    }
    return { text: t('ai.topup.askAmount'), type: 'contextual' };
  }

  const intent = recognizeIntent(text);
  if (!intent) return null;

  switch (intent) {
    case 'balance': {
      try {
        const res = await walletAPI.getBalance();
        const amount = res?.data?.balance ?? res?.data?.data?.balance ?? 0;
        return { text: t('ai.balance', { amount }), type: 'data' };
      } catch (err) {
        console.error('Balance fetch error:', err);
        return { text: t('errors.processing'), type: 'error' };
      }
    }
    case 'missions': {
      try {
        const res = await missionApi.getMissions({ limit: 3 });
        const missions = res?.data?.missions || res?.data?.data || res?.data || [];
        if (missions.length) {
          const titles = missions.slice(0, 3).map((m) => m.title).join(', ');
          return {
            text: t('ai.missions.found', { titles }),
            type: 'contextual',
            actions: [{ label: t('quickSuggestions.findMissions'), action: 'find_missions' }]
          };
        }
        return { text: t('ai.missions.none'), type: 'contextual' };
      } catch (err) {
        console.error('Mission fetch error:', err);
        return { text: t('errors.processing'), type: 'error' };
      }
    }
    case 'topup': {
      try {
        const res = await walletAPI.getCreditPackages();
        const packages = res?.data?.credit_packages || res?.data || [];
        const suggestions = packages.slice(0, 3).map((p) => ({ label: `${p.amount}`, command: `${p.amount}` }));
        setPendingAction('topup');
        return {
          text: t('ai.topup.askAmount'),
          type: 'contextual',
          suggestions
        };
      } catch (err) {
        console.error('Top up packages error:', err);
        return { text: t('errors.processing'), type: 'error' };
      }
    }
    case 'availability': {
      try {
        const date = extractDate(text) || new Date().toISOString().split('T')[0];
        const res = await availabilityAPI.getUserAvailability({ date });
        const slots = res?.data || res || [];
        if (Array.isArray(slots) && slots.length) {
          const slot = slots[0];
          const start = slot.start_time || slot.start || slot.from || '';
          const end = slot.end_time || slot.end || slot.to || '';
          return { text: t('ai.availability.slot', { date, start, end }), type: 'data' };
        }
        return { text: t('ai.availability.none', { date }), type: 'data' };
      } catch (err) {
        console.error('Availability fetch error:', err);
        return { text: t('errors.processing'), type: 'error' };
      }
    }
    default:
      return null;
  }
};

