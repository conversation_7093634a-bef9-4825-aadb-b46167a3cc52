import React from 'react';
import { motion } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

const Message = ({ message, onCommandClick, onActionClick }) => {
  const isUser = message.sender === 'user';
  const isTyping = message.typing;
  const messageType = message.type || 'default';
  const { t } = useTranslation('chatbot');

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-3`}>
      <div className={`
        relative max-w-[85%] px-4 py-3 rounded-2xl backdrop-blur-sm transition-all duration-300 shadow-sm
        ${isUser
          ? 'bg-gradient-to-br from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-indigo-500/20'
          : 'bg-white/90 dark:bg-gray-800/90 text-gray-800 dark:text-gray-200 border border-gray-200/50 dark:border-gray-700/50 shadow-gray-900/5'
        }
        ${isTyping ? 'animate-pulse' : ''}
      `}>

        {/* Message Type Indicator */}
        {!isUser && messageType !== 'default' && (
          <div className="flex items-center gap-2 mb-2 text-sm md:text-xs text-gray-500 dark:text-gray-400">
            <div className={`w-2 h-2 rounded-full ${getTypeColor(messageType)}`} />
            <span className="capitalize">{getTypeLabel(messageType, t)}</span>
          </div>
        )}

        {/* Message Content */}
        <div className="relative z-10 text-left">
          {message.text && (
            <p className={`text-base md:text-sm leading-relaxed text-left ${isTyping ? 'animate-pulse' : ''}`}>
              {message.text}
            </p>
          )}

          {message.checklist && (
            <ul className="mt-3 space-y-2">
              {message.checklist.map((item) => (
                <li key={item.label} className="flex items-center gap-2 text-base md:text-sm">
                  <span
                    className={`flex items-center justify-center w-5 h-5 rounded-md text-xs font-bold ${
                      item.done
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-300 text-gray-600'
                    }`}
                  >
                    {item.done ? '✓' : ''}
                  </span>
                  <span className={item.done ? 'line-through text-gray-500' : ''}>{item.label}</span>
                </li>
              ))}
            </ul>
          )}

          {message.allDone && (
            <p className="mt-3 text-base md:text-sm font-medium text-green-600">
              {t('talent.allDone')}
            </p>
          )}

          {/* Enhanced Action Buttons */}
          {message.actions && (
            <div className="flex flex-wrap gap-2 mt-4 pt-3 border-t border-white/20 dark:border-gray-600/30 justify-start">
              {message.actions.map((action, index) => (
                <motion.button
                  key={action.text}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`group relative px-4 py-3 md:py-2 rounded-xl text-sm md:text-xs font-medium cursor-pointer transition-all duration-200 hover:scale-105 active:scale-95 text-left min-h-[44px] md:min-h-0 ${
                    action.priority === 'high'
                      ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 text-green-700 dark:text-green-300 hover:from-green-500/30 hover:to-emerald-500/30'
                      : 'bg-gradient-to-r from-indigo-500/10 to-purple-500/10 hover:from-indigo-500/20 hover:to-purple-500/20 border border-indigo-500/20 hover:border-indigo-500/30 text-white dark:text-indigo-300'
                  }`}
                  onClick={() => onActionClick ? onActionClick(action.action) : onCommandClick(action.text)}
                >
                  <span className="relative z-10 transition-colors duration-200">
                    {action.text}
                  </span>
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </motion.button>
              ))}
            </div>
          )}

          {/* Enhanced Command Buttons (keeping existing functionality) */}
          {message.commands && (
            <div className="flex flex-wrap gap-2 mt-4 pt-3 border-t border-white/20 dark:border-gray-600/30 justify-start">
              {message.commands.map((cmdItem, index) => {
                const cmd = typeof cmdItem === 'string' ? { label: cmdItem, command: cmdItem } : cmdItem;
                return (
                  <button
                    key={cmd.command}
                    className="group relative px-4 py-3 md:px-3 md:py-2 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 hover:from-indigo-500/20 hover:to-purple-500/20 border border-indigo-500/20 hover:border-indigo-500/30 rounded-xl text-sm md:text-xs font-medium cursor-pointer transition-all duration-200 hover:scale-105 active:scale-95 dark:bg-gradient-to-r dark:from-gray-600/20 dark:to-gray-700/20 dark:hover:from-gray-600/30 dark:hover:to-gray-700/30 dark:border-gray-600/30 dark:hover:border-gray-500/40 dark:text-gray-300 text-left min-h-[44px] md:min-h-0"
                    onClick={() => onCommandClick(cmd.command)}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <span className="relative z-10 text-white dark:text-indigo-300 group-hover:text-indigo-800 dark:group-hover:text-indigo-200 transition-colors duration-200 text-left">
                      {cmd.label}
                    </span>

                    {/* Subtle hover glow */}
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </button>
                );
              })}
            </div>
          )}

          {/* Smart Suggestions */}
          {message.suggestions && (
            <div className="flex flex-wrap gap-2 mt-3 justify-start">
              {message.suggestions.slice(0, 3).map((suggestion, index) => (
                <button
                  key={suggestion}
                  className="px-4 py-2 md:px-3 md:py-1.5 text-sm md:text-xs text-gray-600 dark:text-gray-400 bg-gray-100/80 dark:bg-gray-700/50 hover:bg-gray-200/80 dark:hover:bg-gray-600/50 rounded-full border border-gray-200/50 dark:border-gray-600/50 transition-all duration-200 hover:scale-105 active:scale-95 text-left min-h-[44px] md:min-h-0"
                  onClick={() => onCommandClick(suggestion)}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Message Bubble Enhancements */}
        {isUser && (
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
        )}

        {!isUser && (
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-gray-50/50 to-transparent dark:from-gray-700/20 dark:to-transparent pointer-events-none" />
        )}

        {/* Typing indicator dots */}
        {isTyping && (
          <div className="flex items-center gap-1 mt-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        )}
      </div>
    </div>
  );
};

// Helper functions for message types
const getTypeColor = (type) => {
  switch (type) {
    case 'data': return 'bg-blue-400';
    case 'contextual': return 'bg-green-400';
    case 'help': return 'bg-purple-400';
    case 'error': return 'bg-red-400';
    case 'greeting': return 'bg-yellow-400';
    case 'empty': return 'bg-gray-400';
    default: return 'bg-indigo-400';
  }
};

const getTypeLabel = (type, t) => {
  switch (type) {
    case 'data': return t('typeLabels.data');
    case 'contextual': return t('typeLabels.contextual');
    case 'help': return t('typeLabels.help');
    case 'error': return t('typeLabels.error');
    case 'greeting': return t('typeLabels.greeting');
    case 'empty': return t('typeLabels.empty');
    default: return t('typeLabels.default');
  }
};

export default Message;
