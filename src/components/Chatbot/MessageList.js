import React, { useRef, useEffect } from 'react';
import Message from './Message';
import useTranslation from '../../hooks/useTranslation';

const MessageList = ({ messages, onCommandClick, onActionClick }) => {
  const listRef = useRef(null);
  const { t } = useTranslation('chatbot');

  useEffect(() => {
    if (listRef.current) {
      // Smooth scroll to bottom when new messages arrive
      listRef.current.scrollTo({
        top: listRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [messages]);

  return (
    <div
      className="h-full overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300/50 scrollbar-track-transparent hover:scrollbar-thumb-gray-400/70 dark:scrollbar-thumb-gray-600/50 dark:hover:scrollbar-thumb-gray-500/70"
      ref={listRef}
    >
      <div className="p-4 md:p-6 space-y-4 min-h-full flex flex-col justify-end">
        {messages.length === 0 ? (
          // Welcome message when no messages
          <div className="flex-1 flex items-center justify-start pl-4">
            <div className="text-left text-gray-500 dark:text-gray-400">
              <div className="w-16 h-16 mb-4 opacity-50">
                <img src="/IonaMascot.png" alt="Iona" className="w-full h-full rounded-full" />
              </div>
              <p className="text-base md:text-sm text-left">{t('greeting.welcome')}</p>
            </div>
          </div>
        ) : (
          // Messages
          messages.map((message, index) => (
            <div
              key={index}
              className="animate-in fade-in-0 slide-in-from-bottom-2 duration-300"
              style={{ animationDelay: `${Math.min(index * 50, 500)}ms` }}
            >
              <Message
                message={message}
                onCommandClick={onCommandClick}
                onActionClick={onActionClick}
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default MessageList;
