import React, { useState } from 'react';
import useTranslation from '../../hooks/useTranslation';

const ChatInput = ({ onSendMessage }) => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const { t } = useTranslation('chatbot');

  const handleSubmit = (e) => {
    e.preventDefault();
    const trimmed = inputValue.trim();
    if (trimmed) {
      onSendMessage(trimmed);
      setInputValue('');
    }
  };

  const hasContent = inputValue.trim().length > 0;

  return (
    <div className="p-3 md:p-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
      <form
        className={`flex items-center gap-2 md:gap-3 p-2 md:p-3 rounded-2xl bg-gray-50/80 dark:bg-gray-800/80 backdrop-blur-sm border transition-all duration-300 ${
          isFocused
            ? 'border-indigo-300 dark:border-indigo-600 shadow-lg shadow-indigo-500/10'
            : 'border-gray-200/50 dark:border-gray-700/50'
        }`}
        onSubmit={handleSubmit}
      >
        {/* Input Field */}
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={t('placeholder')}
          autoComplete="off"
          className="flex-1 bg-transparent border-0 text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 outline-none text-base md:text-sm font-medium text-left"
        />

        {/* Send Button */}
        <button
          type="submit"
          disabled={!hasContent}
          aria-label={t('ariaSend')}
          className={`group relative flex items-center justify-center h-11 w-11 md:h-10 md:w-10 p-3 md:p-2.5 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:ring-offset-1 ${
            hasContent
              ? 'bg-gradient-to-br from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/25 hover:shadow-xl hover:shadow-indigo-500/30 hover:scale-105 active:scale-95'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
          }`}
        >
          {/* Button Background Effects */}
          {hasContent && (
            <>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute inset-0 rounded-xl bg-white/10 scale-0 group-active:scale-100 transition-transform duration-200" />
            </>
          )}

          {/* Send Icon */}
          <svg
            className={`w-5 h-5 relative z-10 transition-transform duration-200 ${hasContent ? 'group-hover:translate-x-0.5' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2.5"
              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
            />
          </svg>
        </button>
      </form>

      {/* Enhanced Quick Suggestions */}
      <div className="flex flex-wrap gap-2 mt-3 justify-start">
        {[
          { text: t('quickSuggestions.checkBalance'), command: 'Check my balance', icon: '💰' },
          { text: t('quickSuggestions.findMissions'), command: 'Find missions', icon: '🎯' },
          { text: t('quickSuggestions.setAvailability'), command: 'Set availability', icon: '📅' },
          { text: t('quickSuggestions.showAnalytics'), command: 'Show analytics', icon: '📊' },
          { text: t('quickSuggestions.topUpCredits'), command: 'Top up credits', icon: '💳' }
        ].map((suggestion, index) => (
          <button
            key={suggestion.command}
            onClick={() => {
              onSendMessage(suggestion.command);
            }}
            className="group px-4 py-2 md:px-3 md:py-1.5 text-sm md:text-xs font-medium text-indigo-600 dark:text-indigo-400 bg-indigo-50/80 dark:bg-indigo-900/30 hover:bg-indigo-100/80 dark:hover:bg-indigo-900/50 rounded-full border border-indigo-200/50 dark:border-indigo-700/50 transition-all duration-200 hover:scale-105 active:scale-95 text-left flex items-center gap-1.5 min-h-[44px] md:min-h-0"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <span className="group-hover:scale-110 transition-transform duration-200">
              {suggestion.icon}
            </span>
            <span>{suggestion.text}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ChatInput;
