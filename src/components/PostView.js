import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import socialPostService from '../services/socialPostService';
import { DateTime } from 'luxon';
import Avatar from './Avatar';
import Comment from './Comment';
import MentionsInput from './MentionsInput';
import { usePostComments } from '../hooks/explore/usePostComments';
import { usePostQuery } from '../hooks/explore/usePostQuery';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { InlineLoader } from './ui/LoadingIndicator';
import { getCdnUrl } from '../utils/cdnUtils';
import { useToast } from './common/ToastProvider';

const PostView = ({ postId, isOpen, onClose }) => {
    const [commentText, setCommentText] = useState('');
    const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
    const [imgDimensions, setImgDimensions] = useState({ width: 1, height: 1 });
    const [imgLoaded, setImgLoaded] = useState(false);

    const { data: post, isLoading: loadingPost, error: postError, refetch } = usePostQuery(postId, { enabled: isOpen });

    const queryClient = useQueryClient();
    const commentInputRef = useRef(null);
    const commentsEndRef = useRef(null);

    const { success: showSuccessToast, error: showErrorToast } = useToast();

    const { 
      data: commentsData,
      fetchNextPage,
      hasNextPage,
      isFetchingNextPage,
      isLoading: isLoadingComments,
      isError: isErrorComments,
      error: commentsError,
      refetch: refetchComments
    } = usePostComments(postId);

    const comments = commentsData?.comments || [];

    // Calculate aspect ratio for current media
    const currentMedia = post?.media_files?.[currentMediaIndex];
    const hasDimensions = currentMedia && currentMedia.dimensions && currentMedia.dimensions.width && currentMedia.dimensions.height;
    const aspectRatio = hasDimensions
      ? `${currentMedia.dimensions.width} / ${currentMedia.dimensions.height}`
      : `${imgDimensions.width} / ${imgDimensions.height}`;

    // Dynamically load image dimensions if not present in metadata
    useEffect(() => {
        if (currentMedia?.type && (currentMedia.type.startsWith('image') || currentMedia.type === 'image')) {
            if (hasDimensions) {
                setImgDimensions({ width: currentMedia.dimensions.width, height: currentMedia.dimensions.height });
            } else if (currentMedia?.url) {
                const img = new window.Image();
                img.onload = () => {
                    setImgDimensions({ width: img.naturalWidth, height: img.naturalHeight });
                };
                img.src = currentMedia.url;
            }
        }
    }, [currentMedia]);

    // Post data is fetched via React Query in usePostQuery

    // Mutation for liking/unliking a post
    const { mutate: likePostMutation } = useMutation({
        mutationFn: () => socialPostService.toggleLike(postId),
        onMutate: async () => {
            await queryClient.cancelQueries({ queryKey: ['socialPost', postId] });

            const previousPost = queryClient.getQueryData(['socialPost', postId]);

            // Optimistically update cache
            queryClient.setQueryData(['socialPost', postId], (oldPost) => {
                if (!oldPost) return oldPost;
                const newLiked = !oldPost.is_liked;
                return {
                    ...oldPost,
                    is_liked: newLiked,
                    total_liked: newLiked ? oldPost.total_liked + 1 : oldPost.total_liked - 1,
                };
            });



            return { previousPost };
        },
        onError: (err, _variables, context) => {
            console.error('Error toggling like:', err);
            if (context?.previousPost) {
                queryClient.setQueryData(['socialPost', postId], context.previousPost);
            }
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ['socialPost', postId] });
        },
    });

    // Handle liking a post
    const handleLike = () => {
        if (!postId) return;
        likePostMutation(postId);
    };

    // Mutation for submitting a new comment
    const { mutate: createCommentMutation, isPending: submittingComment } = useMutation({
        mutationFn: ({ postId, content, parentId }) => socialPostService.createComment(postId, content, parentId),
        onSuccess: () => {
            setCommentText('');
            // Invalidate comments query to refetch latest comments
            queryClient.invalidateQueries({ queryKey: ['comments', 'list', postId] });
            // Also invalidate post details to update comment count
            queryClient.invalidateQueries({ queryKey: ['socialPost', postId] });
            showSuccessToast({ title: 'Comment Posted', description: 'Your comment was added.' });
        },
        onError: (err) => {
            console.error('Error submitting comment:', err);
            showErrorToast({ title: 'Failed', description: 'Failed to post comment. Please try again.' });
        }
    });

    // Handle submitting a new comment
    const handleSubmitComment = async (e) => {
        e.preventDefault();
        if (!commentText.trim() || !postId) return;
        createCommentMutation({ postId, content: commentText });
    };

    // Handle adding a new reply to a comment
    const handleAddReply = (newReply) => {
      // Invalidate comments query to refetch latest comments, including the new reply
      queryClient.invalidateQueries({ queryKey: ['comments', 'list', postId] });
      // Also invalidate post details to update comment count
      queryClient.invalidateQueries({ queryKey: ['socialPost', postId] });
    };

    // Infinite scroll for comments
    const observer = useRef();
    const lastCommentElementRef = useCallback(
      (node) => {
        if (isLoadingComments || isFetchingNextPage) return;
        if (observer.current) observer.current.disconnect();
        observer.current = new IntersectionObserver((entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            fetchNextPage();
          }
        });
        if (node) observer.current.observe(node);
      },
      [isLoadingComments, isFetchingNextPage, hasNextPage, fetchNextPage]
    );

    // Navigate through media files
    const handleNextMedia = () => {
        if (post?.media_files && currentMediaIndex < post.media_files.length - 1) {
            setCurrentMediaIndex(currentMediaIndex + 1);
        }
    };

    const handlePrevMedia = () => {
        if (currentMediaIndex > 0) {
            setCurrentMediaIndex(currentMediaIndex - 1);
        }
    };

    // Focus on comment input when requested
    const focusCommentInput = () => {
        if (commentInputRef.current) {
            commentInputRef.current.focus();
        }
    };

    // Format time for display
    const formatTimeAgo = (dateString) => {
        const now = DateTime.now();
        const postDate = DateTime.fromISO(dateString);
        const diff = now.diff(postDate, ['seconds', 'minutes', 'hours', 'days', 'weeks', 'months', 'years']).toObject();

        if (diff.years >= 1) return `${Math.floor(diff.years)}y ago`;
        if (diff.months >= 1) return `${Math.floor(diff.months)}mo ago`;
        if (diff.weeks >= 1) return `${Math.floor(diff.weeks)}w ago`;
        if (diff.days >= 1) return `${Math.floor(diff.days)}d ago`;
        if (diff.hours >= 1) return `${Math.floor(diff.hours)}h ago`;
        if (diff.minutes >= 1) return `${Math.floor(diff.minutes)}m ago`;
        return "Just now";
    };

    const aspectRatioClass = (imgDimensions.width > 1 && imgDimensions.height > 1)
        ? `aspect-[${imgDimensions.width}/${imgDimensions.height}]`
        : '';

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                className="fixed inset-0 bg-black/80 backdrop-blur-md z-50 flex items-center justify-center overflow-y-auto"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                onClick={(e) => {
                    if (e.target === e.currentTarget) {
                        onClose();
                    }
                }}
            >
                <motion.div
                    initial={{ opacity: 0, scale: 0.9, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.9, y: 20 }}
                    transition={{ type: 'spring', damping: 25, stiffness: 400 }}
                    className="w-full h-full flex items-center justify-center p-4 md:p-6"
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Close button */}
                    <motion.button
                        onClick={onClose}
                        className="absolute top-6 right-6 p-3 rounded-full bg-black/60 backdrop-blur-sm text-white z-50 hover:bg-black/80 transition-all duration-200"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </motion.button>

                    {loadingPost ? (
                        <div className="flex items-center justify-center">
                            <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    ) : postError ? (
                        <div className="bg-white rounded-lg p-6 max-w-md dark:bg-gray-800">
                            <div className="text-center">
                                <svg className="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">{postError}</h3>
                                <div className="mt-4">
                                    <button
                                        onClick={() => refetch()}
                                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Try Again
                                    </button>
                                </div>
                            </div>
                        </div>
                    ) : post && (
                        <motion.div
                            className="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden w-full max-w-6xl flex flex-col md:flex-row max-h-[90vh] border border-white/20 dark:bg-gray-900/95 dark:border-gray-800/60"
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: 0.1, type: 'spring', damping: 20, stiffness: 300 }}
                        >
                            {/* Media display */}
                            <div className="relative w-full md:w-3/5 bg-gradient-to-br from-gray-900 to-black flex items-center justify-center overflow-hidden">
                                {post.media_files && post.media_files.length > 0 ? (
                                    <>
                                        <div className="relative w-full flex items-center justify-center p-4">
                                            {currentMedia?.type && (currentMedia.type.startsWith('image') || currentMedia.type === 'image') ? (
                                                <div className={`w-full max-w-2xl mx-auto ${aspectRatioClass} flex items-center justify-center relative`}>
                                                    {imgDimensions.width > 1 && imgDimensions.height > 1 ? (
                                                        <img
                                                            src={currentMedia?.url}
                                                            alt={post.title}
                                                            className="w-full h-auto object-contain rounded-2xl"
                                                            style={{ aspectRatio }}
                                                            onLoad={() => setImgLoaded(true)}
                                                        />
                                                    ) : (
                                                        <div className="flex items-center justify-center w-full h-48">
                                                            <InlineLoader size="large" color="indigo" />
                                                        </div>
                                                    )}
                                                </div>
                                            ) : (
                                                <video
                                                    src={currentMedia?.url}
                                                    className="w-full h-auto object-contain rounded-2xl max-w-2xl mx-auto"
                                                    style={{ aspectRatio }}
                                                    controls
                                                    autoPlay
                                                />
                                            )}
                                        </div>

                                        {/* Media navigation */}
                                        {post.media_files.length > 1 && (
                                            <>
                                                <motion.button
                                                    onClick={handlePrevMedia}
                                                    className={`absolute left-4 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black/60 backdrop-blur-sm text-white transition-all duration-200 ${currentMediaIndex === 0 ? 'opacity-30 cursor-not-allowed' : 'hover:bg-black/80 hover:scale-110'}`}
                                                    disabled={currentMediaIndex === 0}
                                                    whileHover={{ scale: currentMediaIndex === 0 ? 1 : 1.1 }}
                                                    whileTap={{ scale: currentMediaIndex === 0 ? 1 : 0.9 }}
                                                >
                                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M15 19l-7-7 7-7" />
                                                    </svg>
                                                </motion.button>
                                                <motion.button
                                                    onClick={handleNextMedia}
                                                    className={`absolute right-4 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black/60 backdrop-blur-sm text-white transition-all duration-200 ${currentMediaIndex === post.media_files.length - 1 ? 'opacity-30 cursor-not-allowed' : 'hover:bg-black/80 hover:scale-110'}`}
                                                    disabled={currentMediaIndex === post.media_files.length - 1}
                                                    whileHover={{ scale: currentMediaIndex === post.media_files.length - 1 ? 1 : 1.1 }}
                                                    whileTap={{ scale: currentMediaIndex === post.media_files.length - 1 ? 1 : 0.9 }}
                                                >
                                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M9 5l7 7-7 7" />
                                                    </svg>
                                                </motion.button>

                                                {/* Enhanced dots indicator */}
                                                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
                                                    {post.media_files.map((_, index) => (
                                                        <motion.span
                                                            key={index}
                                                            className={`block w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                                                                index === currentMediaIndex ? 'bg-white scale-125' : 'bg-gray-400 opacity-70'
                                                            }`}
                                                            whileHover={{ scale: 1.2 }}
                                                        ></motion.span>
                                                    ))}
                                                </div>
                                            </>
                                        )}
                                    </>
                                ) : (
                                    <div className="flex flex-col items-center justify-center text-gray-500/70 dark:text-gray-400/70 p-8 text-center">
                                        <motion.svg 
                                            className="w-20 h-20 mb-4" 
                                            fill="none" 
                                            stroke="currentColor" 
                                            viewBox="0 0 24 24"
                                            whileHover={{ scale: 1.1 }}
                                        >
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </motion.svg>
                                        <p className="text-lg font-semibold">No Media Available</p>
                                        <p className="text-sm">This post does not contain any images or videos.</p>
                                    </div>
                                )}
                            </div>

                            {/* Content & Comments Section */}
                            <div className="w-full md:w-2/5 flex flex-col bg-white dark:bg-gray-900 p-6 relative">
                                {/* Post Header (User Info) */}
                                <div className="flex items-center mb-4">
                                    <Avatar user={post.user} size="md" />
                                    <div className="ml-3">
                                        <p className="font-semibold text-gray-900 dark:text-gray-100 text-base leading-snug">
                                            {post.user?.username || post.user?.name || 'Unknown User'}
                                        </p>
                                        <p className="text-sm text-left text-gray-500 dark:text-gray-400">
                                            {formatTimeAgo(post.created_at)}
                                        </p>
                                    </div>
                                </div>

                                {/* Post Content */}
                                <div className="flex-1 overflow-y-auto pr-2 -mr-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar-w-1 scrollbar-thumb-gray-300 scrollbar-track-gray-100 dark:scrollbar-thumb-gray-700 dark:scrollbar-track-gray-900">
                                    {post.title && (
                                        <div className="mb-4 backdrop-blur-sm rounded-2xl shadow-sm">
                                            <p className="text-gray-800 dark:text-gray-100 text-left font-semibold text-lg leading-relaxed whitespace-pre-wrap">
                                                {post.title}
                                            </p>
                                        </div>
                                    )}

                                    {/* Post Description */}
                                    {post.description && (
                                        <div className="mb-6 backdrop-blur-sm rounded-2xl shadow-sm">
                                            <p className="text-gray-800 dark:text-gray-100 text-left text-base leading-relaxed whitespace-pre-wrap">
                                                {post.description}
                                            </p>
                                        </div>
                                    )}

                                    {/* Post Actions */}
                                    <div className="flex items-center justify-between p-4 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-sm mb-4 dark:bg-gray-800/70 dark:border-gray-700/50">
                                        <div className="flex items-center space-x-5">
                                            <motion.button
                                                onClick={handleLike}
                                                className="flex items-center bg-transparent hover:bg-transparent space-x-1 text-gray-600 dark:text-gray-300 hover:text-red-500 transition-colors"
                                                whileHover={{ scale: 1.05 }}
                                                whileTap={{ scale: 0.95 }}
                                            >
                                                <svg
                                                    className={`w-5 h-5 ${post.is_liked ? 'text-red-500' : ''}`}
                                                    fill={post.is_liked ? 'currentColor' : 'none'}
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                </svg>
                                                <span className="text-sm bg-transparent font-medium">{post.total_liked || 0} Likes</span>
                                            </motion.button>
                                            <motion.button
                                                onClick={focusCommentInput}
                                                className="flex items-center bg-transparent hover:bg-transparent space-x-1 text-gray-600 dark:text-gray-300 hover:text-indigo-500 transition-colors"
                                                whileHover={{ scale: 1.05 }}
                                                whileTap={{ scale: 0.95 }}
                                            >
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                                <span className="text-sm bg-transparent font-medium">{post.total_comments || 0} Comments</span>
                                            </motion.button>
                                        </div>
                                    </div>

                                    {/* Comments Section */}
                                    <div className="mb-4">
                                  {isErrorComments && (
                                    <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-700">
                                      Error loading comments: {commentsError?.message || 'An unknown error occurred.'}
                                    </div>
                                  )}

                                  {isLoadingComments && comments.length === 0 ? (
                                    <div className="text-center py-8">
                                      <InlineLoader size="medium" color="indigo" />
                                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">Loading comments...</p>
                                    </div>
                                  ) : comments.length > 0 ? (
                                    <div className="space-y-4">
                                      {comments.map((comment, index) => {
                                        const isLastComment = index === comments.length - 1;
                                        return (
                                          <div key={comment.id} ref={isLastComment ? lastCommentElementRef : null}>
                                            <Comment 
                                              comment={comment}
                                              onAddReply={handleAddReply}
                                              postId={postId}
                                            />
                                          </div>
                                        );
                                      })}
                                      {isFetchingNextPage && (
                                        <div className="flex justify-center py-4">
                                          <InlineLoader size="small" color="indigo" />
                                        </div>
                                      )}
                                      {!hasNextPage && !isLoadingComments && comments.length > 0 && (
                                        <div className="text-center text-gray-500 dark:text-gray-400 text-xs py-4">
                                          No more comments.
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    <div className="text-center text-gray-500 dark:text-gray-400 py-8 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 dark:bg-gray-800/70 dark:border-gray-700/50">
                                      <p className="mb-2">No comments yet. Be the first to share your thoughts!</p>
                                      <motion.button 
                                        onClick={focusCommentInput}
                                        className="text-indigo-600 dark:text-indigo-400 bg-transparent hover:bg-transparent hover:underline text-sm"
                                        whileHover={{ scale: 1.05 }}
                                      >
                                        Add a comment
                                      </motion.button>
                                    </div>
                                  )}
                                </div>

                                {/* Comment input */}
                                <form onSubmit={handleSubmitComment} className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <div className="p-4 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-sm dark:bg-gray-800/70 dark:border-gray-700/50">
                                        <MentionsInput
                                            value={commentText}
                                            onChange={setCommentText}
                                            placeholder="Add a comment..."
                                            inputRef={commentInputRef}
                                            className="px-4 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-colors bg-white/80 shadow-sm text-base placeholder-gray-400 dark:bg-gray-800/70 dark:border-gray-700 dark:text-white dark:placeholder-gray-500 dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
                                        />
                                        <motion.button
                                            type="submit"
                                            className="mt-3 w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 rounded-2xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                                            disabled={!commentText.trim() || submittingComment}
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                        >
                                            {submittingComment ? 'Submitting...' : 'Post Comment'}
                                        </motion.button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        </motion.div>
                    )}
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default PostView;

// Helper component for rendering text with mentions (if needed)
const MentionText = ({ text }) => {
    // This is a placeholder. A real implementation would parse the text
    // and render mentions as clickable links.
    return <span>{text}</span>;
};
