/**
 * Homepage Context
 *
 * Provides state management for homepage data across components.
 * Includes robust error handling, caching, and fallback mechanisms.
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useHomepageQuery } from '../features/homepage/hooks/useHomepageQuery';
import { homepageApi } from '../services/homepageApi';
import { transformHomepageData, transformCarouselSlides, transformTalentsListWithPagination } from '../utils/homepageDataTransformers';
import { homepageKeys } from '../queryKeys/homepageKeys';

/**
 * Utility function to show error notifications
 * @param {string} message - Error message to display
 * @param {string} type - Type of notification ('error', 'warning', 'info')
 * @param {number} duration - Duration in milliseconds
 */
const showNotification = (message, type = 'error', duration = 5000) => {
  // Define colors based on notification type
  const colors = {
    error: {
      bg: 'bg-red-50',
      border: 'border-red-500',
      text: 'text-red-700',
      icon: 'text-red-500',
      hover: 'hover:text-red-700'
    },
    warning: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-500',
      text: 'text-yellow-700',
      icon: 'text-yellow-500',
      hover: 'hover:text-yellow-700'
    },
    info: {
      bg: 'bg-blue-50',
      border: 'border-blue-500',
      text: 'text-blue-700',
      icon: 'text-blue-500',
      hover: 'hover:text-blue-700'
    },
    success: {
      bg: 'bg-green-50',
      border: 'border-green-500',
      text: 'text-green-700',
      icon: 'text-green-500',
      hover: 'hover:text-green-700'
    }
  };

  const color = colors[type] || colors.error;
  const title = type.charAt(0).toUpperCase() + type.slice(1);

  // Create a dismissible notification
  const notificationDiv = document.createElement('div');
  notificationDiv.className = `fixed bottom-20 md:bottom-4 right-4 ${color.bg} border-l-4 ${color.border} ${color.text} p-4 rounded shadow-lg z-50 animate-fade-in max-w-md`;

  // Set icon based on notification type
  let iconPath = '';
  switch (type) {
    case 'error':
      iconPath = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />';
      break;
    case 'warning':
      iconPath = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />';
      break;
    case 'info':
      iconPath = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />';
      break;
    case 'success':
      iconPath = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />';
      break;
    default:
      iconPath = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />';
  }

  notificationDiv.innerHTML = `
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 ${color.icon}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          ${iconPath}
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm font-medium">${title}</p>
        <p class="text-xs mt-1">${message}</p>
      </div>
      <button class="ml-auto ${color.icon} ${color.hover}" onclick="this.parentElement.parentElement.remove()">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  `;
  document.body.appendChild(notificationDiv);

  // Auto-remove after specified duration
  setTimeout(() => {
    if (document.body.contains(notificationDiv)) {
      notificationDiv.classList.add('animate-fade-out');
      setTimeout(() => notificationDiv.remove(), 500);
    }
  }, duration);
};

// Create context
const HomepageContext = createContext();

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const STALE_WHILE_REVALIDATE_DURATION = 30 * 60 * 1000; // 30 minutes
const LOCAL_CACHE_KEY = 'missionx_homepage_cache';

export const HomepageProvider = ({ children }) => {
  // State for homepage data
  const [homepageData, setHomepageData] = useState({
    newTalents: { talents: [], pagination: null },
    recommendedTalents: { talents: [], pagination: null },
    onlineTalents: { talents: [], pagination: null },
    availableMissionsCount: 0,
    popularGames: []
  });

  // State for carousel slides
  const [carouselSlides, setCarouselSlides] = useState([]);

  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataStatus, setDataStatus] = useState('idle'); // 'idle', 'loading', 'success', 'error', 'stale'

  // Cache management
  const [lastFetchTime, setLastFetchTime] = useState(null);
  const [carouselLastFetchTime, setCarouselLastFetchTime] = useState(null);
  const fetchInProgressRef = useRef(false);
  const carouselFetchInProgressRef = useRef(false);
  const backgroundRefreshRef = useRef(false);

  const queryClient = useQueryClient();
  const { data: queryHomepageData } = useHomepageQuery();

  // Helper to persist homepage data
  const saveCache = useCallback((data, timestamp) => {
    try {
      localStorage.setItem(
        LOCAL_CACHE_KEY,
        JSON.stringify({ data, timestamp })
      );
    } catch (err) {
      console.error('Failed to save homepage cache:', err);
    }
  }, []);

  // Load cached homepage data on mount for faster initial render
  useEffect(() => {
    try {
      const stored = localStorage.getItem(LOCAL_CACHE_KEY);
      if (stored) {
        const { data, timestamp } = JSON.parse(stored);
        if (Date.now() - timestamp < STALE_WHILE_REVALIDATE_DURATION) {
          setHomepageData(data);
          if (data.carouselSlides) {
            setCarouselSlides(data.carouselSlides);
          }
          setLastFetchTime(timestamp);
          setLoading(false);
          setDataStatus('success');
          // Refresh in background if cache is stale
          if (Date.now() - timestamp >= CACHE_DURATION) {
            fetchHomepageData({}, true, true).catch(err =>
              console.error('Background refresh failed:', err)
            );
          }
        }
      }
    } catch (err) {
      console.error('Failed to load cached homepage data:', err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (queryHomepageData) {
      setHomepageData(queryHomepageData);
      setLastFetchTime(Date.now());
      setLoading(false);
      setDataStatus('success');
      if (queryHomepageData.carouselSlides) {
        setCarouselSlides(queryHomepageData.carouselSlides);
      }
    }
  }, [queryHomepageData]);

  // Fallback data for offline or error scenarios
  const fallbackData = {
    newTalents: {
      talents: [
        {
          id: 'fallback-1',
          name: 'GamerOne',
          level: 5,
          gender: 'male',
          bio: 'This is fallback data while we try to connect to the server.',
          image: '/images/profile-placeholder.svg',
          isNew: true,
          skills: ['Gaming', 'Coaching']
        },
        {
          id: 'fallback-2',
          name: 'GamerTwo',
          level: 3,
          gender: 'female',
          bio: 'This is fallback data while we try to connect to the server.',
          image: '/images/profile-placeholder.svg',
          isNew: true,
          skills: ['Strategy', 'Teamwork']
        }
      ],
      pagination: { currentPage: 1, totalPages: 1, totalItems: 2 }
    },
    recommendedTalents: {
      talents: [
        {
          id: 'fallback-3',
          name: 'ProGamer',
          level: 10,
          gender: 'male',
          bio: 'This is fallback data while we try to connect to the server.',
          image: '/images/profile-placeholder.svg',
          isHot: true,
          skills: ['Pro Gaming', 'Tournaments']
        },
        {
          id: 'fallback-4',
          name: 'GameMaster',
          level: 8,
          gender: 'female',
          bio: 'This is fallback data while we try to connect to the server.',
          image: '/images/profile-placeholder.svg',
          skills: ['Game Design', 'Storytelling']
        }
      ],
      pagination: { currentPage: 1, totalPages: 1, totalItems: 2 }
    },
    onlineTalents: {
      talents: [
        {
          id: 'fallback-5',
          name: 'OnlinePlayer',
          level: 7,
          gender: 'male',
          bio: 'This is fallback data while we try to connect to the server.',
          image: '/images/profile-placeholder.svg',
          isOnline: true,
          skills: ['Live Streaming', 'Commentary']
        }
      ],
      pagination: { currentPage: 1, totalPages: 1, totalItems: 1 }
    },
    availableMissionsCount: 5,
    popularGames: [
      {
        id: 'fallback-game-1',
        name: 'Fallback Game 1',
        image: '/images/game-placeholder.svg',
        description: 'This is fallback data while we try to connect to the server.',
        usageCount: 42,
        category: 'Action'
      },
      {
        id: 'fallback-game-2',
        name: 'Fallback Game 2',
        image: '/images/game-placeholder.svg',
        description: 'This is fallback data while we try to connect to the server.',
        usageCount: 28,
        category: 'Strategy'
      }
    ]
  };

  // Fallback carousel slides
  const fallbackCarouselSlides = [
    {
      id: 'fallback-slide-1',
      title: 'Meet Friend & Complete Mission',
      content: 'Join forces with top gamers and earn while you play',
      buttonText: 'Start Now',
      buttonUrl: '/missions',
      isClickable: true,
      mediaFiles: []
    },
    {
      id: 'fallback-slide-2',
      title: 'Explore New Games',
      content: 'Discover exciting new titles and connect with fellow gamers',
      buttonText: 'Explore',
      buttonUrl: '/explore',
      isClickable: true,
      mediaFiles: []
    }
  ];

  // Add a new ref to track initial fetch
  const initialFetchDoneRef = useRef(false);
  const fetchSourceRef = useRef(null);

  // Initial data fetch - only fetch when on a page that needs it
  useEffect(() => {
    const currentPath = window.location.pathname;
    const homepageRelatedPaths = ['/', '/home', '/games', '/explore', '/talent', '/missions'];

    if (homepageRelatedPaths.some(path => currentPath.startsWith(path)) && !initialFetchDoneRef.current) {
      initialFetchDoneRef.current = true;
      fetchSourceRef.current = 'HomepageContext';

      if (!queryHomepageData) {
        fetchHomepageData().catch(err => {
          console.error('Error during initial data fetch:', err);
          initialFetchDoneRef.current = false;
        });
      } else {
        setLoading(false);
        setDataStatus('success');
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryHomepageData]);

  /**
   * Function to fetch homepage data with improved caching and error handling
   * @param {Object} options - Options for the API call
   * @param {boolean} forceRefresh - Whether to force a refresh regardless of cache
   * @param {boolean} silentRefresh - Whether to refresh in the background without showing loading state
   * @returns {Promise<Object>} - Homepage data
   */
  const fetchHomepageData = useCallback(async (options = {}, forceRefresh = false, silentRefresh = false) => {
    // Create a unique request ID to track this specific request
    const requestId = Date.now().toString();
    const source = fetchSourceRef.current || 'unknown';

    // Prevent concurrent fetches - more robust check
    if (fetchInProgressRef.current) {
      return homepageData;
    }

    // Check if cache is valid and we're not forcing a refresh
    const now = Date.now();
    const cacheIsValid = lastFetchTime && (now - lastFetchTime < CACHE_DURATION);
    const cacheIsStale = lastFetchTime && (now - lastFetchTime >= CACHE_DURATION) &&
                         (now - lastFetchTime < STALE_WHILE_REVALIDATE_DURATION);

    // Use cached data if it's valid and we have data
    if (!forceRefresh && cacheIsValid && homepageData.newTalents.talents.length > 0) {
      return homepageData;
    }

    // If cache is stale but not expired, use stale data but refresh in background
    if (!forceRefresh && cacheIsStale && homepageData.newTalents.talents.length > 0 && !silentRefresh) {
      setDataStatus('stale');

      // Trigger background refresh without recursive call
      setTimeout(() => {
        if (!fetchInProgressRef.current) {
          backgroundRefreshRef.current = true;
          fetchHomepageData(options, true, true).catch(err => {
            console.error('Background refresh failed:', err);
          });
        }
      }, 100);

      return homepageData;
    }

    // Set loading state unless it's a silent refresh
    if (!silentRefresh) {
      setLoading(true);
      setDataStatus('loading');
    }

    setError(null);
    fetchInProgressRef.current = true;

    try {
      const response = await homepageApi.getAllHomepageData();
      const transformedData = transformHomepageData(response);

      // Update state unless it was a background refresh that was cancelled
      if (!(silentRefresh && !backgroundRefreshRef.current)) {
        setHomepageData(transformedData);
        setLastFetchTime(now);
        setDataStatus('success');
        if (transformedData.carouselSlides) {
          setCarouselSlides(transformedData.carouselSlides);
        }
        saveCache(transformedData, now);
      } else {
        saveCache(transformedData, now);
      }

      return transformedData;
    } catch (err) {
      console.error(`Error fetching homepage data from ${source}:`, err);
      const errorMessage = err.message || 'Failed to fetch homepage data';
      setError(errorMessage);
      setDataStatus('error');

      // Show error notification to user (unless it's a silent refresh)
      if (!silentRefresh) {
        showNotification('Using fallback data. Please check your connection.', 'error');
      }

      // If we have existing data, keep using it
      if (homepageData.newTalents.talents.length > 0) {
        return homepageData;
      }

      // Otherwise use fallback data
      console.log(`Using fallback data due to API error (request from ${source})`);
      setHomepageData(fallbackData);
      setLastFetchTime(now);
      saveCache(fallbackData, now);
      return fallbackData;
    } finally {
      // Add a small delay before resetting the fetch flag to prevent race conditions
      setTimeout(() => {
        if (!silentRefresh) {
          setLoading(false);
        }
        fetchInProgressRef.current = false;
        if (silentRefresh) {
          backgroundRefreshRef.current = false;
        }
      }, 50);
    }
  }, [lastFetchTime, homepageData]);

  /**
   * Function to fetch more talents for a specific section with improved error handling
   * @param {string} section - Section to fetch more talents for ('newTalents', 'recommendedTalents', 'onlineTalents')
   * @param {number} page - Page number to fetch
   * @returns {Promise<Array>} - Array of talents
   */
  const fetchMoreTalents = useCallback(async (section, page = 2) => {
    if (!['newTalents', 'recommendedTalents', 'onlineTalents'].includes(section)) {
      console.error(`Invalid section: ${section}`);
      return [];
    }

    // Show loading indicator for the specific section
    setHomepageData(prevData => ({
      ...prevData,
      [section]: {
        ...prevData[section],
        isLoadingMore: true
      }
    }));

    try {
      let response;

      // Select the appropriate API call based on section
      switch (section) {
        case 'newTalents':
          response = await homepageApi.getNewTalents({ page });
          break;
        case 'recommendedTalents':
          response = await homepageApi.getRecommendedTalents({ page });
          break;
        case 'onlineTalents':
          response = await homepageApi.getOnlineTalents({ page });
          break;
        default:
          return [];
      }

      // Transform the response using the utility function
      const { talents, pagination } = transformTalentsListWithPagination(response);

      // Update the state with the new talents
      setHomepageData(prevData => ({
        ...prevData,
        [section]: {
          talents: [...prevData[section].talents, ...talents],
          pagination,
          isLoadingMore: false,
          hasError: false
        }
      }));

      return talents;
    } catch (err) {
      console.error(`Error fetching more ${section}:`, err);

      // Create fallback data for pagination
      const fallbackPagination = {
        currentPage: homepageData[section].pagination?.currentPage || 1,
        totalPages: homepageData[section].pagination?.totalPages || 1,
        totalItems: homepageData[section].pagination?.totalItems || 0,
        hasNextPage: false // Disable pagination on error
      };

      // Show error notification to user
      const errorMessage = `Failed to load more ${section.replace('Talents', ' talents')}`;
      showNotification(errorMessage, 'error');

      // Update state to indicate error but maintain existing data
      setHomepageData(prevData => ({
        ...prevData,
        [section]: {
          talents: [...prevData[section].talents],
          pagination: fallbackPagination,
          isLoadingMore: false,
          hasError: true
        }
      }));

      return [];
    }
  }, [homepageData]);

  /**
   * Function to refresh available missions count
   * @returns {Promise<number>} - Available missions count
   */
  const refreshMissionsCount = useCallback(async () => {
    try {
      const response = await homepageApi.getAvailableMissionsCount();
      const count = response.data.available_missions_count || 0;

      setHomepageData(prevData => ({
        ...prevData,
        availableMissionsCount: count
      }));

      return count;
    } catch (err) {
      console.error('Error refreshing missions count:', err);

      // Keep the previous count if available, otherwise use fallback value
      const fallbackCount = homepageData.availableMissionsCount || 5;

      // Show error notification to user
      showNotification('Failed to update available missions count', 'warning');

      // Update state with fallback count
      setHomepageData(prevData => ({
        ...prevData,
        availableMissionsCount: fallbackCount
      }));

      return fallbackCount;
    }
  }, [homepageData.availableMissionsCount]);

  // Context value with enhanced properties
  const value = {
    ...homepageData,
    carouselSlides,
    loading,
    error,
    dataStatus,
    fetchHomepageData,
    fetchMoreTalents,
    refreshMissionsCount,
    // Add helper methods for components
    isStale: dataStatus === 'stale',
    refresh: (options = {}) => fetchHomepageData(options, true),
    silentRefresh: (options = {}) => fetchHomepageData(options, true, true)
  };

  return (
    <HomepageContext.Provider value={value}>
      {children}
    </HomepageContext.Provider>
  );
};

/**
 * Custom hook to use the homepage context
 * Provides access to homepage data and functions
 * @returns {Object} Homepage context value
 */
export const useHomepage = () => {
  const context = useContext(HomepageContext);

  if (context === undefined) {
    throw new Error('useHomepage must be used within a HomepageProvider');
  }

  return context;
};

/**
 * Custom hook to use a specific section of homepage data
 * @param {string} section - Section to get data for ('newTalents', 'recommendedTalents', 'onlineTalents', 'popularGames')
 * @returns {Object} Section data and loading state
 */
export const useHomepageSection = (section) => {
  const context = useHomepage();

  if (!['newTalents', 'recommendedTalents', 'onlineTalents', 'popularGames', 'carouselSlides'].includes(section)) {
    console.warn(`Invalid section: ${section}. Using fallback empty data.`);
    return {
      data: section === 'popularGames' ? [] : { talents: [], pagination: null },
      loading: false,
      error: 'Invalid section',
      loadMore: () => Promise.resolve([])
    };
  }

  // For carousel slides, return a different structure
  if (section === 'carouselSlides') {
    return {
      slides: context.carouselSlides,
      loading: context.loading,
      error: context.error,
      refresh: () => context.fetchCarouselSlides(true)
    };
  }

  // For popularGames, return a different structure
  if (section === 'popularGames') {
    return {
      games: context.popularGames,
      loading: context.loading,
      error: context.error
    };
  }

  // For talent sections, return with loadMore function
  return {
    data: context[section],
    loading: context.loading || context[section]?.isLoadingMore,
    error: context.error || context[section]?.hasError,
    loadMore: (page) => context.fetchMoreTalents(section, page)
  };
};

export default HomepageContext;
