import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo
} from 'react';
import assistantA<PERSON> from '../services/assistantAI';
import profileService from '../services/profileService';
import { useAuth } from './AuthContext';

// Context to centralize Assistant AI instance and state
const AIContext = createContext({ ai: assistantAI, context: assistantAI.context });

export const AIProvider = ({ children }) => {
  const [ai] = useState(assistantAI);
  const [context, setContext] = useState(ai.context);
  const { isAuthenticated } = useAuth();

  // Helper to update AI context and local state
  const updateAIContext = useCallback(
    (data) => {
      ai.updateContext(data);
      setContext({ ...ai.context });
    },
    [ai]
  );

  // Load profile data on mount and keep AI context in sync - only if authenticated
  useEffect(() => {
    const loadProfile = async () => {
      // Only load profile if user is authenticated
      if (!isAuthenticated) {
        return;
      }

      try {
        const res = await profileService.getCompleteProfile();
        if (res?.success && res.data) {
          updateAIContext({ profile: res.data });
        }
      } catch (err) {
        // Graceful fallback: log error and continue without profile data
        console.error('AI profile load failed', err);
      }
    };

    loadProfile();
  }, [isAuthenticated, updateAIContext]);

  const value = useMemo(
    () => ({ ai, context, updateAIContext }),
    [ai, context, updateAIContext]
  );

  return (
    <AIContext.Provider value={value}>
      {children}
    </AIContext.Provider>
  );
};

export const useAI = () => useContext(AIContext);

export default AIContext;
