/**
 * Authentication Context
 *
 * This context provides authentication state and functions across the application.
 * It handles user authentication, loading states, and error handling.
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo, useRef } from 'react';
import authService from '../services/authService';
import otpService from '../services/otpService';
import { firebaseMessaging } from '../services/firebaseMessaging';
import profileService from '../services/profileService';
import ekycService from '../services/ekycService';
import { useToast } from '../components/common/ToastProvider';

// Create the context
const AuthContext = createContext();

// Constants
const AUTH_CHECK_COOLDOWN = 2000; // 2 seconds cooldown between checks
const TOKEN_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

/**
 * AuthProvider component
 *
 * This provider manages authentication state across the application.
 * It handles user authentication, loading states, and error handling.
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 */
export const AuthProvider = ({ children }) => {
  // Authentication state
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  // Persisted user settings
  const [thirdPartyAccess, setThirdPartyAccess] = useState(null);
  const [kycStatus, setKycStatus] = useState(null);

  // Refs for managing auth checks
  const lastCheckTimeRef = useRef(0);
  const checkInProgressRef = useRef(false);
  const tokenCheckIntervalRef = useRef(null);

  const { success: showSuccessToast } = useToast();

  // Initialize state on mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      checkAuth(true);
    } else {
      setLoading(false);
      setInitialCheckDone(true);
      setIsAuthenticated(false);
    }
  }, []);

  /**
   * Check if the user is authenticated
   * This is the centralized authentication check function
   */
  const checkAuth = useCallback(async (force = false) => {
    const now = Date.now();

    // Prevent duplicate checks
    if (!force && (
      checkInProgressRef.current || 
      (now - lastCheckTimeRef.current < AUTH_CHECK_COOLDOWN)
    )) {
      return;
    }

    // Update check status
    checkInProgressRef.current = true;
    lastCheckTimeRef.current = now;
    setLoading(true);
    setError(null);

    try {
      // Check if token exists in localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        console.log('[AUTH] No token found in localStorage');
        setIsAuthenticated(false);
        setUser(null);
        setLoading(false);
        setInitialCheckDone(true);
        return;
      }

      // Get current user data
      const response = await authService.getCurrentUser();

      if (response.success) {
        setUser(response.data);
        setIsAuthenticated(true);
        fetchThirdPartyAccess();
        fetchKycStatus();

        try {
          await firebaseMessaging.initialize();
        } catch (deviceError) {
          console.warn('[AUTH] Firebase messaging initialization failed:', deviceError);
        }
      } else {
        // If API call was successful but user is not authenticated
        console.warn('[AUTH] API call successful but authentication failed:', response.error);
        setUser(null);
        setIsAuthenticated(false);
        localStorage.removeItem('token');
      }
    } catch (err) {
      console.error('[AUTH] Authentication check failed:', err);
      setError(err.message || 'Authentication check failed');
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
      setInitialCheckDone(true);
      checkInProgressRef.current = false;
    }
  }, []);

  // Set up periodic token check
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      checkAuth(true);
    }

    // Set up interval for periodic checks
    tokenCheckIntervalRef.current = setInterval(() => {
      checkAuth();
    }, TOKEN_CHECK_INTERVAL);

    // Cleanup interval on unmount
    return () => {
      if (tokenCheckIntervalRef.current) {
        clearInterval(tokenCheckIntervalRef.current);
      }
    };
  }, [checkAuth]);

  useEffect(() => {
    if (initialCheckDone && isAuthenticated && user) {
      // Only show once per session
      if (typeof window !== 'undefined' && !sessionStorage.getItem('hasWelcomed')) {
        showSuccessToast({
          title: `Welcome back, ${user.name || 'User'}!`,
          description: "We're glad to see you again.",
          duration: 4000
        });
        sessionStorage.setItem('hasWelcomed', 'true');
      }
    }
  }, [isAuthenticated, user, initialCheckDone, showSuccessToast]);

  /**
   * Login user
   * Backend endpoint: POST /api/auth/login
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} password - User's password
   * @param {boolean} [remember=false] - Whether to remember the user
   * @param {Object} [deviceInfo=null] - Optional device information for push notifications
   * @returns {Promise<Object>} Login result
   */
  const login = async (mobileNumber, password, remember = false, deviceInfo = null) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.login(mobileNumber, password, deviceInfo);

      if (response.success) {
        setUser(response.user);
        setIsAuthenticated(true);
        fetchThirdPartyAccess();
        fetchKycStatus();

        // Store mobile number for future logins if remember is true
        if (remember) {
          localStorage.setItem('lastMobileNumber', mobileNumber);
        } else {
          localStorage.removeItem('lastMobileNumber');
        }

        // Initialize device token for push notifications
        try {
          await firebaseMessaging.initialize();
        } catch (deviceError) {
          console.warn('[AUTH] Firebase messaging initialization failed:', deviceError);
          // Don't fail login if messaging fails
        }

        return { success: true, data: response.data, user: response.user, token: response.token };
      } else {
        setError(response.error || 'Login failed');
        return { success: false, error: response.error, validationErrors: response.validationErrors };
      }
    } catch (err) {
      const errorMessage = err.message || 'Login failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Logout user
   *
   * @returns {Promise<Object>} Logout result
   */
  const logout = async () => {
    setLoading(true);
    setError(null);

    try {
      // Cleanup device token before logout
      try {
        await firebaseMessaging.cleanup();
      } catch (deviceError) {
        console.warn('[AUTH] Firebase messaging cleanup failed:', deviceError);
        // Don't fail logout if messaging cleanup fails
      }

      const response = await authService.logout();

      // Clear user data regardless of API response
      setUser(null);
      setIsAuthenticated(false);

      return { success: true, data: response.data };
    } catch (err) {
      const errorMessage = err.message || 'Logout failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update user data
   *
   * @param {Object} userData - Updated user data
   */
  const updateUser = useCallback((userData) => {
    setUser(prevUser => ({
      ...prevUser,
      ...userData
    }));
  }, []);

  /**
   * Register a new user
   *
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration result
   */
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.register(userData);

      if (response.success) {
        // If registration returns a token and user data, set them
        if (response.data.token && response.data.user) {
          setUser(response.data.user);
          setIsAuthenticated(true);

          // Set first login flag for new registrations
          localStorage.setItem('isFirstLogin', 'true');
          localStorage.setItem('user', JSON.stringify(response.data.user));

          // Initialize Firebase messaging
          try {
            await firebaseMessaging.initialize();
          } catch (deviceError) {
            console.warn('[AUTH] Firebase messaging initialization failed:', deviceError);
            // Don't fail registration if messaging fails
          }
        }

        return { success: true, data: response.data };
      } else {
        setError(response.error || 'Registration failed');
        return { success: false, error: response.error };
      }
    } catch (err) {
      console.error('🔍 Registration error details:', err);

      let errorMessage = 'Registration failed';
      let validationErrors = null;

      if (err.response?.data) {
        console.error('🔍 Server error response:', err.response.data);
        errorMessage = err.response.data.message || err.response.data.error || errorMessage;
        validationErrors = err.response.data.errors || err.response.data.validationErrors;

        if (validationErrors) {
          console.error('📋 Validation errors:', validationErrors);
        }
      } else {
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
        validationErrors: validationErrors
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Request OTP for registration
   * Backend endpoint: POST /api/auth/request-otp
   * Rate limited: 1 request per 60 seconds
   * @param {string} mobileNumber - Mobile number without country code
   * @param {string} [countryCode='+60'] - Country code prefix
   * @returns {Promise<Object>} OTP request result
   */
  const requestOtp = async (mobileNumber, countryCode = '+60') => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.requestOtp(mobileNumber, countryCode);

      if (response.success) {
        return { success: true, data: response.data, message: response.message };
      } else {
        setError(response.error || 'Failed to send OTP');
        return {
          success: false,
          error: response.error,
          validationErrors: response.validationErrors,
          details: response.details
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to send OTP';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Verify OTP for various purposes
   *
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code (e.g., +60)
   * @param {string} otp - OTP code
   * @param {string} purpose - Purpose of OTP ('registration', 'login', 'reset')
   * @returns {Promise<Object>} OTP verification result
   */
  const verifyOtp = async (mobileNumber, countryCode, otp, purpose = 'registration') => {
    setLoading(true);
    setError(null);

    try {
      let response;

      // Call the appropriate verification method based on purpose
      switch (purpose) {
        case 'registration':
          response = await otpService.verifyRegistrationOtp(mobileNumber, countryCode, otp);
          break;
        case 'login':
          response = await otpService.verifyLoginOtp(mobileNumber, countryCode, otp);
          // If login OTP verification is successful, set user data
          if (response.success && response.data.token && response.data.user) {
            setUser(response.data.user);
            setIsAuthenticated(true);
          }
          break;
        case 'reset':
          response = await otpService.verifyPasswordResetOtp(mobileNumber, countryCode, otp);
          break;
        default:
          response = await otpService.verifyRegistrationOtp(mobileNumber, countryCode, otp);
      }

      if (response.success) {
        return { success: true, data: response.data };
      } else {
        setError(response.error || `Failed to verify OTP for ${purpose}`);
        return { success: false, error: response.error };
      }
    } catch (err) {
      const errorMessage = err.message || `Failed to verify OTP for ${purpose}`;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Verify OTP for password reset
   * Backend endpoint: POST /api/auth/verify-otp-for-reset
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} otp - 6-digit OTP code
   * @returns {Promise<Object>} OTP verification result
   */
  const verifyOtpForReset = async (mobileNumber, otp) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.verifyOtpForReset(mobileNumber, otp);

      if (response.success) {
        return { success: true, data: response.data, message: response.message };
      } else {
        setError(response.error || 'Failed to verify OTP');
        return {
          success: false,
          error: response.error,
          validationErrors: response.validationErrors
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to verify OTP';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Fetch third-party access setting
   */
  const fetchThirdPartyAccess = useCallback(async () => {
    try {
      const response = await profileService.getThirdPartyAccess();
      if (response.success) {
        setThirdPartyAccess(response.data);
      } else {
        setThirdPartyAccess({ enabled: false, connected_apps: [] });
      }
    } catch (err) {
      console.error('[AUTH] Failed to load third-party access:', err);
      setThirdPartyAccess({ enabled: false, connected_apps: [] });
    }
  }, []);

  /**
   * Fetch E-KYC verification status
   */
  const fetchKycStatus = useCallback(async () => {
    try {
      const response = await ekycService.getVerificationStatus();
      if (response && response.data) {
        const isVerified = response.data.is_verified;
        const verificationType = response.data.verification_type;
        if (isVerified) {
          setKycStatus('verified');
        } else if (!isVerified && verificationType) {
          setKycStatus('pending_review');
        } else {
          setKycStatus('not_started');
        }
      } else {
        setKycStatus('not_started');
      }
    } catch (err) {
      console.error('[AUTH] Failed to load KYC status:', err);
      setKycStatus('not_started');
    }
  }, []);

  /**
   * Reset password
   * Backend endpoint: POST /api/auth/reset-password
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} password - New password
   * @returns {Promise<Object>} Password reset result
   */
  const resetPassword = async (mobileNumber, password) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.resetPassword(mobileNumber, password);

      if (response.success) {
        // Password reset logs out user from all devices
        setUser(null);
        setIsAuthenticated(false);

        return { success: true, data: response.data, message: response.message };
      } else {
        setError(response.error || 'Failed to reset password');
        return {
          success: false,
          error: response.error,
          validationErrors: response.validationErrors
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to reset password';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Register device token for push notifications
   *
   * @param {string} deviceToken - Firebase device token
   * @param {string} deviceType - Device type (web, android, ios)
   * @param {string} deviceName - Device name/user agent
   * @returns {Promise<Object>} Registration result
   */
  const registerDeviceToken = async (deviceToken, deviceType, deviceName) => {
    if (!isAuthenticated) {
      return { success: false, error: 'User not authenticated' };
    }

    try {
      const response = await authService.registerDeviceToken(deviceToken, deviceType, deviceName);
      return response;
    } catch (err) {
      const errorMessage = err.message || 'Failed to register device token';
      console.error('[AUTH] Device token registration failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Context value - memoized to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    loading,
    error,
    isAuthenticated,
    initialCheckDone,
    login,
    logout,
    register,
    requestOtp,
    verifyOtp,
    verifyOtpForReset,
    resetPassword,
    checkAuth,
    updateUser,
    registerDeviceToken,
    thirdPartyAccess,
    kycStatus,
    fetchThirdPartyAccess,
    fetchKycStatus
  }), [
    user,
    loading,
    error,
    isAuthenticated,
    initialCheckDone,
    login,
    logout,
    register,
    requestOtp,
    verifyOtp,
    verifyOtpForReset,
    resetPassword,
    checkAuth,
    updateUser,
    registerDeviceToken,
    thirdPartyAccess,
    kycStatus,
    fetchThirdPartyAccess,
    fetchKycStatus
  ]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use the auth context
 *
 * @returns {Object} Auth context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};

export default AuthContext;
