import api from './api';

const reportService = {
  getReportTypes: async () => {
    try {
      const response = await api.get('/reports/types');
      return Array.isArray(response.data) ? response.data : response.data?.data || [];
    } catch (error) {
      console.error('Error fetching report types:', error);
      throw error;
    }
  },

  reportUser: async (userId, data) => {
    try {
      const response = await api.post(`/reports/user/${userId}`, data);
      return response.data;
    } catch (error) {
      console.error('Error submitting user report:', error);
      throw error;
    }
  },

  reportPost: async (postId, data) => {
    try {
      const response = await api.post(`/reports/social-post/${postId}`, data);
      return response.data;
    } catch (error) {
      console.error('Error submitting post report:', error);
      throw error;
    }
  },

  reportMission: async (missionId, data) => {
    try {
      const response = await api.post(`/reports/mission/${missionId}`, data);
      return response.data;
    } catch (error) {
      console.error('Error submitting mission report:', error);
      throw error;
    }
  }
};

export default reportService;
