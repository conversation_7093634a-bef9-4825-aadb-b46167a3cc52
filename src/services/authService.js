/**
 * Authentication Service
 * Handles all authentication-related API calls aligned with backend API specification
 * Backend endpoints: /api/auth/*
 * Authentication: JWT with no expiration (persistent until logout)
 */
import api from './api';
import { getDeviceInfo } from '../utils/deviceInfo';

/**
 * Authentication Service - Aligned with Backend API Specification
 */
export const authService = {
  /**
   * Request OTP for registration
   * Backend endpoint: POST /api/auth/request-otp
   * Rate limited: 1 request per 60 seconds (throttle:1,1)
   * @param {string} mobileNumber - Mobile number without country code
   * @param {string} countryCode - Country code (e.g., '+60')
   * @returns {Promise<Object>} Response with success status
   */
  requestOtp: async (mobileNumber, countryCode = '+60') => {
    try {
      // Combine country code and mobile number
      const fullNumber = countryCode + mobileNumber.replace(/\D/g, '');

      const response = await api.post('/auth/request-otp', {
        mobile_number: fullNumber // Send full number with country code
      });

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'OTP sent successfully'
      };
    } catch (error) {
      console.error('OTP request error:', error);

      if (error.response) {
        const { status, data } = error.response;

        if (status === 422) {
          // Validation errors or rate limiting
          return {
            success: false,
            error: data.message || 'Invalid mobile number',
            validationErrors: data.errors || {}
          };
        } else if (status === 429) {
          // Rate limit exceeded (60 second cool down)
          return {
            success: false,
            error: 'Please wait 60 seconds before requesting another OTP'
          };
        } else if (status === 500) {
          // SMS sending failed
          return {
            success: false,
            error: data.message || 'Failed to send OTP',
            details: data.error || null
          };
        } else {
          return {
            success: false,
            error: data.message || 'Failed to send OTP'
          };
        }
      } else if (error.request) {
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  },

  /**
   * Register a new user with OTP verification
   * Backend endpoint: POST /api/auth/register
   * Content-Type: multipart/form-data (if profile picture) or application/json
   * @param {Object} userData - Complete user registration data matching backend RegisterRequest
   * @returns {Promise<Object>} Response with JWT token and user data
   */
  register: async (userData) => {
    try {
      // Log the registration attempt in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('[DEV] Registering user with data:', {
          ...userData,
          password: '[HIDDEN]',
          otp: '[HIDDEN]'
        });
      }

      // Ensure mobile number includes country code
      const mobileNumber = userData.mobile_number.startsWith('+') 
        ? userData.mobile_number 
        : `${userData.countryCode || '+60'}${userData.mobile_number.replace(/\D/g, '')}`;

      // Prepare request data and headers based on profile picture presence
      let requestData;
      let headers = {};

      if (userData.profile_picture && userData.profile_picture instanceof File) {
        // Use FormData for multipart/form-data when profile picture exists
        requestData = new FormData();

        // Add all fields to FormData (backend expects exact field names)
        Object.keys(userData).forEach(key => {
          if (userData[key] !== null && userData[key] !== undefined) {
            if (key === 'mobile_number') {
              requestData.append(key, mobileNumber);
            } else {
              requestData.append(key, userData[key]);
            }
          }
        });

        headers['Content-Type'] = 'multipart/form-data';
      } else {
        // Use JSON for application/json when no profile picture
        requestData = { 
          ...userData,
          mobile_number: mobileNumber
        };
        delete requestData.profile_picture; // Remove null/undefined profile_picture
        headers['Content-Type'] = 'application/json';
      }

      const deviceHeaders = await getDeviceInfo();
      headers['User-Agent'] = deviceHeaders.userAgent;
      headers['App-Version'] = deviceHeaders.appVersion;
      headers['Device-ID'] = deviceHeaders.deviceId;
      headers['Device-Model'] = deviceHeaders.deviceModel;
      headers['OS-Version'] = deviceHeaders.osVersion;
      headers['Device-Type'] = deviceHeaders.deviceType;
      headers['Browser-Type'] = deviceHeaders.browserType;

      const response = await api.post('/auth/register', requestData, { headers });

      if (response.data.token) {
        // Store the JWT token (no expiration as per backend config)
        localStorage.setItem('token', response.data.token);

        // Set the default authorization header for future requests
        api.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
      }

      return {
        success: true,
        data: response.data,
        token: response.data.token,
        user: response.data.user
      };
    } catch (error) {
      console.error('Registration error:', error);

      if (error.response) {
        const { status, data } = error.response;

        if (status === 422) {
          // Validation errors including OTP validation, referral validation, etc.
          return {
            success: false,
            error: data.message || 'Validation failed',
            validationErrors: data.errors || {}
          };
        } else {
          // Other server errors
          return {
            success: false,
            error: data.message || 'Registration failed'
          };
        }
      } else if (error.request) {
        // Network error
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        // Other errors
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  },

  /**
   * Login user
   * Backend endpoint: POST /api/auth/login
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} password - User's password
   * @param {Object} deviceInfo - Optional device information for push notifications
   * @returns {Promise<Object>} Response with JWT token and user data
   */
  login: async (mobileNumber, password, deviceInfo = null) => {
    try {

      // Prepare login payload (backend expects only mobile_number and password)
      const loginData = {
        mobile_number: mobileNumber,
        password: password
      };

      // Add device information if provided
      if (deviceInfo) {
        if (deviceInfo.device_token) loginData.device_token = deviceInfo.device_token;
        if (deviceInfo.device_type) loginData.device_type = deviceInfo.device_type;
        if (deviceInfo.device_name) loginData.device_name = deviceInfo.device_name;
      }

      const deviceHeaders = await getDeviceInfo();
      const headers = {
        'User-Agent': deviceHeaders.userAgent,
        'App-Version': deviceHeaders.appVersion,
        'Device-ID': deviceHeaders.deviceId,
        'Device-Model': deviceHeaders.deviceModel,
        'OS-Version': deviceHeaders.osVersion,
        'Device-Type': deviceHeaders.deviceType,
        'Browser-Type': deviceHeaders.browserType,
      };

      const response = await api.post('/auth/login', loginData, { headers });

      if (response.data.token) {
        // Store the JWT token (no expiration as per backend config)
        localStorage.setItem('token', response.data.token);

        // Set the default authorization header for future requests
        api.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
      }

      return {
        success: true,
        data: response.data,
        token: response.data.token,
        user: response.data.user
      };
    } catch (error) {
      console.error('Login error:', error);

      if (error.response) {
        const { status, data } = error.response;

        if (status === 401) {
          // Invalid credentials
          return {
            success: false,
            error: data.message || 'Invalid credentials'
          };
        } else if (status === 422) {
          // Validation errors
          return {
            success: false,
            error: data.message || 'Validation failed',
            validationErrors: data.errors || {}
          };
        } else {
          // Other server errors
          return {
            success: false,
            error: data.message || 'Login failed'
          };
        }
      } else if (error.request) {
        // Network error
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        // Other errors
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  },

  /**
   * Logout user
   * Backend endpoint: POST /api/auth/logout
   * Requires: Authorization Bearer token
   * @param {string} deviceToken - Optional device token to remove
   * @returns {Promise<Object>} Response with success status
   */
  logout: async (deviceToken = null) => {
    try {
      // Prepare logout payload
      const logoutData = {};
      if (deviceToken) {
        logoutData.device_token = deviceToken;
      }

      const response = await api.post('/auth/logout', logoutData);

      // Clear auth data
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Successfully logged out'
      };
    } catch (error) {
      console.error('Logout error:', error);

      // Still clear token even if API call fails
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];

      if (error.response) {
        const { data } = error.response;
        return {
          success: false,
          error: data.message || 'Logout failed'
        };
      } else {
        return {
          success: false,
          error: 'Network error during logout'
        };
      }
    }
  },

  /**
   * Delete user account
   * Backend endpoint: POST /api/auth/delete-account
   * Requires: current password for verification
   * @param {string} password - User's current password
   * @returns {Promise<Object>} Response with success status
   */
  deleteAccount: async (password) => {
    try {
      const response = await api.post('/auth/delete-account', {
        _method: 'DELETE',
        password
      });

      // Clear auth data
      localStorage.clear();
      sessionStorage.clear();
      delete api.defaults.headers.common['Authorization'];

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Account deleted successfully'
      };
    } catch (error) {
      console.error('Delete account error:', error);

      if (error.response) {
        const { data } = error.response;
        return {
          success: false,
          error: data.message || 'Failed to delete account'
        };
      } else {
        return {
          success: false,
          error: 'Network error during account deletion'
        };
      }
    }
  },

  /**
   * Verify OTP for password reset
   * Backend endpoint: POST /api/auth/verify-otp-for-reset
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} otp - 6-digit OTP code
   * @returns {Promise<Object>} Response with success status
   */
  verifyOtpForReset: async (mobileNumber, otp) => {
    try {
      // Log the verification in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log(`[DEV] Verifying OTP for password reset: ${mobileNumber}`);
      }

      const response = await api.post('/auth/verify-otp-for-reset', {
        mobile_number: mobileNumber,
        otp: otp
      });

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'OTP verified successfully. You can now reset your password.'
      };
    } catch (error) {
      console.error('OTP verification error:', error);

      if (error.response) {
        const { status, data } = error.response;

        if (status === 422) {
          // Invalid or expired OTP, or mobile number not registered
          return {
            success: false,
            error: data.message || 'Invalid or expired OTP',
            validationErrors: data.errors || {}
          };
        } else {
          return {
            success: false,
            error: data.message || 'Failed to verify OTP'
          };
        }
      } else if (error.request) {
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  },

  /**
   * Reset password
   * Backend endpoint: POST /api/auth/reset-password
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} password - New password
   * @returns {Promise<Object>} Response with success status
   */
  resetPassword: async (mobileNumber, password) => {
    try {
      // Log the password reset in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log(`[DEV] Resetting password for ${mobileNumber}`);
      }

      const response = await api.post('/auth/reset-password', {
        mobile_number: mobileNumber,
        password: password
      });

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Password has been successfully reset'
      };
    } catch (error) {
      console.error('Password reset error:', error);

      if (error.response) {
        const { status, data } = error.response;

        if (status === 422) {
          // Validation errors or OTP not verified
          return {
            success: false,
            error: data.message || 'Please verify OTP before resetting password',
            validationErrors: data.errors || {}
          };
        } else {
          return {
            success: false,
            error: data.message || 'Failed to reset password'
          };
        }
      } else if (error.request) {
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  },

  /**
   * Change password for logged in user
   * Backend endpoint: PUT /api/auth/change-password
   * Requires: Authorization Bearer token
   * @param {string} currentPassword
   * @param {string} newPassword
   * @param {string} confirmPassword
   * @returns {Promise<Object>} Response with success status
   */
  changePassword: async (currentPassword, newPassword, confirmPassword) => {
    try {
      const response = await api.put('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword,
        new_password_confirmation: confirmPassword
      });
      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Password updated'
      };
    } catch (error) {
      console.error('Change password error:', error);
      if (error.response) {
        const { data } = error.response;
        return {
          success: false,
          error: data.message || 'Failed to update password',
          validationErrors: data.errors || {}
        };
      }
      return {
        success: false,
        error: 'Network error. Please try again.'
      };
    }
  },

  /**
   * Change phone number for logged in user
   * Backend endpoint: PUT /api/auth/change-phone-number
   * Requires: Authorization Bearer token
   * @param {string} mobileNumber - Phone number with country code
   * @returns {Promise<Object>} Response with success status
   */
  changePhoneNumber: async (mobileNumber, otp, countryCode = '+60') => {
    try {
      let formatted = (mobileNumber || '').trim();
      if (!formatted.startsWith('+')) {
        if (formatted.startsWith('60')) {
          formatted = `+${formatted}`;
        } else if (formatted.startsWith('0')) {
          formatted = `+6${formatted}`;
        } else {
          formatted = `${countryCode}${formatted}`;
        }
      }
      const response = await api.put('/auth/change-phone-number', {
        mobile_number: formatted,
        otp: otp
      });
      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Phone number updated'
      };
    } catch (error) {
      console.error('Change phone number error:', error);
      if (error.response) {
        const { data } = error.response;
        return {
          success: false,
          error: data.message || 'Failed to update phone number',
          validationErrors: data.errors || {}
        };
      }
      return {
        success: false,
        error: 'Network error. Please try again.'
      };
    }
  },

  /**
   * Register device token for push notifications
   * Backend endpoint: POST /api/auth/user/device-token
   * Requires: Authorization Bearer token
   * @param {string} deviceToken - Firebase device token
   * @param {string} deviceType - Device type (web, android, ios)
   * @param {string} deviceName - Device name/user agent
   * @returns {Promise<Object>} Response with success status
   */
  registerDeviceToken: async (deviceToken, deviceType, deviceName) => {
    try {
      const response = await api.post('/auth/user/device-token', {
        device_token: deviceToken,
        device_type: deviceType,
        device_name: deviceName
      });

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Device token registered successfully'
      };
    } catch (error) {
      console.error('Device token registration error:', error);

      if (error.response) {
        const { status, data } = error.response;

        if (status === 422) {
          // Validation errors
          return {
            success: false,
            error: data.message || 'Invalid device token data',
            validationErrors: data.errors || {}
          };
        } else if (status === 401) {
          // Unauthorized
          return {
            success: false,
            error: 'Authentication required'
          };
        } else {
          return {
            success: false,
            error: data.message || 'Failed to register device token'
          };
        }
      } else if (error.request) {
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  },

  /**
   * Remove device token
   * Backend endpoint: DELETE /api/auth/user/device-token
   * Requires: Authorization Bearer token
   * @param {string} deviceToken - Firebase device token to remove
   * @returns {Promise<Object>} Response with success status
   */
  removeDeviceToken: async (deviceToken) => {
    try {
      const response = await api.delete('/auth/user/device-token', {
        data: { device_token: deviceToken }
      });

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Device token removed successfully'
      };
    } catch (error) {
      console.error('Device token removal error:', error);

      if (error.response) {
        const { status, data } = error.response;
        return {
          success: false,
          error: data.message || 'Failed to remove device token'
        };
      } else {
        return {
          success: false,
          error: 'Network error during device token removal'
        };
      }
    }
  },

  /**
   * Get current user data
   * Backend endpoint: GET /api/user
   * Requires: Authorization Bearer token
   * @returns {Promise<Object>} Response with user data
   */
  getCurrentUser: async () => {
    try {

      const response = await api.get('/user');

      return {
        success: true,
        data: response.data,
        user: response.data
      };
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[AUTH] Failed to get user data:', error.response?.status, error.response?.data || error.message);
      }

      if (error.response) {
        const { status, data } = error.response;

        if (status === 401) {
          // Unauthorized - token invalid or expired
          return {
            success: false,
            error: 'Authentication required',
            requiresLogin: true
          };
        } else {
          return {
            success: false,
            error: data.message || 'Failed to get user data'
          };
        }
      } else if (error.request) {
        return {
          success: false,
          error: 'Network error. Please check your connection and try again.'
        };
      } else {
        return {
          success: false,
          error: 'An unexpected error occurred. Please try again.'
        };
      }
    }
  }
};

export default authService;
