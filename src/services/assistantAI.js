import { missionApi } from './missionApi';
import globalBalanceService from './globalBalanceService';
import { transactionService } from './transactionService';
import profileService from './profileService';

/**
 * Remove sensitive fields from profile objects before storing them
 */
export const sanitizeProfile = (profile = {}) => {
  if (!profile || typeof profile !== 'object') return {};

  const forbidden = ['password', 'ssn', 'bank', 'card', 'financial'];
  return Object.keys(profile).reduce((acc, key) => {
    const lower = key.toLowerCase();
    if (!forbidden.some(f => lower.includes(f))) {
      acc[key] = profile[key];
    }
    return acc;
  }, {});
};

/**
 * Enhanced AI Assistant Service
 * Integrates with existing platform data to provide contextual responses
 */
class AssistantAI {
  constructor() {
    this.context = {
      user: null,
      profile: null,
      wallet: null,
      recentActivity: [],
      preferences: {}
    };
  }

  /**
   * Update context with current user data
   */
  updateContext(contextData) {
    const updated = { ...contextData };

    if (updated.profile) {
      updated.profile = sanitizeProfile(updated.profile);
    }

    this.context = { ...this.context, ...updated };
  }

  /**
   * Enhanced message processing with context awareness
   */
  async processMessage(userInput, contextData = {}) {
    this.updateContext(contextData);
    
    const input = userInput.toLowerCase().trim();
    
    try {
      // Check for specific command patterns first
      const commandResponse = await this.handleCommands(input);
      if (commandResponse) return commandResponse;

      // Handle contextual queries
      const contextualResponse = await this.handleContextualQueries(input);
      if (contextualResponse) return contextualResponse;

      // Handle natural language queries
      const nlpResponse = await this.handleNaturalLanguage(input);
      if (nlpResponse) return nlpResponse;

      // Fallback with contextual suggestions
      return this.getFallbackResponse(input);

    } catch (error) {
      console.error('Assistant AI Error:', error);
      return {
        text: "I'm having trouble processing that request. Please try again or be more specific.",
        type: 'error',
        suggestions: ['!help', 'Check my balance', 'Show missions']
      };
    }
  }

  /**
   * Handle specific commands with enhanced functionality
   */
  async handleCommands(input) {
    // Enhanced balance command
    if (input.includes('!balance') || input.includes('balance')) {
      return await this.getBalanceInfo();
    }

    // Enhanced missions command
    if (input.includes('!missions') || input.includes('missions')) {
      return await this.getMissionsInfo(input);
    }

    // New analytics command
    if (input.includes('!analytics') || input.includes('analytics')) {
      return await this.getAnalytics();
    }

    // New schedule command
    if (input.includes('!schedule') || input.includes('availability')) {
      return await this.getScheduleInfo();
    }

    // Enhanced help command
    if (input.includes('!help') || input.includes('help')) {
      return this.getEnhancedHelp();
    }

    return null;
  }

  /**
   * Handle contextual queries based on user data
   */
  async handleContextualQueries(input) {
    const { wallet, profile, recentActivity } = this.context;

    // Talent status checklist
    if (
      input.includes('how to be a talent') ||
      input.includes('how to become a talent') ||
      (input.includes('be a talent') && input.includes('how')) ||
      input.includes('become a talent')
    ) {
      return await this.getTalentChecklist();
    }

    // Wallet-related contextual responses
    if (input.includes('money') || input.includes('credits') || input.includes('spend')) {
      if (wallet && wallet.balance < 100) {
        return {
          text: `Your balance is low (${wallet.balance} credits). Would you like to top up?`,
          type: 'contextual',
          actions: [
            { text: 'Top Up Now', action: 'topup', priority: 'high' },
            { text: 'View Transactions', action: 'transactions' }
          ],
          suggestions: ['Top up 500 credits', 'Show spending history']
        };
      }
    }

    // Profile optimization suggestions
    if (input.includes('profile') || input.includes('optimize')) {
      const suggestions = await this.getProfileSuggestions();
      if (suggestions.length > 0) {
        return {
          text: `I found some ways to improve your profile:\n\n${suggestions.join('\n')}`,
          type: 'contextual',
          actions: [
            { text: 'Update Profile', action: 'profile' },
            { text: 'Add Services', action: 'services' }
          ]
        };
      }
    }

    // Mission recommendations
    if (input.includes('find') || input.includes('recommend')) {
      return await this.getMissionRecommendations();
    }

    return null;
  }

  /**
   * Handle natural language processing
   */
  async handleNaturalLanguage(input) {
    // Greeting patterns
    if (['hi', 'hello', 'hey', 'good morning', 'good afternoon'].some(greet => 
      input.includes(greet))) {
      return this.getPersonalizedGreeting();
    }

    // Question patterns
    if (input.includes('how') || input.includes('what') || input.includes('when')) {
      return await this.handleQuestions(input);
    }

    // Action patterns
    if (input.includes('show me') || input.includes('get me') || input.includes('find me')) {
      return await this.handleActionRequests(input);
    }

    return null;
  }

  /**
   * Get enhanced balance information
   */
  async getBalanceInfo() {
    try {
      // Try to use balance from context first, fallback to service
      let balance = this.context.wallet?.balance;

      if (balance === null || balance === undefined) {
        // Use globalBalanceService as fallback
        balance = await globalBalanceService.getBalance();
      }

      // Try to get recent transactions from context first
      let recentTransactions = [];

      if (this.context.wallet?.transactions) {
        recentTransactions = this.context.wallet.transactions.slice(0, 3);
      } else {
        // Fallback to service call
        try {
          const transactionData = await transactionService.getTransactions({ limit: 5 });
          recentTransactions = transactionData.data?.slice(0, 3) || [];
        } catch (txError) {
          console.warn('Could not fetch recent transactions:', txError);
          // Continue without transactions - balance is more important
        }
      }

      // Format balance properly
      const formattedBalance = typeof balance === 'number'
        ? `${balance} credits`
        : globalBalanceService.formatBalance(balance);

      let response = `💰 Your current balance is ${formattedBalance}.\n\n`;

      if (recentTransactions.length > 0) {
        response += "Recent activity:\n";
        recentTransactions.forEach(tx => {
          response += `• ${tx.type}: ${tx.amount} credits\n`;
        });
      }

      // Add contextual suggestions based on balance
      const suggestions = [];
      if (balance < 100) {
        suggestions.push('Top up credits');
      }
      suggestions.push('View all transactions', 'Set spending alerts');

      return {
        text: response,
        type: 'data',
        actions: [
          { text: 'Top Up', action: 'topup', priority: balance < 50 ? 'high' : 'medium' },
          { text: 'View History', action: 'transactions' }
        ],
        suggestions
      };
    } catch (error) {
      console.error('Balance fetch error:', error);
      return {
        text: "I couldn't fetch your balance right now. Please try again later.",
        type: 'error',
        suggestions: ['Try again', 'Contact support']
      };
    }
  }

  /**
   * Get enhanced missions information
   */
  async getMissionsInfo(input) {
    try {
      let missions;
      
      if (input.includes('active') || input.includes('current')) {
        missions = await missionApi.getMyMissions('status=active');
      } else if (input.includes('find') || input.includes('new')) {
        missions = await missionApi.getMissions({ limit: 5 });
      } else {
        missions = await missionApi.getMyMissions();
      }

      if (!missions.data || missions.data.length === 0) {
        return {
          text: "You don't have any missions yet. Would you like me to find some opportunities for you?",
          type: 'empty',
          actions: [
            { text: 'Find Missions', action: 'find_missions' },
            { text: 'Create Mission', action: 'create_mission' }
          ],
          suggestions: ['Find missions for me', 'Create a new mission']
        };
      }

      let response = `🎯 You have ${missions.data.length} missions:\n\n`;
      missions.data.slice(0, 3).forEach(mission => {
        response += `• ${mission.title} - ${mission.status}\n`;
      });

      return {
        text: response,
        type: 'data',
        actions: [
          { text: 'View All', action: 'missions' },
          { text: 'Find More', action: 'find_missions' }
        ],
        suggestions: ['Show mission details', 'Find similar missions']
      };
    } catch (error) {
      return {
        text: "I couldn't fetch your missions right now. Please try again later.",
        type: 'error',
        suggestions: ['Try again', 'Go to missions page']
      };
    }
  }

  /**
   * Get personalized greeting
   */
  getPersonalizedGreeting() {
    const { user, profile } = this.context;
    const timeOfDay = this.getTimeOfDay();
    const userName = user?.name || profile?.name || 'there';
    
    const greetings = [
      `Good ${timeOfDay}, ${userName}! 👋 How can I help you today?`,
      `Hello ${userName}! Ready to tackle some missions?`,
      `Hi ${userName}! What would you like to accomplish today?`
    ];

    const greeting = greetings[Math.floor(Math.random() * greetings.length)];
    
    return {
      text: greeting,
      type: 'greeting',
      suggestions: this.getContextualSuggestions()
    };
  }

  /**
   * Get contextual suggestions based on user state
   */
  getContextualSuggestions() {
    const { wallet, profile } = this.context;
    const suggestions = [];

    if (wallet?.balance < 100) {
      suggestions.push('Top up credits');
    }
    
    if (!profile?.availability?.is_available) {
      suggestions.push('Set availability');
    }

    suggestions.push('Check my balance', 'Show active missions', 'Find new opportunities');
    
    return suggestions.slice(0, 4);
  }

  /**
   * Get enhanced help information
   */
  getEnhancedHelp() {
    return {
      text: `🤖 I'm your enhanced Mission X assistant! Here's what I can help you with:

💰 **Wallet & Finance**
• Check balance & transactions
• Top up credits
• Spending analytics
• Budget alerts

🎯 **Missions & Work**
• Find personalized missions
• Track active missions
• Application status
• Deadline reminders

👤 **Profile & Optimization**
• Profile improvement tips
• Service recommendations
• Availability management
• Performance analytics

📊 **Smart Insights**
• Earnings analytics
• Market trends
• Success tips
• Goal tracking

Just ask me naturally or use commands like !balance, !missions, !analytics`,
      type: 'help',
      commands: [
        '!balance', '!missions', '!analytics', '!schedule', 
        'Find missions', 'Optimize profile', 'Top up credits'
      ]
    };
  }

  /**
   * Get fallback response with suggestions
   */
  getFallbackResponse(input) {
    const fallbacks = [
      "I'm not sure about that, but I can help you with missions, wallet, or profile management.",
      "Could you be more specific? I can assist with balance checks, finding missions, or optimizing your profile.",
      "I didn't quite understand that. Try asking about your wallet, missions, or account settings."
    ];

    return {
      text: fallbacks[Math.floor(Math.random() * fallbacks.length)],
      type: 'fallback',
      suggestions: this.getContextualSuggestions()
    };
  }

  /**
   * Utility methods
   */
  getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
  }

  async getTalentChecklist() {
    try {
      const res = await profileService.getCompleteProfile();
      if (!res?.success || !res.data) {
        throw new Error('profile fetch failed');
      }

      const data = res.data;
      // Update context with latest profile data
      this.updateContext({ profile: data });

      const services = data.services || data.service || [];
      const hasProfilePicture = !!data.profile_picture;
      const photos = data.profile_media?.photos || [];
      const hasCoverMedia = Array.isArray(photos) && photos.length > 0 && photos.every(p => p?.path);
      const hasBiography = !!data.biography;
      const hasApprovedService = Array.isArray(services)
        ? services.length > 0
        : !!services && Object.keys(services).length > 0;

      const checklist = [
        { label: 'Profile Picture', done: hasProfilePicture },
        { label: 'Cover Media (all)', done: hasCoverMedia },
        { label: 'Biography', done: hasBiography },
        { label: 'Approved Service (at least one)', done: hasApprovedService }
      ];

      const allDone = checklist.every(item => item.done);

      return {
        text: "Here's your talent checklist:",
        type: 'talent-checklist',
        checklist,
        allDone
      };
    } catch (error) {
      console.error('Talent checklist error:', error);
      return {
        text: 'Sorry, I could not check your talent requirements right now.',
        type: 'error'
      };
    }
  }

  async getProfileSuggestions() {
    const { profile } = this.context;
    const suggestions = [];

    if (!profile?.bio || profile.bio.length < 50) {
      suggestions.push('• Add a detailed bio to attract more clients');
    }
    
    if (!profile?.services || profile.services.length === 0) {
      suggestions.push('• Add services to showcase your skills');
    }

    if (!profile?.media || profile.media.length === 0) {
      suggestions.push('• Upload portfolio images to stand out');
    }

    return suggestions;
  }
}

const assistantAIInstance = new AssistantAI();

export { AssistantAI };
export default assistantAIInstance;
