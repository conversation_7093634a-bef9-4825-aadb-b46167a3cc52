/**
 * Talent Service
 *
 * This service handles API calls related to talent filtering and retrieval.
 * Connects to the real backend API endpoints for talent data.
 */

import { createApi } from './api';
import { formatTalentForDisplay } from '../models/TalentModel';
import { convertFiltersToApiFormat } from '../utils/filterUtils';
import { handleApiError } from '../utils/apiErrorHandler';
import { cacheManager } from '../utils/cacheUtils';
import api from './api';

// Base API URL
const API_URL = '/api';

/**
 * Get the authentication token from localStorage
 * @returns {string|null} The authentication token or null if not found
 */
const getAuthToken = () => {
  return localStorage.getItem('token');
};

/**
 * Configure axios with authentication
 */
const apiClient = createApi({ baseURL: API_URL, headers: { 'Content-Type': 'application/json' } });

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const profileCache = new Map();

/**
 * Cache a talent profile
 * @param {string} talentId - The talent ID
 * @param {Object} profileData - The profile data to cache
 */
export const cacheProfile = (talentId, profileData) => {
    profileCache.set(talentId, {
        data: profileData,
        timestamp: Date.now()
    });
};

/**
 * Get a cached talent profile
 * @param {string} talentId - The talent ID
 * @returns {Object|null} The cached profile data or null if not found/expired
 */
export const getCachedProfile = (talentId) => {
    const cached = profileCache.get(talentId);
    if (!cached) return null;

    // Check if cache is expired
    if (Date.now() - cached.timestamp > CACHE_DURATION) {
        profileCache.delete(talentId);
        return null;
    }

    return cached.data;
};

/**
 * Clear the profile cache for a specific talent
 * @param {string} talentId - The talent ID
 */
export const clearProfileCache = (talentId) => {
    profileCache.delete(talentId);
};

/**
 * Clear all profile caches
 */
export const clearAllProfileCaches = () => {
    profileCache.clear();
};

/**
 * Fetch talents without any filters
 * @param {number} page - Page number for pagination
 * @param {number} perPage - Number of results per page
 * @returns {Promise<Object>} Talents data with pagination metadata
 */
export const getTalents = async (page = 1, perPage = 15) => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
    });

    const response = await api.get(`/talents?${queryParams}`);

    if (!response.data) {
      throw new Error('Invalid response format');
    }

    return {
      data: response.data.data || [],
      meta: {
        current_page: response.data.current_page || 1,
        last_page: response.data.last_page || 1,
        total: response.data.total || (response.data.data?.length || 0),
        per_page: response.data.per_page || perPage,
      },
    };
  } catch (error) {
    console.error('Error fetching talents:', error);
    return {
      error: {
        message: error.response?.data?.message || 'Failed to fetch talents',
      },
    };
  }
};

/**
 * Filter talents based on criteria
 * @param {Object} filters - Filter criteria
 * @param {number} page - Page number for pagination
 * @param {number} perPage - Number of results per page
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Object>} Filtered talents with pagination metadata
 * @throws {Object} Standardized error object
 */
export const filterTalents = async (filters = {}, page = 1, perPage = 15, skipCache = false) => {
  // Only cache certain filter combinations to avoid cache explosion
  // We'll cache:
  // 1. Empty filters (default listing)
  // 2. Single category/type/style filters
  // 3. Common combinations like gender + level
  const shouldCache =
    Object.keys(filters).length === 0 || // Empty filters
    (Object.keys(filters).length === 1 &&
      (filters.serviceCategoryId ||
       filters.serviceTypeId ||
       filters.serviceStyleId ||
       filters.gender)) ||
    (Object.keys(filters).length === 2 &&
      filters.gender &&
      (filters.experienceLevel || filters.serviceTypeId));

  if (shouldCache) {
    const cacheKey = cacheManager.getTalentFiltersKey(filters, page, perPage);

    // Check if data is in cache and we're not skipping cache
    if (!skipCache && cacheManager.has(cacheKey)) {
      return cacheManager.get(cacheKey);
    }
  }

  try {
    // Convert camelCase filter keys to the API's expected snake_case keys
    const apiFilters = convertFiltersToApiFormat(filters);

    console.log('Original filters:', filters); // Debug log
    console.log('Converted API filters:', apiFilters); // Debug log

    // Filter out undefined values from apiFilters
    const cleanApiFilters = Object.fromEntries(
      Object.entries(apiFilters).filter(([key, value]) => value !== undefined && value !== null && value !== '')
    );

    console.log('Cleaned API filters:', cleanApiFilters); // Debug log

    const queryParams = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
      ...cleanApiFilters
    });

    console.log('Final query params:', queryParams.toString()); // Debug log
    console.log('API URL:', `/talents?${queryParams}`); // Debug log

    const response = await api.get(`/talents?${queryParams}`);
    
    if (!response.data) {
      throw new Error('Invalid response format');
    }

    // Ensure data is properly formatted
    const formattedData = Array.isArray(response.data.data) 
      ? response.data.data.map(talent => ({
          ...talent,
          profileImage: talent.profileImage || '/images/default-profile.png',
          displayName: talent.displayName || talent.username || 'Unknown Talent',
          level: talent.level || 0,
          serviceType: talent.serviceType || 'No Service Type',
          serviceDescription: talent.serviceDescription || 'No description available'
        }))
      : [];

    const result = {
      data: formattedData,
      meta: response.data.meta || {
        current_page: 1,
        last_page: 1,
        total: 0,
        per_page: perPage
      }
    };

    // Cache the result if it meets our caching criteria
    if (shouldCache) {
      const cacheKey = cacheManager.getTalentFiltersKey(filters, page, perPage);
      cacheManager.set(cacheKey, result);
    }

    return result;
  } catch (error) {
    console.error('Error in filterTalents:', error);
    return {
      error: {
        message: error.response?.data?.message || 'Failed to fetch talents'
      }
    };
  }
};

/**
 * Get service types, optionally filtered by service category
 * @param {number|null} serviceCategoryId - Service category ID to filter by
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of service types
 * @throws {Object} Standardized error object
 */
export const getServiceTypes = async (serviceCategoryId = null, skipCache = false) => {
  const cacheKey = cacheManager.getServiceTypesKey(serviceCategoryId);

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/talents/service-types', {
      params: {
        service_category_id: serviceCategoryId
      }
    });

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching service types:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get service styles, optionally filtered by service category
 * @param {number|null} serviceCategoryId - Service category ID to filter by
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of service styles
 * @throws {Object} Standardized error object
 */
export const getServiceStyles = async (serviceCategoryId = null, skipCache = false) => {
  const cacheKey = cacheManager.getServiceStylesKey(serviceCategoryId);

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    // Fetch service styles scoped to talents
    const response = await api.get('/talents/service-styles', {
      params: {
        service_category_id: serviceCategoryId
      }
    });

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching service styles:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get a single talent by ID
 * @param {number} talentId - Talent ID to fetch
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Object>} Talent object
 */
export const getTalentById = async (talentId, skipCache = false) => {
  const cacheKey = cacheManager.getTalentKey(talentId);

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get(`/talents/${talentId}`);

    // Format talent for display
    const formattedTalent = formatTalentForDisplay(response.data);

    // Cache the response
    cacheManager.set(cacheKey, formattedTalent);

    return formattedTalent;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error(`Error fetching talent with ID ${talentId}:`, handledError);

    // Return null instead of throwing
    return null;
  }
};

/**
 * Fetch full talent profile with services, media and reviews
 * @param {number|string} talentId - ID of the talent
 * @returns {Promise<Object|null>} Talent object or null on error
 */
export const getTalentFullProfile = async (talentId) => {
  try {
    // Use the api instance instead of apiClient to ensure proper base URL
    const response = await api.get(`/user/all-profile/${talentId}`);
    
    if (!response.data) {
      throw new Error('No data received from server');
    }
    
    return formatTalentForDisplay(response.data);
  } catch (error) {
    const handledError = handleApiError(error);
    console.error(`Error fetching full profile for talent ${talentId}:`, handledError);
    
    // Throw the error instead of returning null to trigger proper error handling
    throw handledError;
  }
};

/**
 * Fetch basic talent info
 * @param {number|string} talentId - ID of the talent
 * @returns {Promise<Object|null>} Talent object or null
 */
export const getTalentBasicProfile = async (talentId) => {
  try {
    // Use the api instance to ensure the correct base URL
    const response = await api.get(`/user/profile/${talentId}`);
    return formatTalentForDisplay(response.data);
  } catch (error) {
    const handledError = handleApiError(error);
    console.error(
      `Error fetching basic profile for talent ${talentId}:`,
      handledError,
    );
    return null;
  }
};

/**
 * Get service categories
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of service categories
 */
export const getServiceCategories = async (skipCache = false) => {
  const cacheKey = cacheManager.getKey('serviceCategories');

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/service-configuration/categories');

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching service categories:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get races
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of races
 */
export const getRaces = async (skipCache = false) => {
  const cacheKey = cacheManager.getKey('races');

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/races');

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching races:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get languages
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of languages
 */
export const getLanguages = async (skipCache = false) => {
  const cacheKey = cacheManager.getKey('languages');

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/languages');

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching languages:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Toggle follow/unfollow for a talent (user)
 * @param {number|string} userId - The ID of the user to follow/unfollow
 * @returns {Promise<Object>} The API response
 */
export const toggleFollow = async (userId) => {
  try {
    const response = await api.post(`/users/${userId}/follow`);
    return response.data;
  } catch (error) {
    const handledError = handleApiError(error);
    console.error('Error toggling follow:', handledError);
    throw handledError;
  }
};

/**
 * Fetch reasons for blocking a user
 * @returns {Promise<Array>} Array of blocked reasons
 */
export const getBlockedReasons = async () => {
  try {
    const response = await api.get('/blocked-reasons');
    return Array.isArray(response.data) ? response.data : response.data?.data || [];
  } catch (error) {
    const handledError = handleApiError(error);
    console.error('Error fetching blocked reasons:', handledError);
    throw handledError;
  }
};

/**
 * Fetch list of blocked users for the current user
 * @returns {Promise<Array>} Array of blocked users
 */
export const getBlockedUsers = async () => {
  try {
    const response = await api.get('/user/blocked');
    // Backend returns { data: [...] }
    return Array.isArray(response.data?.data) ? response.data.data : [];
  } catch (error) {
    const handledError = handleApiError(error);
    console.error('Error fetching blocked users:', handledError);
    throw handledError;
  }
};

/**
 * Block a user with a reason
 * @param {number|string} userId - The ID of the user to block
 * @param {number|string} reasonId - The ID of the reason
 * @returns {Promise<Object>} The API response
 */
export const blockUser = async (userId, reasonId) => {
  try {
    const response = await api.post(`/users/${userId}/block`, {
      blocked_reason_id: reasonId,
    });
    return response.data;
  } catch (error) {
    const handledError = handleApiError(error);
    console.error('Error blocking user:', handledError);
    throw handledError;
  }
};

/**
 * Unblock a previously blocked user
 * @param {number|string} userId - The ID of the user to unblock
 * @returns {Promise<Object>} The API response
 */
export const unblockUser = async (userId) => {
  try {
    const response = await api.post(`/users/${userId}/block`, {
      blocked_reason_id: null,
    });
    return response.data;
  } catch (error) {
    const handledError = handleApiError(error);
    console.error('Error unblocking user:', handledError);
    throw handledError;
  }
};

export default {
  getTalents,
  filterTalents,
  getServiceTypes,
  getServiceStyles,
  getTalentById,
  getTalentFullProfile,
  getTalentBasicProfile,
  getServiceCategories,
  getRaces,
  getLanguages,
  cacheProfile,
  getCachedProfile,
  clearProfileCache,
  clearAllProfileCaches,
  toggleFollow,
  getBlockedReasons,
  getBlockedUsers,
  blockUser,
  unblockUser,
};
