import { createApi } from './api';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

// Helper function to simulate API delay for development
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Create API instance with auth headers
const api = createApi({ baseURL: API_BASE_URL, headers: { 'Content-Type': 'application/json' } });

// Mock data for development
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Enhanced Verification Service with caching and verification-first approach
 */
class VerificationService {
  constructor() {
    this.cache = {
      verificationStatus: null,
      lastStatusUpdate: null,
      withdrawalEligibility: null,
      lastEligibilityCheck: null
    };

    // Cache duration: 5 minutes for verification status
    this.VERIFICATION_CACHE_DURATION = 5 * 60 * 1000;
  }

  /**
   * Check if cached data is still valid
   */
  isCacheValid(lastUpdate, duration) {
    if (!lastUpdate) return false;
    return Date.now() - lastUpdate < duration;
  }

  /**
   * Clear all verification caches
   */
  clearCache() {
    this.cache = {
      verificationStatus: null,
      lastStatusUpdate: null,
      withdrawalEligibility: null,
      lastEligibilityCheck: null
    };
  }

  /**
   * Get unified verification status
   */
  async getVerificationStatus(forceRefresh = false) {
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && this.isCacheValid(this.cache.lastStatusUpdate, this.VERIFICATION_CACHE_DURATION)) {
      return this.cache.verificationStatus;
    }

    try {
      const response = await api.get('/ekyc/status');
      const status = response.data;

      // Create unified verification object
      const verificationStatus = {
        emailVerified: status.email_verified || false,
        kycVerified: status.is_verified === true, // Backend returns is_verified boolean, not status string
        kycStatus: status.is_verified ? 'verified' : 'not_verified',
        canWithdraw: status.email_verified && status.is_verified === true,
        missingRequirements: this.getMissingRequirements(status),
        submittedAt: status.verified_at,
        verificationLevel: this.calculateVerificationLevel(status)
      };

      // Update cache
      this.cache.verificationStatus = verificationStatus;
      this.cache.lastStatusUpdate = Date.now();

      return verificationStatus;
    } catch (error) {
      console.error('Error fetching verification status:', error);

      // Return cached data if available during error
      if (this.cache.verificationStatus) {
        return this.cache.verificationStatus;
      }

      // Return default unverified status
      return {
        emailVerified: false,
        kycVerified: false,
        kycStatus: 'not_submitted',
        canWithdraw: false,
        missingRequirements: ['email_verification', 'kyc_verification'],
        submittedAt: null,
        verificationLevel: 'unverified'
      };
    }
  }

  /**
   * Get missing requirements for verification
   */
  getMissingRequirements(status) {
    const missing = [];

    if (!status.email_verified) {
      missing.push('email_verification');
    }

    if (status.is_verified !== true) {
      missing.push('kyc_verification');
    }

    return missing;
  }

  /**
   * Calculate verification level based on status
   */
  calculateVerificationLevel(status) {
    if (status.email_verified && status.is_verified === true) return 'fully_verified';
    if (status.is_verified === true) return 'kyc_verified';
    if (status.email_verified) return 'email_verified';
    return 'unverified';
  }

  /**
   * Check if user can access withdrawal features
   */
  canAccessWithdrawals(verificationStatus) {
    if (!verificationStatus) return false;
    return verificationStatus.emailVerified && verificationStatus.kycVerified;
  }

  /**
   * Send email verification
   */
  async sendEmailVerification() {
    try {
      const response = await api.post('/email/send-verification');

      // Clear cache to force refresh on next status check
      this.cache.verificationStatus = null;
      this.cache.lastStatusUpdate = null;

      return {
        success: true,
        message: response.data?.message || 'Verification email sent successfully'
      };
    } catch (error) {
      console.error('Error sending email verification:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to send verification email'
      };
    }
  }
}

// Create service instance
const verificationServiceInstance = new VerificationService();

// Legacy compatibility wrapper
const verificationService = isDevelopment
  ? {
      // Get user's verification status (legacy format for backward compatibility)
      getVerificationStatus: async () => {
        await delay(800);

        // Get from localStorage if available, otherwise create random mock data
        const mockStatus = {
          email_verified: (typeof window !== 'undefined' ? localStorage.getItem('email_verified') === 'true' : false) || Math.random() > 0.5,
          kyc_verified: (typeof window !== 'undefined' ? localStorage.getItem('kyc_verified') === 'true' : false) || Math.random() > 0.5,
          bank_accounts_count: parseInt((typeof window !== 'undefined' ? localStorage.getItem('bank_accounts_count') : '0') || '0')
        };

        // Store in localStorage for persistence in development
        localStorage.setItem('email_verified', mockStatus.email_verified);
        localStorage.setItem('kyc_verified', mockStatus.kyc_verified);

        return {
          data: {
            status: mockStatus,
            message: 'Verification status retrieved successfully'
          }
        };
      },

      // Check if user can withdraw (all requirements met)
      canWithdraw: async () => {
        await delay(500);

        // Get verification status
        const emailVerified = localStorage.getItem('email_verified') === 'true';
        const kycVerified = localStorage.getItem('kyc_verified') === 'true';
        const bankAccountsCount = parseInt(localStorage.getItem('bank_accounts_count') || '0');

        const canWithdraw = emailVerified && kycVerified && bankAccountsCount > 0;
        const requirements = {
          email_verified: emailVerified,
          kyc_verified: kycVerified,
          has_bank_account: bankAccountsCount > 0
        };

        const missingRequirements = [];
        if (!emailVerified) missingRequirements.push('email_verification');
        if (!kycVerified) missingRequirements.push('kyc_verification');
        if (bankAccountsCount === 0) missingRequirements.push('bank_account');

        return {
          data: {
            can_withdraw: canWithdraw,
            requirements,
            missing_requirements: missingRequirements,
            message: canWithdraw ? 'User can withdraw funds' : 'User cannot withdraw funds'
          }
        };
      },

      // Verify user's email (mock implementation)
      verifyEmail: async (verificationCode) => {
        await delay(1000);

        if (verificationCode === '123456') {
          localStorage.setItem('email_verified', 'true');
          return {
            data: {
              success: true,
              message: 'Email verified successfully'
            }
          };
        } else {
          throw new Error('Invalid verification code');
        }
      },

      // Submit KYC verification (mock implementation)
      submitKYCVerification: async (kycData) => {
        await delay(1500);

        // Simulate successful KYC submission
        localStorage.setItem('kyc_status', 'pending');

        return {
          data: {
            success: true,
            message: 'KYC verification submitted successfully and is pending review'
          }
        };
      },

      // Check KYC status (mock implementation)
      getKYCStatus: async () => {
        await delay(600);

        // Get from localStorage if available, or generate random mock status
        const kycStatus = localStorage.getItem('kyc_status') ||
          (Math.random() > 0.7 ? 'approved' : Math.random() > 0.5 ? 'pending' : 'not_submitted');

        // If approved, set kyc_verified to true
        if (kycStatus === 'approved') {
          localStorage.setItem('kyc_verified', 'true');
        }

        return {
          data: {
            status: kycStatus,
            message: 'KYC status retrieved successfully'
          }
        };
      }
    }
  : {
      // Real API implementations
      getVerificationStatus: async () => {
        return api.get('/users/verification-status');
      },

      canWithdraw: async () => {
        return api.get('/withdrawals/check-eligibility');
      },

      verifyEmail: async (verificationCode) => {
        return api.post('/users/verify-email', { code: verificationCode });
      },

      submitKYCVerification: async (kycData) => {
        // Note: This is deprecated - use ekycService.verifyMalaysian() or ekycService.verifyForeigner() instead
        // This endpoint doesn't exist - keeping for backward compatibility but will fail
        return api.post('/users/kyc', kycData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
      },

      getKYCStatus: async () => {
        // Updated to use correct E-KYC endpoint
        return api.get('/ekyc/status');
      }
    };

// Enhanced service methods (available in both development and production)
verificationService.enhanced = verificationServiceInstance;

// Add enhanced methods to main service object
verificationService.getEnhancedVerificationStatus = verificationServiceInstance.getVerificationStatus.bind(verificationServiceInstance);
verificationService.canAccessWithdrawals = verificationServiceInstance.canAccessWithdrawals.bind(verificationServiceInstance);
verificationService.sendEmailVerification = verificationServiceInstance.sendEmailVerification.bind(verificationServiceInstance);
verificationService.clearVerificationCache = verificationServiceInstance.clearCache.bind(verificationServiceInstance);

export default verificationService;