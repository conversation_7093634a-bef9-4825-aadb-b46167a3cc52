import api from './api';

// Simulated delay for development (remove in production)
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Check if in development environment
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Process media files to add URL and type properties
 * @param {Array} mediaFiles - Array of media file objects from the backend
 * @returns {Array} - Processed media files with URL and type properties
 */
const processMediaFiles = (mediaFiles = []) => {
    const mimeMap = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'webp': 'image/webp',
        'gif': 'image/gif',
        'mp4': 'video/mp4',
        'mov': 'video/quicktime',
        'avi': 'video/x-msvideo',
        'webm': 'video/webm'
    };

    return mediaFiles.map(file => {
        // If the file already has a URL property, return it as is
        if (file.url && (file.url.startsWith('http://') || file.url.startsWith('https://'))) {
            return file;
        }

        // Determine the file type using available information
        let fileType = 'image/jpeg';
        if (file.mime_type) {
            fileType = file.mime_type;
        } else if (file.type && file.type.includes('/')) {
            // Backend may already supply a proper mime type
            fileType = file.type;
        } else if (file.type === 'video' || file.type === 'image') {
            const ext = (file.original || '').split('.').pop().toLowerCase();
            fileType = mimeMap[ext] || (file.type === 'video' ? 'video/mp4' : 'image/jpeg');
        } else if (file.extension) {
            fileType = mimeMap[file.extension.toLowerCase()] || 'image/jpeg';
        } else {
            const ext = (file.original || '').split('.').pop().toLowerCase();
            if (mimeMap[ext]) {
                fileType = mimeMap[ext];
            }
        }

        // Use optimized version if available, otherwise use original
        const filePath = file.optimized || file.original;

        // Add URL property using CDN URL from environment
        return {
            ...file,
            type: fileType,
            url: `${process.env.REACT_APP_CDN_URL}/${filePath}`
        };
    });
};

// Transform a single comment from the backend into the format expected by the frontend
const transformComment = (comment) => {
    if (!comment) return null;

    const user = comment.user || {};

    return {
        id: comment.id,
        post_id: comment.social_post_id,
        parent_id: comment.parent_id,
        content: comment.content,
        created_at: comment.created_at,
        // Include like data from backend
        is_liked: comment.is_liked || false,
        total_likes: comment.total_likes || 0,
        user: {
            id: user.id || 0,
            username: user.nickname || user.name || 'Unknown User',
            name: user.name || user.nickname || 'Unknown User',
            profile_picture: user.profile_picture || null
        },
        // Recursively transform nested replies if present
        replies: Array.isArray(comment.replies)
            ? comment.replies.map(transformComment)
            : []
    };
};

// Social Post API Service
const socialPostService = {
    // Get user's feed (For You or Following)
    getFeed: async (feedType = 'for_you', page = 1, limit = 10) => {
        try {
            // Default parameters
            const params = {
                page,
                per_page: limit,
            };
            if (feedType === 'following') {
                params.is_following = true;
            }

            // Use backend social-posts index endpoint which applies the
            // "For You" algorithm when no filters are provided.
            const response = await api.get('/social-posts', {
                params,
            });

            // Transform backend response to match frontend expectations
            const posts = response.data.data.map(post => ({
                id: post.id,
                title: post.title,
                description: post.description,
                media_files: processMediaFiles(post.media_files),
                user: {
                    id: post.user.id,
                    username: post.user.nickname || post.user.name,
                    name: post.user.name || post.user.nickname,
                    profile_picture: post.user.profile_picture,
                    level: post.user.level || null
                },
                location_data: post.location_data,
                created_at: post.created_at,
                total_liked: post.total_liked || 0,
                total_comments: post.total_comments || 0,
                is_liked: post.is_liked || false,
                is_hidden: post.is_hidden,
                is_featured: post.is_featured
            }));

            return {
                posts,
                has_more: response.data.next_page_url !== null,
                next_page: response.data.current_page + 1,
                total: response.data.total,
                current_page: response.data.current_page,
                last_page: response.data.last_page
            };
        } catch (error) {
            // Enhanced error handling
            if (error.response) {
                const err = new Error(error.response.data.message || 'Failed to fetch feed');
                err.response = error.response;
                throw err;
            } else if (error.request) {
                throw new Error('No response from server. Please check your internet connection.');
            } else {
                throw error;
            }
        }
    },

    // Get all social posts (alternative feed method)
    getAllPosts: async (page = 1, limit = 10) => {
        try {
            const response = await api.get('/social-posts', {
                params: {
                    page,
                    per_page: limit
                }
            });

            // Transform backend response
            const posts = response.data.data.map(post => ({
                id: post.id,
                title: post.title,
                description: post.description,
                media_files: processMediaFiles(post.media_files),
                user: {
                    id: post.user.id,
                    username: post.user.nickname || post.user.name,
                    name: post.user.name || post.user.nickname,
                    profile_picture: post.user.profile_picture,
                    level: post.user.level || null
                },
                location_data: post.location_data,
                created_at: post.created_at,
                total_liked: post.total_liked || 0,
                total_comments: post.total_comments || 0,
                is_liked: post.is_liked || false,
                is_hidden: post.is_hidden,
                is_featured: post.is_featured
            }));

            return {
                posts,
                has_more: response.data.next_page_url !== null,
                next_page: response.data.current_page + 1,
                total: response.data.total,
                current_page: response.data.current_page,
                last_page: response.data.last_page
            };
        } catch (error) {
            console.error('Error fetching posts:', error);
            throw error;
        }
    },

    // Get random posts for discovery
    getRandomPosts: async (limit = 10) => {
        try {
            const response = await api.get('/social-posts/random', {
                params: { limit }
            });

            // Transform backend response
            const posts = response.data.map(post => ({
                id: post.id,
                title: post.title,
                description: post.description,
                media_files: processMediaFiles(post.media_files),
                user: {
                    id: post.user.id,
                    username: post.user.nickname || post.user.name,
                    name: post.user.name || post.user.nickname,
                    profile_picture: post.user.profile_picture,
                    level: post.user.level || null
                },
                location_data: post.location_data,
                created_at: post.created_at,
                total_liked: post.total_liked || 0,
                total_comments: post.total_comments || 0,
                is_liked: post.is_liked || false,
                is_hidden: post.is_hidden,
                is_featured: post.is_featured
            }));

            return { posts };
        } catch (error) {
            console.error('Error fetching random posts:', error);
            throw error;
        }
    },

    // Get post details by ID
    getPostById: async (postId) => {
        try {
            const response = await api.get(`/social-posts/${postId}`);
            const post = response.data;

            // Process comments (and their replies) so each has a valid user object
            const processedComments = (post.comments || []).map(transformComment);

            // Transform backend response
            return {
                id: post.id,
                title: post.title,
                description: post.description,
                media_files: processMediaFiles(post.media_files),
                user: {
                    id: post.user.id,
                    username: post.user.nickname || post.user.name,
                    name: post.user.name || post.user.nickname,
                    profile_picture: post.user.profile_picture,
                    level: post.user.level || null
                },
                location_data: post.location_data,
                created_at: post.created_at,
                total_liked: post.total_liked || 0,
                total_comments: post.total_comments || 0,
                is_liked: post.is_liked || false,
                is_hidden: post.is_hidden,
                is_featured: post.is_featured,
                comments: processedComments
            };
        } catch (error) {
            console.error('Error fetching post:', error);
            throw error;
        }
    },

    // Get details of a single comment, including replies
    getCommentById: async (postId, commentId) => {
        try {
            const response = await api.get(`/social-posts/${postId}/comments/${commentId}`);
            return transformComment(response.data);
        } catch (error) {
            console.error('Error fetching comment details:', error);
            throw error;
        }
    },

    // Get comments for a post with their replies
    getPostComments: async (postId, page = 1, limit = 10) => {
        try {
            const response = await api.get(`/social-posts/${postId}/comments`, {
                params: {
                    page,
                    per_page: limit
                }
            });

            // Fetch replies for each top-level comment
            const comments = await Promise.all(
                response.data.data.map(async (comment) => {
                    try {
                        const detailRes = await api.get(`/social-posts/${postId}/comments/${comment.id}`);
                        return transformComment(detailRes.data);
                    } catch (err) {
                        console.warn('Error fetching replies for comment', comment.id, err);
                        return transformComment(comment);
                    }
                })
            );

            return {
                comments,
                has_more: response.data.next_page_url !== null,
                next_page: response.data.current_page + 1,
                total: response.data.total,
                current_page: response.data.current_page,
                last_page: response.data.last_page
            };
        } catch (error) {
            console.error('Error fetching post comments:', error);
            throw error;
        }
    },

    // Create a new post
    createPost: async (postData, onUploadProgress = null) => {
        try {
            // Create FormData for file uploads
            const formData = new FormData();

            // Add text fields
            if (postData.title) formData.append('title', postData.title);
            if (postData.description) formData.append('description', postData.description);

            // Add location data if provided
            if (postData.location_data) {
                formData.append('location_data', JSON.stringify(postData.location_data));
            }

            // Add media files
            if (postData.media_files && postData.media_files.length > 0) {
                postData.media_files.forEach((file, index) => {
                    if (file instanceof File) {
                        formData.append(`media_files[${index}]`, file);
                    }
                });
            }

            const response = await api.post('/social-posts', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: onUploadProgress
                    ? (evt) => {
                          const percent = Math.round((evt.loaded * 100) / evt.total);
                          onUploadProgress(percent);
                      }
                    : undefined,
            });

            // Some endpoints wrap the created post in a `data` or `post` key.
            // Fallback to the raw response when those keys are missing.
            const post = response.data?.data || response.data?.post || response.data;

            // Safely build the post object in the format expected by the UI.
            return {
                id: post.id,
                title: post.title,
                description: post.description,
                media_files: processMediaFiles(post.media_files),
                user: post.user
                    ? {
                        id: post.user.id,
                        username: post.user.nickname || post.user.name,
                        name: post.user.name || post.user.nickname,
                        profile_picture: post.user.profile_picture,
                    }
                    : null,
                location_data: post.location_data,
                created_at: post.created_at,
                total_liked: post.total_liked || 0,
                total_comments: post.total_comments || 0,
                is_liked: post.is_liked || false,
                is_hidden: post.is_hidden,
                is_featured: post.is_featured,
            };
        } catch (error) {
            console.error('Error creating post:', error);
            throw error;
        }
    },

    // Update an existing post (e.g., to add or modify location data)
    updatePost: async (postId, updateData) => {
        try {
            const payload = {};
            if (updateData.title) payload.title = updateData.title;
            if (updateData.description) payload.description = updateData.description;
            if (updateData.location_data) {
                payload.location_data = updateData.location_data;
            }

            const response = await api.put(`/social-posts/${postId}`, payload);
            const post = response.data?.data || response.data?.post || response.data;

            return {
                id: post.id,
                title: post.title,
                description: post.description,
                media_files: processMediaFiles(post.media_files),
                user: post.user
                    ? {
                        id: post.user.id,
                        username: post.user.nickname || post.user.name,
                        name: post.user.name || post.user.nickname,
                        profile_picture: post.user.profile_picture,
                    }
                    : null,
                location_data: post.location_data,
                created_at: post.created_at,
                total_liked: post.total_liked || 0,
                total_comments: post.total_comments || 0,
                is_liked: post.is_liked || false,
                is_hidden: post.is_hidden,
                is_featured: post.is_featured,
            };
        } catch (error) {
            console.error('Error updating post:', error);
            throw error;
        }
    },

    // Like a post
    likePost: async (postId) => {
        try {
            const response = await api.post(`/social-posts/${postId}/like`);
            return response.data;
        } catch (error) {
            console.error('Error liking post:', error);
            throw error;
        }
    },

    // Unlike a post
    unlikePost: async (postId) => {
        try {
            const response = await api.post(`/social-posts/${postId}/like`);
            return response.data;
        } catch (error) {
            console.error('Error unliking post:', error);
            throw error;
        }
    },

    // Toggle like state
    toggleLike: async (postId) => {
        try {
            const response = await api.post(`/social-posts/${postId}/like`);
            return response.data;
        } catch (error) {
            console.error('Error toggling like:', error);
            throw error;
        }
    },

    // Create a comment on a post
    createComment: async (postId, content, parentId = null) => {
        try {
            const response = await api.post(`/social-posts/${postId}/comments`, {
                content,
                parent_id: parentId
            });

            const comment = response.data;
            
            // The backend returns the comment without user information
            // We need to use the current authenticated user's information
            
            // First try to get the current user ID from the comment
            const commentUserId = comment.user_id;
            
            // If we don't have a user ID in the comment, try to get it from the auth token
            // This is a fallback and shouldn't normally be needed
            if (!commentUserId) {
                console.warn('Comment response missing user_id, using current authenticated user');
            }
            
            // Get current user profile from localStorage if available
            let currentUser = null;
            try {
                const userJson = localStorage.getItem('user');
                if (userJson) {
                    currentUser = JSON.parse(userJson);
                }
            } catch (e) {
                console.warn('Error parsing user data from localStorage:', e);
            }
            
            // If we couldn't get the current user from localStorage, create a minimal user object
            if (!currentUser) {
                // Try to get user ID from JWT token (decode it)
                let tokenUserId = null;
                try {
                    const token = localStorage.getItem('token');
                    if (token) {
                        // Simple JWT payload extraction (not full validation)
                        const base64Url = token.split('.')[1];
                        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                        const payload = JSON.parse(window.atob(base64));
                        tokenUserId = payload.sub; // standard JWT subject claim
                    }
                } catch (e) {
                    console.warn('Error extracting user ID from token:', e);
                }
                
                currentUser = {
                    id: commentUserId || tokenUserId || 1, // Fallback to 1 if all else fails
                    name: 'User',
                    nickname: 'User',
                    profile_picture: null
                };
            }

            // Transform backend response
            return {
                id: comment.id,
                post_id: comment.social_post_id,
                parent_id: comment.parent_id,
                content: comment.content,
                created_at: comment.created_at,
                // Include like data for new comments (start with 0 likes and not liked)
                is_liked: false,
                total_likes: 0,
                user: {
                    id: currentUser.id,
                    username: currentUser.nickname || currentUser.name,
                    name: currentUser.name || currentUser.nickname,
                    profile_picture: currentUser.profile_picture
                },
                replies: []
            };
        } catch (error) {
            console.error('Error creating comment:', error);
            throw error;
        }
    },

    // Search for places (for location tagging)
    searchPlaces: async (query) => {
        try {
            const response = await api.get('/places/search', {
                params: { query }
            });
            return response.data;
        } catch (error) {
            console.error('Error searching places:', error);
            throw error;
        }
    },

    // Get place details
    getPlaceDetails: async (placeId) => {
        try {
            const response = await api.get('/places/details', {
                params: { place_id: placeId }
            });
            return response.data;
        } catch (error) {
            console.error('Error getting place details:', error);
            throw error;
        }
    },

    // Create a reply to a comment (uses the same createComment method with parentId)
    createCommentReply: async (postId, commentId, content) => {
        try {
            return await socialPostService.createComment(postId, content, commentId);
        } catch (error) {
            console.error('Error creating comment reply:', error);
            throw error;
        }
    },

    // Search for users (for @mentions)
    searchUsers: async (query) => {
        try {
            const response = await api.get('/users/search', {
                params: { query }
            });

            // Transform backend response
            const users = response.data.map(user => ({
                id: user.id,
                username: user.nickname || user.name,
                name: user.name || user.nickname,
                profile_picture: user.profile_picture
            }));

            return users;
        } catch (error) {
            console.error('Error searching users:', error);
            throw error;
        }
    },

    // Get user profile by username
    getUserByUsername: async (username) => {
        try {
            const response = await api.get(`/users/${username}`);
            const user = response.data;

            // Transform backend response
            return {
                id: user.id,
                username: user.nickname || user.name,
                name: user.name || user.nickname,
                profile_picture: user.profile_picture,
                bio: user.bio || user.description
            };
        } catch (error) {
            console.error('Error fetching user profile:', error);
            throw error;
        }
    },

    // Toggle like state for a comment
    toggleCommentLike: async (socialPostId, commentId) => {
        try {
            const response = await api.post(`/social-posts/${socialPostId}/comments/${commentId}/like`);
            return response.data;
        } catch (error) {
            console.error('Error toggling comment like:', error);
            throw error;
        }
    },

    // Delete a post
    deletePost: async (postId) => {
        try {
            const response = await api.delete(`/social-posts/${postId}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting post:', error);
            throw error;
        }
    }
};

export { transformComment };
export default socialPostService;