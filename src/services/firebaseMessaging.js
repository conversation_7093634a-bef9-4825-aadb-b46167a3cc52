import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { app as firebaseApp } from '../firebase/config';

// Local storage keys for caching token and registration status
const TOKEN_KEY = 'fcm_token';
const TOKEN_REGISTERED_KEY = 'fcm_token_registered';

// Get messaging instance
let messaging = null;
let currentToken = localStorage.getItem(TOKEN_KEY) || null;
let tokenRegistered = localStorage.getItem(TOKEN_REGISTERED_KEY) === 'true';
let listenerRegistered = false;
let onMessageUnsubscribe = null;

// Check if browser supports service worker and Firebase messaging
const isMessagingSupported = () => {
  return 'serviceWorker' in navigator && 'Notification' in window;
};

// Initialize Firebase messaging if supported
if (isMessagingSupported()) {
  try {
    messaging = getMessaging(firebaseApp);
  } catch (error) {
    console.error('Failed to initialize Firebase messaging:', error);
  }
}

// Store active notification handlers
const notificationHandlers = {};

/**
 * Register a handler for a specific notification type
 * @param {string} type - Notification type identifier
 * @param {Function} handler - Handler function for this notification type
 */
const registerNotificationHandler = (type, handler) => {
  notificationHandlers[type] = handler;
};

/**
 * Unregister a handler for a specific notification type
 * @param {string} type - Notification type identifier
 * @param {Function} handler - Handler function to remove (optional, for future extensibility)
 */
const unregisterNotificationHandler = (type, handler) => {
  if (notificationHandlers[type]) {
    delete notificationHandlers[type];
  }
};

/**
 * Process incoming notification and route to appropriate handler
 * @param {Object} payload - Notification payload
 */
const processNotification = (payload) => {
  try {
    const normalized = { ...payload, data: { ...payload.data } };
    if (!normalized.data.type && normalized.data.notification_type) {
      normalized.data.type = normalized.data.notification_type;
    }
    const { notification, data } = normalized;
    // console.log('[FCM] Notification received:', notification, 'Data:', data);

    // Extract notification type from data
    const notificationType = data?.type || 'default';

    // Check if a handler is registered for this notification type
    if (notificationHandlers[notificationType]) {
      notificationHandlers[notificationType](normalized);
    } else if (notificationHandlers.default) {
      // Use default handler if specific handler not found
      notificationHandlers.default(normalized);
    } else {
      // If no handlers registered, display basic browser notification
      if (notification && Notification.permission === 'granted') {
        const notificationOptions = {
          body: notification.body,
          icon: notification.icon || '/logo192.png',
          tag: data?.id || 'default',
          data
        };

        const notificationInstance = new Notification(notification.title, notificationOptions);

        // Add click handler for deep linking
        notificationInstance.onclick = () => {
          // Handle deep linking based on notification data
          if (data?.url) {
            window.open(data.url, '_blank');
          } else if (data?.route) {
            window.location.href = data.route;
          } else {
            window.focus();
          }
          notificationInstance.close();
        };
      }
    }
  } catch (error) {
    console.error('[FCM] Error processing notification:', error);
  }
};

/**
 * Request notification permission and register device
 * @returns {Promise<string>} FCM token for the device
 */
const requestNotificationPermission = async () => {
  try {
    if (currentToken) {
      if (!listenerRegistered) {
        onMessageUnsubscribe = onMessage(messaging, processNotification);
        listenerRegistered = true;
      }
      return currentToken;
    }

    if (!isMessagingSupported() || !messaging) {
      throw new Error('Notifications not supported on this device or browser');
    }

    // Check permission status
    const permission = await Notification.requestPermission();

    if (permission !== 'granted') {
      throw new Error('Notification permission denied');
    }

    // Register FCM token
    const token = await getToken(messaging, {
      vapidKey: process.env.REACT_APP_FIREBASE_VAPID_KEY
    });

    if (!token) {
      throw new Error('Failed to retrieve FCM token');
    }

    currentToken = token;
    localStorage.setItem(TOKEN_KEY, currentToken);

    // Set up notification listener
    if (!listenerRegistered) {
      onMessageUnsubscribe = onMessage(messaging, processNotification);
      listenerRegistered = true;
    }

    // Return token for registering with the backend
    return currentToken;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    throw error;
  }
};

/**
 * Register FCM token with the backend
 * @param {string} token - FCM token
 * @returns {Promise<Object>} Response from the server
 */
const registerDeviceToken = async (token) => {
  try {
    console.log('[FCM] Attempting to register device token:', token);
    if (tokenRegistered) {
      console.log('[FCM] Token already registered, skipping registration.');
      return { success: true, message: 'Token already registered' };
    }

    // Check if we have a token to register
    if (!token) {
      console.warn('[FCM] No FCM token available to register');
      return { success: false, message: 'No token available' };
    }

    // Get authentication token if needed
    const authToken = localStorage.getItem('token');
    if (!authToken) {
      console.warn('[FCM] User not authenticated, skipping FCM token registration');
      return { success: false, message: 'User not authenticated' };
    }

    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';
    console.log('[FCM] Sending device token to backend:', apiUrl);
    const response = await fetch(`${apiUrl}/auth/user/device-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        device_token: token,
        device_type: 'web',
        device_name: navigator.userAgent
      })
    });

    // Check if response is OK before trying to parse JSON
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[FCM] HTTP error! Status: ${response.status}, Body:`, errorText);
      return {
        success: false,
        status: response.status,
        message: `Server returned ${response.status}`
      };
    }

    // Parse the response JSON
    try {
      const data = await response.json();
      console.log('[FCM] Device token registered successfully:', data);
      tokenRegistered = true;
      localStorage.setItem(TOKEN_REGISTERED_KEY, 'true');
      return {
        success: true,
        data: data
      };
    } catch (error) {
      console.error('[FCM] Error parsing response JSON:', error);
      tokenRegistered = true;
      localStorage.setItem(TOKEN_REGISTERED_KEY, 'true');
      return {
        success: true, // Still return success as the server accepted the request
        message: 'Device registered but response could not be parsed'
      };
    }
  } catch (error) {
    console.error('[FCM] Error registering device token:', error);
    // Return an object instead of throwing to prevent app crashes
    return {
      success: false,
      message: error.message || 'Unknown error occurred',
      error
    };
  }
};

/**
 * Remove device token from backend and clear local cache
 * @returns {Promise<Object>} Status object
 */
const cleanup = async () => {
  try {
    if (!currentToken) {
      return { success: true, message: 'No token to clean up' };
    }

    const authToken = localStorage.getItem('token');

    // Attempt to remove token from backend if user is authenticated
    if (authToken) {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';
      try {
        await fetch(`${apiUrl}/auth/user/device-token`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          },
          body: JSON.stringify({ device_token: currentToken })
        });
      } catch (networkError) {
        console.warn('Failed to remove device token from backend:', networkError);
      }
    }

    // Clear local state and storage
    if (listenerRegistered && typeof onMessageUnsubscribe === 'function') {
      try {
        onMessageUnsubscribe();
      } catch (unsubError) {
        console.warn('Failed to unsubscribe message listener:', unsubError);
      }
    }
    onMessageUnsubscribe = null;
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(TOKEN_REGISTERED_KEY);
    currentToken = null;
    tokenRegistered = false;
    listenerRegistered = false;

    return { success: true };
  } catch (error) {
    console.error('Error during FCM cleanup:', error);
    // Still clear local state to prevent stale info
    if (listenerRegistered && typeof onMessageUnsubscribe === 'function') {
      try {
        onMessageUnsubscribe();
      } catch (_) {
        // ignore
      }
    }
    onMessageUnsubscribe = null;
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(TOKEN_REGISTERED_KEY);
    currentToken = null;
    tokenRegistered = false;
    listenerRegistered = false;
    return { success: false, error: error.message || 'Cleanup failed' };
  }
};

/**
 * Initialize Firebase messaging and register the device
 * @returns {Promise<Object>} Status object indicating success and token
 */
const initializeMessaging = async () => {
  try {
    // console.log('[FCM] Initializing Firebase messaging...');
    // Check if messaging is supported
    if (!isMessagingSupported()) {
      console.log('Firebase messaging is not supported in this browser or environment');
      return {
        success: false,
        message: 'Firebase messaging is not supported in this browser or environment'
      };
    }

    // If we already have a registered token, simply ensure the listener is set
    if (currentToken && tokenRegistered) {
      if (!listenerRegistered && messaging) {
        onMessageUnsubscribe = onMessage(messaging, processNotification);
        listenerRegistered = true;
        // console.log('[FCM] Listener registered for FCM messages.');
      }
      return { success: true, token: currentToken };
    }

    // Check if Firebase app is initialized
    if (!firebaseApp) {
      console.warn('Firebase app is not properly initialized');
      return {
        success: false,
        message: 'Firebase app is not properly initialized',
        configError: true
      };
    }

    // Request permission and get token with timeout
    let permissionResult;
    try {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Permission request timed out')), 10000)
      );

      const permissionPromise = requestNotificationPermission();

      permissionResult = await Promise.race([permissionPromise, timeoutPromise]);
    } catch (permissionError) {
      console.warn('Failed to get notification permission:', permissionError);
      return {
        success: false,
        message: 'Failed to get notification permission: ' + permissionError.message
      };
    }

    // If permission was denied or token couldn't be obtained
    if (!permissionResult) {
      console.log('Failed to get notification permission or token');
      return {
        success: false,
        message: 'Failed to get notification permission or token'
      };
    }

    // Register token with backend (only if we have a valid token)
    const registrationResult = await registerDeviceToken(permissionResult);

    // If registration failed but we still have a token, we can continue
    if (!registrationResult.success) {
      console.warn('[FCM] Token registration with backend failed, but notifications may still work:',
        registrationResult.message);

      return {
        success: true,
        token: permissionResult,
        warning: 'Token registered locally but not with backend',
        backendError: registrationResult.message
      };
    }
    console.log('[FCM] Firebase messaging initialized successfully.');
    return {
      success: true,
      token: permissionResult
    };
  } catch (error) {
    console.error('[FCM] Failed to initialize messaging:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
};

// Create and export the notification service
export const firebaseMessaging = {
  isSupported: isMessagingSupported,
  initialize: initializeMessaging,
  registerHandler: registerNotificationHandler,
  unregisterHandler: unregisterNotificationHandler,
  cleanup
};